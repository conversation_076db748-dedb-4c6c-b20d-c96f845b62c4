<?php
/**
 * Get Analytics Data
 * Provides statistics and analytics for form submissions and shares
 */

require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    $analytics = [];

    // Total submissions
    try {
        $sql = "SELECT COUNT(*) as total FROM form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $analytics['total_submissions'] = $result['total'] ?? 0;
    } catch (PDOException $e) {
        $analytics['total_submissions'] = 0;
    }

    // Monthly submissions
    try {
        $sql = "SELECT COUNT(*) as monthly 
                FROM form_submissions 
                WHERE MONTH(submitted_at) = MONTH(CURRENT_DATE()) 
                AND YEAR(submitted_at) = YEAR(CURRENT_DATE())";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $analytics['monthly_submissions'] = $result['monthly'] ?? 0;
    } catch (PDOException $e) {
        $analytics['monthly_submissions'] = 0;
    }

    // Active shares
    try {
        $sql = "SELECT COUNT(*) as active 
                FROM page_shares 
                WHERE is_active = 1 
                AND (expires_at IS NULL OR expires_at > NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $analytics['active_shares'] = $result['active'] ?? 0;
    } catch (PDOException $e) {
        $analytics['active_shares'] = 0;
    }

    // Total views
    try {
        $sql = "SELECT SUM(view_count) as total_views FROM page_shares WHERE is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $analytics['total_views'] = $result['total_views'] ?? 0;
    } catch (PDOException $e) {
        $analytics['total_views'] = 0;
    }

    // Daily submissions for the last 30 days
    try {
        $sql = "SELECT 
                    DATE(submitted_at) as date,
                    COUNT(*) as count
                FROM form_submissions 
                WHERE submitted_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
                GROUP BY DATE(submitted_at)
                ORDER BY date DESC";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $analytics['daily_submissions'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $analytics['daily_submissions'] = [];
    }

    // Top pages by submissions
    try {
        $sql = "SELECT 
                    p.title,
                    p.original_filename,
                    COUNT(fs.id) as submission_count
                FROM pages p
                LEFT JOIN form_submissions fs ON p.id = fs.page_id
                GROUP BY p.id
                HAVING submission_count > 0
                ORDER BY submission_count DESC
                LIMIT 10";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $analytics['top_pages'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $analytics['top_pages'] = [];
    }

    // Recent activity
    try {
        $sql = "SELECT 
                    'submission' as type,
                    fs.submitted_at as timestamp,
                    p.title as page_title,
                    p.original_filename as page_filename,
                    fs.ip_address
                FROM form_submissions fs
                LEFT JOIN pages p ON fs.page_id = p.id
                ORDER BY fs.submitted_at DESC
                LIMIT 10";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $analytics['recent_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $analytics['recent_activity'] = [];
    }

    echo json_encode([
        'success' => true,
        'analytics' => $analytics
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
