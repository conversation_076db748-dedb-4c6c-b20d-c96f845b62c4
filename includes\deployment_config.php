<?php
/**
 * Deployment Configuration Helper
 * Handles configuration for both local and online deployment
 */

class DeploymentConfig {
    private $db;
    private $settings = [];
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->loadSettings();
    }
    
    /**
     * Load settings from database
     */
    private function loadSettings() {
        try {
            $sql = "SELECT setting_key, setting_value, setting_type FROM app_settings";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($results as $setting) {
                $value = $setting['setting_value'];
                
                // Convert based on type
                switch ($setting['setting_type']) {
                    case 'boolean':
                        $value = (bool)$value;
                        break;
                    case 'integer':
                        $value = (int)$value;
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }
                
                $this->settings[$setting['setting_key']] = $value;
            }
        } catch (Exception $e) {
            // Use defaults if database not available
            $this->setDefaultSettings();
        }
    }
    
    /**
     * Set default settings
     */
    private function setDefaultSettings() {
        $this->settings = [
            'enable_form_submissions' => true,
            'auto_detect_index_pages' => true,
            'form_submission_notifications' => true,
            'online_deployment_mode' => false,
            'base_url' => '',
            'share_url_domain' => '',
            'max_file_size' => 10485760,
            'allowed_file_types' => 'html,htm,css,js,png,jpg,jpeg,gif,svg,woff,woff2,ttf,otf'
        ];
    }
    
    /**
     * Get setting value
     */
    public function get($key, $default = null) {
        return $this->settings[$key] ?? $default;
    }
    
    /**
     * Set setting value
     */
    public function set($key, $value) {
        $this->settings[$key] = $value;
        
        // Update in database
        try {
            $sql = "UPDATE app_settings SET setting_value = ? WHERE setting_key = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([is_bool($value) ? ($value ? '1' : '0') : $value, $key]);
        } catch (Exception $e) {
            // Ignore database errors for now
        }
    }
    
    /**
     * Check if running in online deployment mode
     */
    public function isOnlineMode() {
        return $this->get('online_deployment_mode', false);
    }
    
    /**
     * Get base URL for the application
     */
    public function getBaseUrl() {
        $baseUrl = $this->get('base_url', '');
        
        if (empty($baseUrl)) {
            // Auto-detect base URL
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $path = rtrim(dirname($_SERVER['SCRIPT_NAME'] ?? ''), '/');
            $baseUrl = $protocol . '://' . $host . $path;
        }
        
        return rtrim($baseUrl, '/');
    }
    
    /**
     * Get share URL domain
     */
    public function getShareUrlDomain() {
        $domain = $this->get('share_url_domain', '');
        
        if (empty($domain)) {
            return $this->getBaseUrl();
        }
        
        return rtrim($domain, '/');
    }
    
    /**
     * Generate share URL
     */
    public function generateShareUrl($shareToken) {
        $baseUrl = $this->getShareUrlDomain();
        return $baseUrl . '/view.php?token=' . $shareToken;
    }
    
    /**
     * Generate short URL
     */
    public function generateShortUrl($shortCode) {
        $baseUrl = $this->getShareUrlDomain();
        return $baseUrl . '/s/' . $shortCode;
    }
    
    /**
     * Get upload directory path
     */
    public function getUploadDir() {
        $uploadDir = __DIR__ . '/../uploads/';
        
        // Ensure directory exists
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        return $uploadDir;
    }
    
    /**
     * Get upload URL
     */
    public function getUploadUrl() {
        return $this->getBaseUrl() . '/uploads/';
    }
    
    /**
     * Check if form submissions are enabled
     */
    public function isFormSubmissionsEnabled() {
        return $this->get('enable_form_submissions', true);
    }
    
    /**
     * Check if index page auto-detection is enabled
     */
    public function isIndexDetectionEnabled() {
        return $this->get('auto_detect_index_pages', true);
    }
    
    /**
     * Get maximum file upload size
     */
    public function getMaxFileSize() {
        return $this->get('max_file_size', 10485760);
    }
    
    /**
     * Get allowed file types
     */
    public function getAllowedFileTypes() {
        $types = $this->get('allowed_file_types', 'html,htm,css,js,png,jpg,jpeg,gif,svg,woff,woff2,ttf,otf');
        return explode(',', $types);
    }
    
    /**
     * Configure for online deployment
     */
    public function configureForOnline($config) {
        $this->set('online_deployment_mode', true);
        
        if (isset($config['base_url'])) {
            $this->set('base_url', $config['base_url']);
        }
        
        if (isset($config['share_url_domain'])) {
            $this->set('share_url_domain', $config['share_url_domain']);
        }
        
        if (isset($config['database_host'])) {
            // Update database configuration
            $this->updateDatabaseConfig($config);
        }
    }
    
    /**
     * Update database configuration for online deployment
     */
    private function updateDatabaseConfig($config) {
        $configFile = __DIR__ . '/../config/database.php';
        
        if (file_exists($configFile)) {
            $content = file_get_contents($configFile);
            
            // Update database settings
            if (isset($config['database_host'])) {
                $content = preg_replace(
                    '/private \$host = "[^"]*";/',
                    'private $host = "' . $config['database_host'] . '";',
                    $content
                );
            }
            
            if (isset($config['database_name'])) {
                $content = preg_replace(
                    '/private \$db_name = "[^"]*";/',
                    'private $db_name = "' . $config['database_name'] . '";',
                    $content
                );
            }
            
            if (isset($config['database_user'])) {
                $content = preg_replace(
                    '/private \$username = "[^"]*";/',
                    'private $username = "' . $config['database_user'] . '";',
                    $content
                );
            }
            
            if (isset($config['database_password'])) {
                $content = preg_replace(
                    '/private \$password = "[^"]*";/',
                    'private $password = "' . $config['database_password'] . '";',
                    $content
                );
            }
            
            file_put_contents($configFile, $content);
        }
    }
    
    /**
     * Get deployment status
     */
    public function getDeploymentStatus() {
        return [
            'is_online' => $this->isOnlineMode(),
            'base_url' => $this->getBaseUrl(),
            'share_url_domain' => $this->getShareUrlDomain(),
            'form_submissions_enabled' => $this->isFormSubmissionsEnabled(),
            'index_detection_enabled' => $this->isIndexDetectionEnabled(),
            'max_file_size' => $this->getMaxFileSize(),
            'allowed_file_types' => $this->getAllowedFileTypes(),
            'upload_dir_writable' => is_writable($this->getUploadDir()),
            'database_connected' => $this->db !== null
        ];
    }
    
    /**
     * Test deployment configuration
     */
    public function testDeployment() {
        $tests = [];
        
        // Test database connection
        $tests['database'] = [
            'name' => 'Database Connection',
            'status' => $this->db !== null,
            'message' => $this->db ? 'Connected' : 'Failed to connect'
        ];
        
        // Test upload directory
        $uploadDir = $this->getUploadDir();
        $tests['upload_dir'] = [
            'name' => 'Upload Directory',
            'status' => is_dir($uploadDir) && is_writable($uploadDir),
            'message' => is_dir($uploadDir) ? 
                (is_writable($uploadDir) ? 'Writable' : 'Not writable') : 
                'Does not exist'
        ];
        
        // Test form submission endpoint
        $tests['form_endpoint'] = [
            'name' => 'Form Submission Endpoint',
            'status' => file_exists(__DIR__ . '/../submit_form.php'),
            'message' => file_exists(__DIR__ . '/../submit_form.php') ? 'Available' : 'Missing'
        ];
        
        // Test share viewing
        $tests['share_viewer'] = [
            'name' => 'Share Viewer',
            'status' => file_exists(__DIR__ . '/../view.php'),
            'message' => file_exists(__DIR__ . '/../view.php') ? 'Available' : 'Missing'
        ];
        
        // Test URL generation
        $baseUrl = $this->getBaseUrl();
        $tests['url_generation'] = [
            'name' => 'URL Generation',
            'status' => !empty($baseUrl) && filter_var($baseUrl, FILTER_VALIDATE_URL),
            'message' => !empty($baseUrl) ? "Base URL: $baseUrl" : 'Invalid base URL'
        ];
        
        return $tests;
    }
}

// Global deployment config instance
$deploymentConfig = new DeploymentConfig();
?>
