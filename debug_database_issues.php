<?php
/**
 * Debug Database Issues
 * Check what's wrong with the enhanced database structure
 */

require_once 'config/database.php';

echo "<h1>Database Issues Debug</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Check current form_submissions table structure
    echo "<h2>Current form_submissions Table Structure</h2>";
    
    try {
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for missing columns
        $presentColumns = array_column($columns, 'Field');
        $expectedColumns = [
            'id', 'page_id', 'form_id', 'share_id', 'form_name', 'submission_data',
            'visitor_session', 'ip_address', 'user_agent', 'referrer',
            'country_code', 'city', 'browser_name', 'browser_version', 
            'os_name', 'device_type', 'submission_source', 'status', 
            'priority', 'tags', 'notes', 'processed_at', 'processed_by',
            'response_sent', 'response_sent_at', 'submitted_at'
        ];
        
        echo "<h3>Missing Columns Check:</h3>";
        $missingColumns = [];
        foreach ($expectedColumns as $expected) {
            if (!in_array($expected, $presentColumns)) {
                $missingColumns[] = $expected;
            }
        }
        
        if (empty($missingColumns)) {
            echo "<p>✅ All expected columns are present</p>";
        } else {
            echo "<p>❌ Missing columns: " . implode(', ', $missingColumns) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error checking form_submissions table: " . $e->getMessage() . "</p>";
    }
    
    // Check zip_extractions table
    echo "<h2>ZIP Extractions Table Check</h2>";
    
    try {
        $sql = "DESCRIBE zip_extractions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $zipColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>✅ zip_extractions table exists</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        foreach ($zipColumns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p>❌ zip_extractions table missing: " . $e->getMessage() . "</p>";
    }
    
    // Test form submission insertion
    echo "<h2>Form Submission Insertion Test</h2>";
    
    try {
        // Try to insert a test record with basic fields first
        $sql = "INSERT INTO form_submissions (page_id, submission_data, ip_address, submitted_at) 
                VALUES (?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([1, '{"test": "data"}', '127.0.0.1']);
        
        $testId = $db->lastInsertId();
        echo "<p>✅ Basic insertion successful with ID: $testId</p>";
        
        // Clean up test record
        $sql = "DELETE FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testId]);
        
    } catch (Exception $e) {
        echo "<p>❌ Basic insertion failed: " . $e->getMessage() . "</p>";
    }
    
    // Test enhanced insertion
    echo "<h2>Enhanced Insertion Test</h2>";
    
    try {
        // Check which columns actually exist before trying enhanced insertion
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $actualColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
        
        // Build insertion query with only existing columns
        $insertColumns = [];
        $insertValues = [];
        $insertData = [];
        
        $testData = [
            'page_id' => 1,
            'form_id' => null,
            'share_id' => null,
            'form_name' => 'test_form',
            'submission_data' => '{"name": "Test", "email": "<EMAIL>"}',
            'visitor_session' => 'test_session',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'referrer' => 'http://test.com',
            'country_code' => 'US',
            'city' => 'Test City',
            'browser_name' => 'Chrome',
            'browser_version' => '91.0',
            'os_name' => 'Windows',
            'device_type' => 'desktop',
            'submission_source' => 'shared',
            'status' => 'pending',
            'priority' => 'normal'
        ];
        
        foreach ($testData as $column => $value) {
            if (in_array($column, $actualColumns)) {
                $insertColumns[] = $column;
                $insertValues[] = '?';
                $insertData[] = $value;
            } else {
                echo "<p>⚠️ Column '$column' not found in table</p>";
            }
        }
        
        if (!empty($insertColumns)) {
            $sql = "INSERT INTO form_submissions (" . implode(', ', $insertColumns) . ", submitted_at) 
                    VALUES (" . implode(', ', $insertValues) . ", NOW())";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($insertData);
            
            $enhancedId = $db->lastInsertId();
            echo "<p>✅ Enhanced insertion successful with ID: $enhancedId</p>";
            
            // Show what was actually inserted
            $sql = "SELECT * FROM form_submissions WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$enhancedId]);
            $inserted = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
            echo "<h4>Inserted Data:</h4>";
            foreach ($inserted as $key => $value) {
                if ($value !== null) {
                    echo "<p><strong>$key:</strong> $value</p>";
                }
            }
            echo "</div>";
            
            // Clean up test record
            $sql = "DELETE FROM form_submissions WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$enhancedId]);
            
        } else {
            echo "<p>❌ No valid columns found for enhanced insertion</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Enhanced insertion failed: " . $e->getMessage() . "</p>";
    }
    
    // Check page_shares table
    echo "<h2>Page Shares Table Check</h2>";
    
    try {
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $shareColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $presentShareColumns = array_column($shareColumns, 'Field');
        
        if (in_array('page_id', $presentShareColumns)) {
            echo "<p>✅ page_shares table has page_id column</p>";
        } else {
            echo "<p>❌ page_shares table missing page_id column</p>";
        }
        
        // Test the problematic query
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.is_active = 1 
                LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $testShare = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testShare) {
            echo "<p>✅ Share query works correctly</p>";
        } else {
            echo "<p>⚠️ No active shares found (this is normal if no shares exist)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Page shares query failed: " . $e->getMessage() . "</p>";
    }
    
    // Show all tables
    echo "<h2>All Database Tables</h2>";
    
    $sql = "SHOW TABLES";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>Tables in database:</strong></p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Recommended Fixes</h2>";
echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px;'>";
echo "<h4>If issues were found:</h4>";
echo "<ol>";
echo "<li><strong>Missing Columns:</strong> Run the database update script to add missing columns</li>";
echo "<li><strong>Table Structure:</strong> The form_submissions table may need to be recreated</li>";
echo "<li><strong>Foreign Keys:</strong> Check if foreign key constraints are causing issues</li>";
echo "<li><strong>Data Types:</strong> Ensure all column types are compatible</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='fix_enhanced_database.php'>Fix Enhanced Database</a> (if this file exists)</li>";
echo "<li><a href='test_form_submission.php'>Original Form Test</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3, h4 { color: #333; }
</style>
