<?php
/**
 * Add status column to pages table if it doesn't exist
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // Check if status column exists
    $sql = "SHOW COLUMNS FROM pages LIKE 'status'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();

    if (!$result) {
        // Add status column
        $sql = "ALTER TABLE pages ADD COLUMN status ENUM('active', 'archived', 'deleted') DEFAULT 'active' AFTER file_hash";
        $db->exec($sql);
        echo "Status column added successfully.\n";
    } else {
        echo "Status column already exists.\n";
    }

    // Check if updated_at column exists
    $sql = "SHOW COLUMNS FROM pages LIKE 'updated_at'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();

    if (!$result) {
        // Add updated_at column
        $sql = "ALTER TABLE pages ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at";
        $db->exec($sql);
        echo "Updated_at column added successfully.\n";
    } else {
        echo "Updated_at column already exists.\n";
    }

    echo "Database migration completed successfully.\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
