<?php
/**
 * Step by Step Fix
 * Methodical approach to fix all issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Step by Step System Fix</h1>";

$issues = [];
$fixes = [];

echo "<h2>Step 1: Database Connection</h2>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p>✅ Database connection successful</p>";
    } else {
        $issues[] = "Database connection failed";
        echo "<p>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    $issues[] = "Database class error: " . $e->getMessage();
    echo "<p>❌ Database class error: " . $e->getMessage() . "</p>";
}

if (!isset($db) || !$db) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Cannot proceed without database connection</h3>";
    echo "<p>Please check your database configuration in config/database.php</p>";
    echo "</div>";
    exit;
}

echo "<h2>Step 2: Check Required Tables</h2>";

$requiredTables = ['pages', 'page_shares', 'form_submissions'];
$missingTables = [];

foreach ($requiredTables as $table) {
    try {
        $sql = "SELECT 1 FROM $table LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        echo "<p>✅ Table '$table' exists</p>";
    } catch (Exception $e) {
        $missingTables[] = $table;
        echo "<p>❌ Table '$table' missing or inaccessible</p>";
    }
}

if (!empty($missingTables)) {
    echo "<h3>Creating Missing Tables</h3>";
    
    try {
        // Create tables using the Database class method
        $result = $database->createTables();
        if ($result) {
            echo "<p>✅ Tables created successfully</p>";
            $fixes[] = "Created missing tables";
        } else {
            echo "<p>❌ Failed to create tables</p>";
            $issues[] = "Could not create required tables";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error creating tables: " . $e->getMessage() . "</p>";
        $issues[] = "Table creation failed: " . $e->getMessage();
    }
}

echo "<h2>Step 3: Fix page_shares Table</h2>";

try {
    $sql = "DESCRIBE page_shares";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    
    if (in_array('page_id', $columns)) {
        echo "<p>✅ page_shares table has page_id column</p>";
    } else {
        echo "<p>❌ page_shares table missing page_id column</p>";
        
        // Add the missing column
        try {
            $sql = "ALTER TABLE page_shares ADD COLUMN page_id INT NOT NULL AFTER id";
            $db->exec($sql);
            echo "<p>✅ Added page_id column to page_shares</p>";
            $fixes[] = "Added page_id column to page_shares";
        } catch (Exception $e) {
            echo "<p>❌ Failed to add page_id column: " . $e->getMessage() . "</p>";
            $issues[] = "Cannot add page_id column";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Cannot check page_shares table: " . $e->getMessage() . "</p>";
    $issues[] = "page_shares table inaccessible";
}

echo "<h2>Step 4: Test Basic Form Submission</h2>";

try {
    // Test basic form submission
    $sql = "INSERT INTO form_submissions (page_id, submission_data, ip_address, submitted_at) 
            VALUES (?, ?, ?, NOW())";
    $stmt = $db->prepare($sql);
    $stmt->execute([1, '{"test": "basic"}', '127.0.0.1']);
    
    $testId = $db->lastInsertId();
    echo "<p>✅ Basic form submission works (ID: $testId)</p>";
    
    // Clean up
    $sql = "DELETE FROM form_submissions WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$testId]);
    
} catch (Exception $e) {
    echo "<p>❌ Basic form submission failed: " . $e->getMessage() . "</p>";
    $issues[] = "Basic form submission broken";
    
    // Try to fix the form_submissions table
    echo "<h3>Attempting to Fix form_submissions Table</h3>";
    
    try {
        $sql = "DROP TABLE IF EXISTS form_submissions";
        $db->exec($sql);
        
        $sql = "CREATE TABLE form_submissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            form_id INT NULL,
            share_id INT NULL,
            submission_data JSON NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            referrer VARCHAR(500),
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_page_id (page_id),
            INDEX idx_submitted_at (submitted_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ Recreated form_submissions table</p>";
        $fixes[] = "Recreated form_submissions table";
        
        // Test again
        $sql = "INSERT INTO form_submissions (page_id, submission_data, ip_address, submitted_at) 
                VALUES (?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([1, '{"test": "fixed"}', '127.0.0.1']);
        
        $testId = $db->lastInsertId();
        echo "<p>✅ Form submission now works (ID: $testId)</p>";
        
        $sql = "DELETE FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testId]);
        
    } catch (Exception $e) {
        echo "<p>❌ Cannot fix form_submissions table: " . $e->getMessage() . "</p>";
        $issues[] = "Cannot recreate form_submissions table";
    }
}

echo "<h2>Step 5: Test Share System</h2>";

try {
    // Test the share query that was failing
    $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.is_active = 1 
            LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $share = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>✅ Share query works correctly</p>";
    
    if (!$share) {
        echo "<p>⚠️ No shares found - creating test share</p>";
        
        // Get a page to share
        $sql = "SELECT id FROM pages LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $page = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($page) {
            $shareToken = bin2hex(random_bytes(16));
            $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
            
            $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
                    VALUES (?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$page['id'], $shareToken, $shortCode, 'Test Share']);
            
            echo "<p>✅ Test share created</p>";
            $fixes[] = "Created test share";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Share system error: " . $e->getMessage() . "</p>";
    $issues[] = "Share system broken";
}

echo "<h2>Step 6: Test Form Submission Handler</h2>";

if (file_exists('submit_form.php')) {
    echo "<p>✅ submit_form.php exists</p>";
    
    // Test if it loads without errors
    try {
        $originalMethod = $_SERVER['REQUEST_METHOD'] ?? '';
        $_SERVER['REQUEST_METHOD'] = 'GET'; // Prevent actual processing
        
        ob_start();
        include 'submit_form.php';
        $output = ob_get_clean();
        
        $_SERVER['REQUEST_METHOD'] = $originalMethod;
        
        echo "<p>✅ submit_form.php loads without syntax errors</p>";
    } catch (Exception $e) {
        echo "<p>❌ submit_form.php has errors: " . $e->getMessage() . "</p>";
        $issues[] = "submit_form.php has syntax errors";
    }
} else {
    echo "<p>❌ submit_form.php missing</p>";
    $issues[] = "submit_form.php missing";
}

echo "<h2>Step 7: Summary</h2>";

if (empty($issues)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 5px;'>";
    echo "<h3>🎉 All Issues Fixed!</h3>";
    echo "<p>The system is now working correctly.</p>";
    if (!empty($fixes)) {
        echo "<h4>Fixes Applied:</h4>";
        echo "<ul>";
        foreach ($fixes as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Issues Remaining:</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    
    if (!empty($fixes)) {
        echo "<h4>Fixes Applied:</h4>";
        echo "<ul>";
        foreach ($fixes as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><a href='test_form_submission.php'>Test Form Submission System</a></li>";
echo "<li><a href='simple_diagnostic.php'>Run Simple Diagnostic</a></li>";
echo "<li><a href='index.html'>Try Main Application</a></li>";
echo "</ol>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
</style>
