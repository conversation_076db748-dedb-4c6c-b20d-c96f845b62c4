<?php
/**
 * Test Complete Form Collection Flow
 * End-to-end test of form data collection from shared pages
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Test Complete Form Collection Flow</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>1. Create Test Page with Form</h2>";
    
    // Create test HTML page with form
    if (!is_dir('uploads/pages')) {
        mkdir('uploads/pages', 0755, true);
    }
    
    $testHtml = '<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Test</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a8b; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin-top: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin-top: 20px; }
    </style>
</head>
<body>
    <h1>Contact Us</h1>
    <p>Please fill out the form below and we will get back to you soon.</p>
    
    <form name="contact_form" method="POST">
        <div class="form-group">
            <label for="name">Full Name *</label>
            <input type="text" id="name" name="name" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email Address *</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="phone">Phone Number</label>
            <input type="tel" id="phone" name="phone">
        </div>
        
        <div class="form-group">
            <label for="subject">Subject *</label>
            <input type="text" id="subject" name="subject" required>
        </div>
        
        <div class="form-group">
            <label for="message">Message *</label>
            <textarea id="message" name="message" rows="5" required></textarea>
        </div>
        
        <div class="form-group">
            <button type="submit">Send Message</button>
        </div>
    </form>
    
    <div id="form-result"></div>
</body>
</html>';
    
    $testFile = 'uploads/pages/contact_form_test_' . time() . '.html';
    file_put_contents($testFile, $testHtml);
    
    // Store page in database
    $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash, has_forms) 
            VALUES (?, ?, ?, ?, ?, ?, 1)";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        basename($testFile),
        'contact_form_test.html',
        'Contact Form Test Page',
        $testFile,
        strlen($testHtml),
        hash('sha256', $testHtml)
    ]);
    
    $pageId = $db->lastInsertId();
    echo "<p>✅ Test page created with ID: $pageId</p>";
    
    echo "<h2>2. Create Share for Test Page</h2>";
    
    $shareToken = bin2hex(random_bytes(16));
    $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
    
    $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, description, show_forms, is_active) 
            VALUES (?, ?, ?, ?, ?, 1, 1)";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        $pageId, 
        $shareToken, 
        $shortCode, 
        'Contact Form Test Share',
        'Test share for form data collection verification'
    ]);
    
    $shareId = $db->lastInsertId();
    echo "<p>✅ Share created with ID: $shareId</p>";
    
    // Generate share URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = rtrim(dirname($_SERVER['SCRIPT_NAME'] ?? ''), '/');
    $baseUrl = $protocol . '://' . $host . $path;
    $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
    
    echo "<div style='background: #e7f3ff; color: #004085; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔗 Test Share URL Created</h4>";
    echo "<p><strong>Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
    echo "<p><strong>Short Code:</strong> $shortCode</p>";
    echo "<p><strong>Share Token:</strong> $shareToken</p>";
    echo "</div>";
    
    echo "<h2>3. Test Form Submission Simulation</h2>";
    
    // Simulate form submission
    $testFormData = [
        '_share_token' => $shareToken,
        '_page_id' => $pageId,
        '_form_name' => 'contact_form',
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'phone' => '******-123-4567',
        'subject' => 'Test Form Submission',
        'message' => 'This is a test message to verify that the form data collection system is working correctly. The form was submitted from a shared page and should be stored in the database.'
    ];
    
    // Backup original values
    $originalPost = $_POST;
    $originalMethod = $_SERVER['REQUEST_METHOD'] ?? '';
    $originalRemoteAddr = $_SERVER['REMOTE_ADDR'] ?? '';
    $originalUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $originalReferer = $_SERVER['HTTP_REFERER'] ?? '';
    
    // Set up simulation environment
    $_POST = $testFormData;
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REMOTE_ADDR'] = '192.168.1.100';
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    $_SERVER['HTTP_REFERER'] = $shareUrl;
    
    echo "<p>Simulating form submission...</p>";
    
    try {
        ob_start();
        include 'submit_form.php';
        $response = ob_get_clean();
        
        echo "<p>✅ Form submission processed</p>";
        echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        echo "<h4>Response from submit_form.php:</h4>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        echo "</div>";
        
        // Parse JSON response
        $responseData = json_decode($response, true);
        if ($responseData && isset($responseData['success']) && $responseData['success']) {
            echo "<p>✅ Form submission successful according to response</p>";
            $submissionId = $responseData['submission_id'] ?? null;
            if ($submissionId) {
                echo "<p><strong>Submission ID:</strong> $submissionId</p>";
            }
        } else {
            echo "<p>❌ Form submission failed according to response</p>";
            if ($responseData && isset($responseData['message'])) {
                echo "<p><strong>Error:</strong> {$responseData['message']}</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error during form submission: " . $e->getMessage() . "</p>";
    }
    
    // Restore original values
    $_POST = $originalPost;
    $_SERVER['REQUEST_METHOD'] = $originalMethod;
    $_SERVER['REMOTE_ADDR'] = $originalRemoteAddr;
    $_SERVER['HTTP_USER_AGENT'] = $originalUserAgent;
    $_SERVER['HTTP_REFERER'] = $originalReferer;
    
    echo "<h2>4. Verify Data Storage</h2>";
    
    // Check if submission was stored
    $sql = "SELECT * FROM form_submissions WHERE page_id = ? ORDER BY id DESC LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $submission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($submission) {
        echo "<p>✅ Form submission found in database</p>";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📊 Stored Submission Data:</h4>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        
        foreach ($submission as $key => $value) {
            if ($value !== null && $value !== '') {
                if ($key === 'submission_data' || $key === 'form_data') {
                    $decoded = json_decode($value, true);
                    if ($decoded) {
                        echo "<tr><td><strong>$key</strong></td><td>";
                        foreach ($decoded as $formField => $formValue) {
                            echo "<strong>$formField:</strong> " . htmlspecialchars($formValue) . "<br>";
                        }
                        echo "</td></tr>";
                    } else {
                        echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                    }
                } else {
                    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                }
            }
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<p>❌ No form submission found in database</p>";
        
        // Check if there are any submissions at all
        $sql = "SELECT COUNT(*) as total FROM form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<p><strong>Total submissions in database:</strong> $total</p>";
    }
    
    echo "<h2>5. Test Database Tab Integration</h2>";
    
    // Count submissions by page
    $sql = "SELECT 
                p.title as page_title,
                COUNT(fs.id) as submission_count,
                MAX(fs.submitted_at) as latest_submission
            FROM pages p
            LEFT JOIN form_submissions fs ON p.id = fs.page_id
            GROUP BY p.id, p.title
            HAVING submission_count > 0
            ORDER BY submission_count DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pageStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($pageStats)) {
        echo "<p>✅ Form submissions are being tracked by page</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Page</th><th>Submissions</th><th>Latest</th></tr>";
        foreach ($pageStats as $stat) {
            echo "<tr>";
            echo "<td>{$stat['page_title']}</td>";
            echo "<td>{$stat['submission_count']}</td>";
            echo "<td>{$stat['latest_submission']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>6. Summary</h2>";
    
    $sql = "SELECT COUNT(*) as total FROM form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $totalSubmissions = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    if ($totalSubmissions > 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 5px;'>";
        echo "<h3>🎉 Form Collection System Working!</h3>";
        echo "<ul>";
        echo "<li>✅ Test page created with working contact form</li>";
        echo "<li>✅ Share created and accessible</li>";
        echo "<li>✅ Form submission processed successfully</li>";
        echo "<li>✅ Data stored in database ($totalSubmissions total submissions)</li>";
        echo "<li>✅ Data viewable in database tab</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px;'>";
        echo "<h3>❌ Form Collection System Issues</h3>";
        echo "<p>Form submissions are not being stored properly. Check the error messages above.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px;'>";
    echo "<h2>❌ Test Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Visit the share URL:</strong> Click the test share URL above and submit the form manually</li>";
echo "<li><strong>Check main application:</strong> <a href='index.html'>Go to main app</a> and check the database tab</li>";
echo "<li><strong>Create your own shares:</strong> Upload your pages and create shares with forms</li>";
echo "<li><strong>Monitor form data:</strong> All form submissions should now be collected and viewable</li>";
echo "</ol>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
h1, h2, h3, h4 { color: #333; }
</style>
