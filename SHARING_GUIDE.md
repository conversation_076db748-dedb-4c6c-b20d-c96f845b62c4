# Page Sharing & URL Shortening Guide

This guide explains how to use the enhanced page viewing and sharing features in Webpage Manager v2.0.

## Overview

The Webpage Manager now includes comprehensive page sharing and URL shortening capabilities that allow you to:

- **View imported pages** in their original format with proper asset loading
- **Share pages with friends** through secure, customizable links
- **Create shortened URLs** for easy sharing and tracking
- **Control access** with passwords, expiration dates, and view limits
- **Track analytics** for shared content

## Features

### 🔍 **Page Viewing**
- View imported HTML pages with all assets (CSS, JS, images) properly loaded
- Maintain original page functionality and styling
- Support for all file types (fonts, documents, etc.)
- Responsive viewing on all devices

### 🔗 **Page Sharing**
- **Secure sharing** with unique tokens and short codes
- **Password protection** for sensitive content
- **Expiration dates** for time-limited access
- **View limits** to control access count
- **Form visibility control** - show or hide forms in shared pages
- **Download permissions** - allow or restrict file downloads
- **Metadata display** - show or hide sharing information

### ⚡ **URL Shortening**
- Create short, memorable URLs for any web address
- Track click statistics and analytics
- Set expiration dates for temporary links
- Custom titles and descriptions

### 📊 **Analytics & Tracking**
- View count tracking for all shared pages
- Access logs with IP addresses and timestamps
- Geographic information (when available)
- Referrer tracking
- User agent information

## How to Use

### 1. Viewing Pages

**From the Manage Pages tab:**
1. Click the eye icon (👁️) on any page card
2. The page opens in a new tab with all assets properly loaded
3. All forms and functionality work as in the original page

**Direct URL access:**
- Use `view.php?id=PAGE_ID` to view any page directly

### 2. Creating Shares

**Quick Share from Page Card:**
1. Go to the **Manage Pages** tab
2. Click the share icon (🔗) on any page card
3. The sharing modal opens with the page pre-selected

**Manual Share Creation:**
1. Go to the **Sharing** tab
2. Click **"Create New Share"**
3. Configure your sharing options:

**Basic Settings:**
- **Page**: Select which page to share
- **Title**: Custom title for the shared page
- **Description**: Optional description for social media

**Security Settings:**
- **Password**: Optional password protection
- **Expires At**: Set expiration date/time
- **Max Views**: Limit number of views

**Display Options:**
- **Show Forms**: Include forms in shared page
- **Allow Download**: Enable file downloads
- **Show Metadata**: Display sharing information banner

4. Click **"Create Share"** to generate the links

### 3. Managing Shares

**View All Shares:**
- Go to the **Sharing** tab to see all active shares
- Each share card shows:
  - Title and status badges
  - View count and limits
  - Expiration information
  - Both full and short URLs

**Share Actions:**
- **👁️ View**: Open the shared page
- **✏️ Edit**: Modify share settings (coming soon)
- **📊 Analytics**: View detailed statistics (coming soon)
- **🗑️ Delete**: Deactivate the share

**Copy URLs:**
- Click any URL field to select all text
- Use the copy button (📋) for one-click copying
- Both full and short URLs are provided

### 4. URL Shortening

**Create Short URLs:**
1. Go to the **Sharing** tab
2. Click **"Shorten URL"**
3. Enter the original URL
4. Add optional title and description
5. Set expiration date if needed
6. Click **"Create Short URL"**

**Use Cases:**
- Shorten long external URLs
- Create memorable links for marketing
- Track clicks on shared content
- Temporary links for events or promotions

### 5. Accessing Shared Pages

**For Your Friends:**
1. Share the provided URL (full or short version)
2. Friends can access without any account or login
3. Password-protected pages will show a password prompt
4. Expired or limit-reached pages show appropriate messages

**URL Formats:**
- **Full Share URL**: `yoursite.com/view.php?token=SHARE_TOKEN`
- **Short URL**: `yoursite.com/view.php?s=SHORT_CODE`

## Security Features

### 🔒 **Password Protection**
- Secure password hashing (bcrypt)
- Session-based authentication
- No password storage in URLs

### ⏰ **Expiration Control**
- Automatic expiration based on date/time
- View count limits
- Immediate deactivation when limits reached

### 🛡️ **Access Control**
- Unique, unguessable tokens
- IP address logging
- User agent tracking
- Referrer information

### 🔍 **Privacy Options**
- Hide forms to prevent submissions
- Disable downloads for sensitive content
- Control metadata visibility

## Advanced Features

### 📱 **Mobile Optimization**
- Responsive design for all devices
- Touch-friendly interface
- Optimized loading for mobile networks

### 🌐 **Server Compatibility**
- Works on shared hosting
- Compatible with Apache and Nginx
- Supports both HTTP and HTTPS
- Database-driven for reliability

### 🔧 **Customization**
- Configurable through app settings
- Custom branding options
- Flexible URL structures
- Extensible architecture

## Best Practices

### 🎯 **For Sharing**
1. **Use descriptive titles** for better organization
2. **Set appropriate expiration dates** for security
3. **Use passwords for sensitive content**
4. **Monitor view counts** to track engagement
5. **Disable forms** if you don't want submissions

### 🔗 **For URL Shortening**
1. **Add titles and descriptions** for better tracking
2. **Use expiration dates** for temporary campaigns
3. **Monitor click statistics** for analytics
4. **Keep URLs organized** with meaningful titles

### 🔒 **For Security**
1. **Use strong passwords** for protected shares
2. **Set reasonable view limits** to prevent abuse
3. **Monitor access logs** for suspicious activity
4. **Expire old shares** regularly
5. **Review sharing permissions** periodically

## Troubleshooting

### Common Issues

**Page Not Loading Properly:**
- Check if all associated files were uploaded
- Verify file paths in the original HTML
- Ensure proper file permissions

**Share Not Working:**
- Check if share is still active
- Verify expiration date hasn't passed
- Confirm view limit hasn't been reached

**Assets Not Loading:**
- Ensure CSS/JS files were uploaded with the page
- Check file associations in the database
- Verify asset server is working

**Password Issues:**
- Clear browser cache and cookies
- Try incognito/private browsing mode
- Verify password was entered correctly

### Server Requirements

**For Online Hosting:**
- PHP 7.4 or higher
- MySQL 5.7 or higher
- mod_rewrite enabled (for clean URLs)
- Sufficient storage for uploaded files
- HTTPS recommended for security

**Configuration:**
- Update `getBaseUrl()` method for your domain
- Configure database connection for production
- Set appropriate file permissions
- Enable error logging for debugging

## API Reference

### Sharing Manager Endpoints

**Create Share:**
```
POST /includes/sharing_manager.php
action=create_share&page_id=ID&options=JSON
```

**Get Shares:**
```
GET /includes/sharing_manager.php?action=get_shares
```

**Update Share:**
```
POST /includes/sharing_manager.php
action=update_share&share_id=ID&updates=JSON
```

**Delete Share:**
```
POST /includes/sharing_manager.php
action=delete_share&share_id=ID
```

**Create Short URL:**
```
POST /includes/sharing_manager.php
action=create_short_url&original_url=URL&options=JSON
```

### View Endpoints

**View Page:**
```
GET /view.php?id=PAGE_ID
GET /view.php?token=SHARE_TOKEN
GET /view.php?s=SHORT_CODE
```

**Serve Assets:**
```
GET /assets.php?file=FILENAME
```

This comprehensive sharing system transforms your Webpage Manager into a powerful tool for collaboration and content distribution!
