<?php
// Debug Upload Test Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Upload Debug Test</h1>";

// Check PHP configuration
echo "<h2>PHP Configuration</h2>";
echo "<p>File uploads enabled: " . (ini_get('file_uploads') ? 'Yes' : 'No') . "</p>";
echo "<p>Upload max filesize: " . ini_get('upload_max_filesize') . "</p>";
echo "<p>Post max size: " . ini_get('post_max_size') . "</p>";
echo "<p>Max file uploads: " . ini_get('max_file_uploads') . "</p>";
echo "<p>Memory limit: " . ini_get('memory_limit') . "</p>";

// Check directories
echo "<h2>Directory Check</h2>";
$uploadDir = __DIR__ . DIRECTORY_SEPARATOR . 'uploads';
echo "<p>Upload directory: $uploadDir</p>";
echo "<p>Directory exists: " . (is_dir($uploadDir) ? 'Yes' : 'No') . "</p>";
echo "<p>Directory writable: " . (is_writable($uploadDir) ? 'Yes' : 'No') . "</p>";

$pagesDir = $uploadDir . DIRECTORY_SEPARATOR . 'pages';
echo "<p>Pages directory: $pagesDir</p>";
echo "<p>Pages directory exists: " . (is_dir($pagesDir) ? 'Yes' : 'No') . "</p>";
echo "<p>Pages directory writable: " . (is_writable($pagesDir) ? 'Yes' : 'No') . "</p>";

$assetsDir = $uploadDir . DIRECTORY_SEPARATOR . 'assets';
echo "<p>Assets directory: $assetsDir</p>";
echo "<p>Assets directory exists: " . (is_dir($assetsDir) ? 'Yes' : 'No') . "</p>";
echo "<p>Assets directory writable: " . (is_writable($assetsDir) ? 'Yes' : 'No') . "</p>";

// Create directories if they don't exist
if (!is_dir($uploadDir)) {
    if (mkdir($uploadDir, 0755, true)) {
        echo "<p style='color: green;'>Created uploads directory</p>";
    } else {
        echo "<p style='color: red;'>Failed to create uploads directory</p>";
    }
}

if (!is_dir($pagesDir)) {
    if (mkdir($pagesDir, 0755, true)) {
        echo "<p style='color: green;'>Created pages directory</p>";
    } else {
        echo "<p style='color: red;'>Failed to create pages directory</p>";
    }
}

if (!is_dir($assetsDir)) {
    if (mkdir($assetsDir, 0755, true)) {
        echo "<p style='color: green;'>Created assets directory</p>";
    } else {
        echo "<p style='color: red;'>Failed to create assets directory</p>";
    }
}

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $conn = $database->getConnection();
    if ($conn) {
        echo "<p style='color: green;'>Database connection successful</p>";
    } else {
        echo "<p style='color: red;'>Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

// Simple upload test form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<h2>Upload Test Result</h2>";
    
    $file = $_FILES['test_file'];
    echo "<pre>";
    print_r($file);
    echo "</pre>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $targetPath = $uploadDir . DIRECTORY_SEPARATOR . 'test_' . time() . '_' . $file['name'];
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            echo "<p style='color: green;'>File uploaded successfully to: $targetPath</p>";
            echo "<p>File size: " . filesize($targetPath) . " bytes</p>";
        } else {
            echo "<p style='color: red;'>Failed to move uploaded file</p>";
        }
    } else {
        echo "<p style='color: red;'>Upload error: " . $file['error'] . "</p>";
    }
}

// Test the actual upload API
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_api'])) {
    echo "<h2>API Test Result</h2>";
    
    // Simulate API call
    $_FILES['files'] = $_FILES['test_file'];
    
    try {
        require_once 'includes/upload.php';
        $uploader = new FileUploader();
        $result = $uploader->handleUpload();
        
        echo "<pre>";
        print_r($result);
        echo "</pre>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>API Error: " . $e->getMessage() . "</p>";
    }
}
?>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
h1, h2 { color: #333; }
p { margin: 5px 0; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>

<h2>Upload Test Form</h2>
<form method="POST" enctype="multipart/form-data">
    <p>
        <label>Select a file to test upload:</label><br>
        <input type="file" name="test_file" accept=".html,.htm,.css,.js,.png,.jpg,.gif">
    </p>
    <p>
        <button type="submit">Test Direct Upload</button>
        <button type="submit" name="test_api" value="1">Test API Upload</button>
    </p>
</form>

<h2>Manual Test</h2>
<p>You can also test the upload by:</p>
<ol>
    <li>Open the main application (index.html)</li>
    <li>Open browser developer tools (F12)</li>
    <li>Go to Console tab</li>
    <li>Try uploading a file and check for JavaScript errors</li>
    <li>Go to Network tab and check the upload request</li>
</ol>
