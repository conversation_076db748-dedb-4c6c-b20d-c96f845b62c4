# Form Editing Guide - Webpage Manager

This guide explains how to view and modify form attributes in imported pages using the enhanced Webpage Manager interface.

## Features Overview

The Webpage Manager now provides comprehensive form editing capabilities:

### ✨ **View Imported Pages**
- Browse all imported HTML pages with form counts
- Search and filter pages
- View detailed page information including metadata

### 🔧 **Edit Form Attributes**
- **Form-level editing**: Modify action, method, encoding type
- **Field-level editing**: Change all field properties and validation rules
- **Add/Remove fields**: Dynamically add new fields or remove existing ones
- **Real-time updates**: Changes are saved to the database immediately

### 📋 **Supported Field Types**
- Text inputs (text, email, password, url, tel, search)
- Numeric inputs (number, range)
- Date/time inputs (date, datetime-local, time)
- Selection inputs (radio, checkbox, select)
- File uploads
- Textareas and hidden fields

## How to Use

### 1. Import a Page
1. Go to the **Import Pages** tab
2. Drag and drop or select your HTML file(s)
3. The system will automatically detect and analyze forms
4. Associated CSS, JS, and image files can be uploaded together

### 2. View Imported Pages
1. Switch to the **Manage Pages** tab
2. Browse all imported pages with form counts displayed
3. Use the search bar to find specific pages
4. Click on any page card to view detailed information

### 3. Edit Forms and Fields
1. **Open Page Details**: Click on a page card to open the details modal
2. **Enter Edit Mode**: Click the "Edit Forms" button in the modal
3. **Edit Form Attributes**:
   - Modify form action URL
   - Change HTTP method (GET/POST)
   - Set encoding type (multipart/form-data, etc.)
   - Click the save icon next to each form to save changes

4. **Edit Field Properties**:
   - Change field names and types
   - Set placeholder text and default values
   - Configure validation (required, min/max length, patterns)
   - Set field constraints (readonly, disabled)
   - Click the save icon next to each field to save changes

5. **Add New Fields**:
   - Click the "+" button in the fields section
   - Enter field name and type
   - The new field will be added to the form

6. **Delete Fields**:
   - Click the trash icon next to any field
   - Confirm deletion when prompted

### 4. Field Attributes You Can Modify

**Basic Properties:**
- Field name and type
- Placeholder text
- Default value

**Validation Rules:**
- Required field
- Minimum/maximum length
- Regular expression patterns
- Numeric min/max values

**Field States:**
- Read-only
- Disabled
- Multiple selection (for file inputs)

**Advanced Properties:**
- Step value (for numeric inputs)
- Accept attribute (for file inputs)
- Autocomplete settings

## Example Workflow

1. **Import the Sample Form**:
   - Upload `test_samples/sample_form.html` using the Import tab
   - This contains a comprehensive contact form with various field types

2. **View and Edit**:
   - Go to Manage Pages and click on the imported form
   - Click "Edit Forms" to enter edit mode
   - Try modifying field properties like making optional fields required
   - Change field types (e.g., text to email)
   - Add new fields to the form

3. **Save Changes**:
   - Click the save icon next to modified forms/fields
   - Changes are immediately saved to the database
   - Exit edit mode to see the updated form structure

## Database Integration

- All changes are saved to the database in real-time
- Original HTML files are preserved (backups created when needed)
- Form structure can be exported as SQL for database table generation
- Analysis logs track all modifications for audit purposes

## Tips for Best Results

1. **Field Types**: Choose appropriate field types for better validation
2. **Required Fields**: Mark essential fields as required
3. **Validation**: Use patterns and length constraints for data quality
4. **User Experience**: Set helpful placeholder text and default values
5. **Testing**: Always test forms after modifications

## Troubleshooting

- **Changes not saving**: Check browser console for JavaScript errors
- **Form not displaying**: Ensure the HTML structure is valid
- **Field validation**: Verify pattern syntax for regex validation
- **Database errors**: Check PHP error logs and database connectivity

## Next Steps

After editing forms, you can:
- Generate database tables based on form structure
- Export SQL schema for your database
- Use the modified forms in your web applications
- Continue importing and managing more pages

The enhanced Webpage Manager provides a complete solution for form analysis, editing, and database integration!
