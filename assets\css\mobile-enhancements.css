/**
 * Mobile Enhancements for Webpage Manager
 * Optimized for mobile-first usage
 */

/* Mobile-First Base Styles */
@media (max-width: 768px) {
    /* Container and Layout */
    .container {
        padding: 10px;
        max-width: 100%;
    }
    
    /* Header and Navigation */
    .header {
        padding: 10px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }
    
    .nav-tabs {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
        margin-bottom: 15px;
    }
    
    .nav-tab {
        flex: 1;
        min-width: 80px;
        padding: 12px 8px;
        font-size: 0.85rem;
        text-align: center;
        border-radius: 8px;
        margin: 2px;
    }
    
    .nav-tab i {
        display: block;
        margin-bottom: 4px;
        font-size: 1.2rem;
    }
    
    /* Upload Section */
    .upload-section {
        margin-bottom: 20px;
    }
    
    .upload-area {
        padding: 20px;
        min-height: 120px;
        border: 2px dashed #007cba;
        border-radius: 10px;
        text-align: center;
        background: #f8f9fa;
    }
    
    .upload-area.dragover {
        background: #e3f2fd;
        border-color: #1976d2;
    }
    
    .upload-text {
        font-size: 0.9rem;
        margin-bottom: 10px;
    }
    
    .file-input-wrapper {
        margin-top: 10px;
    }
    
    .file-input-wrapper input[type="file"] {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;
    }
    
    /* Buttons */
    .btn {
        padding: 12px 16px;
        font-size: 1rem;
        border-radius: 8px;
        min-height: 44px; /* Touch-friendly minimum */
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }
    
    .btn-primary {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .btn-secondary {
        width: 100%;
        margin-bottom: 10px;
    }
    
    /* Forms and Inputs */
    input, textarea, select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #ddd;
        width: 100%;
        box-sizing: border-box;
    }
    
    /* Tables - Mobile Responsive */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    table {
        min-width: 600px;
        font-size: 0.85rem;
    }
    
    th, td {
        padding: 8px 4px;
        white-space: nowrap;
    }
    
    /* Card-based layout for mobile tables */
    .mobile-card-view {
        display: none;
    }
    
    .mobile-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #007cba;
    }
    
    .mobile-card-header {
        font-weight: bold;
        color: #007cba;
        margin-bottom: 8px;
        font-size: 1rem;
    }
    
    .mobile-card-content {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }
    
    .mobile-card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .mobile-card-row:last-child {
        border-bottom: none;
    }
    
    .mobile-card-label {
        font-weight: 500;
        color: #666;
        font-size: 0.85rem;
    }
    
    .mobile-card-value {
        color: #333;
        font-size: 0.9rem;
        text-align: right;
        max-width: 60%;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .mobile-card-actions {
        margin-top: 10px;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .mobile-card-actions .btn {
        flex: 1;
        min-width: 80px;
        padding: 8px 12px;
        font-size: 0.85rem;
    }
    
    /* Modals */
    .modal-content {
        margin: 10px;
        padding: 20px;
        max-width: calc(100vw - 20px);
        max-height: calc(100vh - 40px);
        border-radius: 10px;
    }
    
    .modal-header {
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
        margin-bottom: 15px;
    }
    
    .modal-title {
        font-size: 1.2rem;
        margin: 0;
    }
    
    .modal-close {
        position: absolute;
        top: 15px;
        right: 15px;
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #666;
        cursor: pointer;
        padding: 5px;
        min-width: 44px;
        min-height: 44px;
    }
    
    /* Form Fields */
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
    }
    
    .form-row {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    /* Share Controls */
    .share-controls {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .share-options {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .share-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    
    .share-option input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    /* Notifications */
    .notification {
        position: fixed;
        top: 20px;
        left: 10px;
        right: 10px;
        max-width: none;
        z-index: 9999;
        padding: 15px;
        border-radius: 8px;
        font-size: 0.9rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    /* Progress Bars */
    .progress-container {
        margin: 15px 0;
    }
    
    .progress-bar {
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
        background: #e9ecef;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #007cba, #0056b3);
        transition: width 0.3s ease;
    }
    
    /* Loading States */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }
    
    .loading-spinner {
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007cba;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }
    
    /* Touch-friendly improvements */
    .touch-target {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Swipe gestures for tables */
    .swipe-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: x mandatory;
    }
    
    .swipe-item {
        scroll-snap-align: start;
    }
    
    /* Hide desktop-only elements */
    .desktop-only {
        display: none !important;
    }
    
    /* Show mobile-only elements */
    .mobile-only {
        display: block !important;
    }
    
    /* Sticky elements */
    .sticky-header {
        position: sticky;
        top: 0;
        background: white;
        z-index: 100;
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }
    
    /* Bottom navigation for mobile */
    .mobile-bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-top: 1px solid rgba(102, 126, 234, 0.2);
        padding: 10px 0 calc(10px + env(safe-area-inset-bottom));
        display: flex;
        justify-content: space-around;
        z-index: 1000;
        box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px;
        color: #666;
        text-decoration: none;
        font-size: 0.75rem;
        min-width: 60px;
        border-radius: 12px;
        transition: all 0.3s ease;
        font-family: 'Inter', sans-serif;
    }

    .mobile-nav-item.active {
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }

    .mobile-nav-item:hover {
        color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }
    
    .mobile-nav-item svg {
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
        stroke: currentColor;
        stroke-width: 2;
        fill: none;
    }

    /* Hide Font Awesome icons in mobile nav */
    .mobile-nav-item i[class*="fa"] {
        display: none !important;
    }

    /* Ensure Feather icons are visible */
    .mobile-nav-item i[data-feather] {
        display: block;
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        padding: 15px;
    }
    
    .nav-tabs {
        justify-content: center;
    }
    
    .nav-tab {
        min-width: 120px;
    }
    
    table {
        font-size: 0.9rem;
    }
    
    .modal-content {
        max-width: 600px;
        margin: 5% auto;
    }
}

/* Very small screens (phones in portrait) */
@media (max-width: 480px) {
    .container {
        padding: 5px;
    }
    
    .header h1 {
        font-size: 1.3rem;
    }
    
    .nav-tab {
        font-size: 0.75rem;
        padding: 10px 6px;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 10px 12px;
    }
    
    .modal-content {
        margin: 5px;
        padding: 15px;
        max-width: calc(100vw - 10px);
        max-height: calc(100vh - 20px);
    }
    
    .notification {
        left: 5px;
        right: 5px;
        top: 10px;
        font-size: 0.85rem;
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .modal-content {
        max-height: calc(100vh - 20px);
        overflow-y: auto;
    }
    
    .upload-area {
        min-height: 80px;
        padding: 15px;
    }
    
    .nav-tabs {
        margin-bottom: 10px;
    }
    
    .nav-tab {
        padding: 8px 6px;
    }
    
    .nav-tab i {
        font-size: 1rem;
        margin-bottom: 2px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn, .nav-tab, input, textarea, select {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    .mobile-card {
        background: #2d3748;
        color: #e2e8f0;
        border-left-color: #4299e1;
    }
    
    .mobile-card-label {
        color: #a0aec0;
    }
    
    .mobile-card-value {
        color: #e2e8f0;
    }
    
    .upload-area {
        background: #2d3748;
        border-color: #4299e1;
        color: #e2e8f0;
    }
    
    .notification {
        background: #2d3748;
        color: #e2e8f0;
    }
}
