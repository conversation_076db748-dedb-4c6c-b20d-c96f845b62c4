<?php
/**
 * Enhanced ZIP Extractor
 * Automatically detects and prioritizes index pages
 * Improved for online deployment
 */

class EnhancedZipExtractor {
    private $uploadDir;
    private $database;
    private $db;
    
    public function __construct() {
        $this->uploadDir = __DIR__ . '/../uploads/';
        $this->database = new Database();
        $this->db = $this->database->getConnection();
        
        // Ensure upload directories exist
        $this->ensureDirectories();
    }
    
    private function ensureDirectories() {
        $dirs = ['pages', 'assets', 'temp'];
        foreach ($dirs as $dir) {
            $path = $this->uploadDir . $dir;
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }
    
    /**
     * Process ZIP file with enhanced index page detection
     */
    public function processZipFile($file, $projectId = 1) {
        $startTime = microtime(true);
        $extractionId = null;
        
        try {
            // Log extraction start
            $extractionId = $this->logExtractionStart($file['name']);
            
            // Create temporary extraction directory
            $tempDir = $this->uploadDir . 'temp' . DIRECTORY_SEPARATOR . uniqid('zip_') . DIRECTORY_SEPARATOR;
            mkdir($tempDir, 0755, true);
            
            // Extract ZIP file
            $zip = new ZipArchive();
            $result = $zip->open($file['tmp_name']);
            
            if ($result !== TRUE) {
                throw new Exception("Failed to open ZIP file: " . $this->getZipError($result));
            }
            
            $zip->extractTo($tempDir);
            $totalFiles = $zip->numFiles;
            $zip->close();
            
            // Analyze extracted structure
            $structure = $this->analyzeFileStructure($tempDir);
            
            // Find HTML files with priority for index pages
            $htmlFiles = $this->findHtmlFilesWithPriority($tempDir);
            
            $processedPages = [];
            $indexPageId = null;
            
            // Process each HTML file
            foreach ($htmlFiles as $htmlFile) {
                $pageResult = $this->processHtmlWithAssets($htmlFile, $tempDir, $projectId);
                if ($pageResult['success']) {
                    $processedPages[] = $pageResult;
                    
                    // Mark index page
                    if ($pageResult['is_index']) {
                        $indexPageId = $pageResult['page_id'];
                    }
                }
            }
            
            // Update extraction log
            $extractionTime = microtime(true) - $startTime;
            $this->logExtractionComplete($extractionId, [
                'total_files' => $totalFiles,
                'html_files_found' => count($htmlFiles),
                'assets_found' => $structure['assets_count'],
                'index_page_id' => $indexPageId,
                'file_structure' => $structure,
                'extraction_time' => $extractionTime
            ]);
            
            // Clean up temporary directory
            $this->removeDirectory($tempDir);
            
            return [
                'success' => true,
                'message' => 'ZIP file processed successfully',
                'pages_created' => count($processedPages),
                'index_page_id' => $indexPageId,
                'extraction_id' => $extractionId,
                'pages' => $processedPages
            ];
            
        } catch (Exception $e) {
            if ($extractionId) {
                $this->logExtractionError($extractionId, $e->getMessage());
            }
            
            // Clean up on error
            if (isset($tempDir) && is_dir($tempDir)) {
                $this->removeDirectory($tempDir);
            }
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'extraction_id' => $extractionId
            ];
        }
    }
    
    /**
     * Find HTML files with priority for index pages
     */
    private function findHtmlFilesWithPriority($directory) {
        $htmlFiles = [];
        $indexFiles = [];
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory),
            RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $extension = strtolower(pathinfo($file->getFilename(), PATHINFO_EXTENSION));
                if (in_array($extension, ['html', 'htm'])) {
                    $filename = strtolower(pathinfo($file->getFilename(), PATHINFO_FILENAME));
                    
                    // Check if this is likely an index page
                    $isIndex = in_array($filename, ['index', 'home', 'main', 'default']) ||
                               strpos($filename, 'index') === 0 ||
                               $file->getFilename() === 'index.html' ||
                               $file->getFilename() === 'index.htm';
                    
                    if ($isIndex) {
                        $indexFiles[] = $file->getPathname();
                    } else {
                        $htmlFiles[] = $file->getPathname();
                    }
                }
            }
        }
        
        // Return index files first, then other HTML files
        return array_merge($indexFiles, $htmlFiles);
    }
    
    /**
     * Analyze file structure for better organization
     */
    private function analyzeFileStructure($directory) {
        $structure = [
            'total_files' => 0,
            'html_files' => 0,
            'css_files' => 0,
            'js_files' => 0,
            'image_files' => 0,
            'font_files' => 0,
            'other_files' => 0,
            'assets_count' => 0,
            'directories' => [],
            'file_types' => []
        ];
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory),
            RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $structure['total_files']++;
                $extension = strtolower(pathinfo($file->getFilename(), PATHINFO_EXTENSION));
                
                // Count by type
                switch ($extension) {
                    case 'html':
                    case 'htm':
                        $structure['html_files']++;
                        break;
                    case 'css':
                        $structure['css_files']++;
                        $structure['assets_count']++;
                        break;
                    case 'js':
                        $structure['js_files']++;
                        $structure['assets_count']++;
                        break;
                    case 'png':
                    case 'jpg':
                    case 'jpeg':
                    case 'gif':
                    case 'svg':
                    case 'webp':
                        $structure['image_files']++;
                        $structure['assets_count']++;
                        break;
                    case 'woff':
                    case 'woff2':
                    case 'ttf':
                    case 'otf':
                    case 'eot':
                        $structure['font_files']++;
                        $structure['assets_count']++;
                        break;
                    default:
                        $structure['other_files']++;
                        break;
                }
                
                // Track file types
                if (!isset($structure['file_types'][$extension])) {
                    $structure['file_types'][$extension] = 0;
                }
                $structure['file_types'][$extension]++;
                
                // Track directory structure
                $relativePath = str_replace($directory, '', dirname($file->getPathname()));
                $relativePath = trim($relativePath, DIRECTORY_SEPARATOR);
                if ($relativePath && !in_array($relativePath, $structure['directories'])) {
                    $structure['directories'][] = $relativePath;
                }
            }
        }
        
        return $structure;
    }
    
    /**
     * Process HTML file with assets and index detection
     */
    private function processHtmlWithAssets($htmlFile, $tempDir, $projectId) {
        $filename = basename($htmlFile);
        $filenameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        
        // Check if this is an index page
        $isIndex = in_array(strtolower($filenameWithoutExt), ['index', 'home', 'main', 'default']) ||
                   strpos(strtolower($filenameWithoutExt), 'index') === 0;
        
        // Read HTML content
        $htmlContent = file_get_contents($htmlFile);
        
        // Extract title from HTML
        $title = $this->extractTitle($htmlContent);
        if (!$title) {
            $title = $isIndex ? 'Home Page' : ucfirst(str_replace(['_', '-'], ' ', $filenameWithoutExt));
        }
        
        // Generate unique filename
        $uniqueFilename = $filenameWithoutExt . '_' . time() . '_' . uniqid() . '.html';
        $targetPath = $this->uploadDir . 'pages' . DIRECTORY_SEPARATOR . $uniqueFilename;
        
        // Copy HTML file
        copy($htmlFile, $targetPath);
        
        // Store in database
        $sql = "INSERT INTO pages (
                    project_id, filename, original_filename, title, 
                    file_path, file_size, file_hash, has_forms, 
                    is_active, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $projectId,
            $uniqueFilename,
            $filename,
            $title,
            $targetPath,
            filesize($targetPath),
            hash_file('sha256', $targetPath),
            $this->hasFormElements($htmlContent) ? 1 : 0
        ]);
        
        $pageId = $this->db->lastInsertId();
        
        // Process associated assets
        $this->processAssetsForPage($htmlContent, $tempDir, $pageId);
        
        // Analyze forms if present
        if ($this->hasFormElements($htmlContent)) {
            $this->analyzeFormsInPage($pageId, $targetPath);
        }
        
        return [
            'success' => true,
            'page_id' => $pageId,
            'title' => $title,
            'filename' => $uniqueFilename,
            'is_index' => $isIndex,
            'has_forms' => $this->hasFormElements($htmlContent)
        ];
    }
    
    /**
     * Extract title from HTML content
     */
    private function extractTitle($htmlContent) {
        if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $htmlContent, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return null;
    }
    
    /**
     * Check if HTML has form elements
     */
    private function hasFormElements($htmlContent) {
        return preg_match('/<(form|input|textarea|select)\b[^>]*>/i', $htmlContent);
    }
    
    /**
     * Process assets referenced in HTML
     */
    private function processAssetsForPage($htmlContent, $tempDir, $pageId) {
        // Extract asset references
        $assetRefs = $this->extractAssetReferences($htmlContent);
        
        foreach ($assetRefs as $ref) {
            $assetPath = $this->findAssetFile($ref, $tempDir);
            if ($assetPath && file_exists($assetPath)) {
                $this->processAssetFile($assetPath, $pageId, $ref);
            }
        }
    }
    
    /**
     * Extract asset references from HTML
     */
    private function extractAssetReferences($htmlContent) {
        $references = [];
        
        // CSS files
        preg_match_all('/<link[^>]+href=["\']([^"\']+\.css)["\'][^>]*>/i', $htmlContent, $matches);
        $references = array_merge($references, $matches[1]);
        
        // JavaScript files
        preg_match_all('/<script[^>]+src=["\']([^"\']+\.js)["\'][^>]*>/i', $htmlContent, $matches);
        $references = array_merge($references, $matches[1]);
        
        // Images
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $htmlContent, $matches);
        $references = array_merge($references, $matches[1]);
        
        // Background images in CSS
        preg_match_all('/background-image:\s*url\(["\']?([^"\']+)["\']?\)/i', $htmlContent, $matches);
        $references = array_merge($references, $matches[1]);
        
        return array_unique($references);
    }
    
    /**
     * Find asset file in extracted directory
     */
    private function findAssetFile($reference, $tempDir) {
        // Remove leading slash and query parameters
        $cleanRef = ltrim(parse_url($reference, PHP_URL_PATH), '/');
        
        // Try different possible locations
        $possiblePaths = [
            $tempDir . $cleanRef,
            $tempDir . DIRECTORY_SEPARATOR . $cleanRef,
            $tempDir . DIRECTORY_SEPARATOR . basename($cleanRef)
        ];
        
        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }
        
        return null;
    }
    
    /**
     * Process individual asset file
     */
    private function processAssetFile($assetPath, $pageId, $originalRef) {
        $fileInfo = pathinfo($assetPath);
        $extension = strtolower($fileInfo['extension']);
        
        // Determine file type
        $fileType = $this->getFileType($extension);
        
        // Generate unique filename
        $uniqueName = $fileInfo['filename'] . '_' . time() . '_' . uniqid() . '.' . $extension;
        
        // Determine target directory
        $targetDir = $this->uploadDir . 'assets' . DIRECTORY_SEPARATOR;
        $targetPath = $targetDir . $uniqueName;
        
        // Copy file
        if (copy($assetPath, $targetPath)) {
            // Store in database
            $sql = "INSERT INTO associated_files (
                        page_id, filename, original_filename, file_path, 
                        file_type, file_size, mime_type, is_referenced, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW())";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId,
                $uniqueName,
                basename($assetPath),
                $targetPath,
                $fileType,
                filesize($targetPath),
                mime_content_type($targetPath)
            ]);
        }
    }
    
    /**
     * Determine file type from extension
     */
    private function getFileType($extension) {
        $types = [
            'css' => 'css',
            'js' => 'js',
            'png' => 'image',
            'jpg' => 'image',
            'jpeg' => 'image',
            'gif' => 'image',
            'svg' => 'image',
            'webp' => 'image',
            'woff' => 'font',
            'woff2' => 'font',
            'ttf' => 'font',
            'otf' => 'font',
            'eot' => 'font'
        ];
        
        return $types[$extension] ?? 'other';
    }
    
    /**
     * Analyze forms in the page
     */
    private function analyzeFormsInPage($pageId, $filePath) {
        // This would integrate with existing form analysis functionality
        // For now, just mark that the page has forms
        $sql = "UPDATE pages SET has_forms = 1, last_analyzed = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
    }
    
    /**
     * Log extraction start
     */
    private function logExtractionStart($filename) {
        $sql = "INSERT INTO zip_extractions (
                    original_filename, extracted_path, extraction_status, created_at
                ) VALUES (?, ?, 'processing', NOW())";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$filename, '']);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Log extraction completion
     */
    private function logExtractionComplete($extractionId, $data) {
        $sql = "UPDATE zip_extractions SET 
                    total_files = ?, html_files_found = ?, assets_found = ?,
                    index_page_id = ?, file_structure = ?, extraction_time = ?,
                    extraction_status = 'completed', completed_at = NOW()
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['total_files'],
            $data['html_files_found'],
            $data['assets_found'],
            $data['index_page_id'],
            json_encode($data['file_structure']),
            $data['extraction_time'],
            $extractionId
        ]);
    }
    
    /**
     * Log extraction error
     */
    private function logExtractionError($extractionId, $error) {
        $sql = "UPDATE zip_extractions SET 
                    extraction_status = 'failed', error_message = ?, completed_at = NOW()
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$error, $extractionId]);
    }
    
    /**
     * Get ZIP error message
     */
    private function getZipError($code) {
        $errors = [
            ZipArchive::ER_OK => 'No error',
            ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
            ZipArchive::ER_RENAME => 'Renaming temporary file failed',
            ZipArchive::ER_CLOSE => 'Closing zip archive failed',
            ZipArchive::ER_SEEK => 'Seek error',
            ZipArchive::ER_READ => 'Read error',
            ZipArchive::ER_WRITE => 'Write error',
            ZipArchive::ER_CRC => 'CRC error',
            ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
            ZipArchive::ER_NOENT => 'No such file',
            ZipArchive::ER_EXISTS => 'File already exists',
            ZipArchive::ER_OPEN => 'Can\'t open file',
            ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
            ZipArchive::ER_ZLIB => 'Zlib error',
            ZipArchive::ER_MEMORY => 'Memory allocation failure',
            ZipArchive::ER_CHANGED => 'Entry has been changed',
            ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
            ZipArchive::ER_EOF => 'Premature EOF',
            ZipArchive::ER_INVAL => 'Invalid argument',
            ZipArchive::ER_NOZIP => 'Not a zip archive',
            ZipArchive::ER_INTERNAL => 'Internal error',
            ZipArchive::ER_INCONS => 'Zip archive inconsistent',
            ZipArchive::ER_REMOVE => 'Can\'t remove file',
            ZipArchive::ER_DELETED => 'Entry has been deleted'
        ];
        
        return $errors[$code] ?? "Unknown error code: $code";
    }
    
    /**
     * Remove directory recursively
     */
    private function removeDirectory($dir) {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        
        return rmdir($dir);
    }
}
?>
