<?php
/**
 * Database Manager for Webpage Manager v2.0
 * Handles database operations, maintenance, and utilities
 */

require_once dirname(__DIR__) . '/config/database.php';

header('Content-Type: application/json');

class DatabaseManager {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    public function getStats() {
        try {
            $stats = [];
            
            // Get table counts
            $tables = [
                'pages' => 'Imported Pages',
                'forms' => 'Detected Forms', 
                'form_fields' => 'Form Fields',
                'projects' => 'Projects',
                'form_templates' => 'Form Templates',
                'form_submissions' => 'Form Submissions',
                'generated_tables' => 'Generated Tables',
                'associated_files' => 'Associated Files',
                'analysis_log' => 'Analysis Log Entries',
                'activity_log' => 'Activity Log Entries',
                'page_versions' => 'Page Versions'
            ];
            
            foreach ($tables as $table => $description) {
                $sql = "SELECT COUNT(*) as count FROM {$table}";
                $stmt = $this->db->prepare($sql);
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats[$table] = [
                    'count' => $result['count'],
                    'description' => $description
                ];
            }
            
            // Get database size
            $sql = "SELECT 
                        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = 'webpage_manager'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['database_size'] = $result['size_mb'] . ' MB';
            
            // Get recent activity
            $sql = "SELECT COUNT(*) as count FROM activity_log WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['recent_activity'] = $result['count'];
            
            return ['success' => true, 'stats' => $stats];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function cleanupLogs($days = 30) {
        try {
            $sql = "DELETE FROM analysis_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$days]);
            $analysis_deleted = $stmt->rowCount();
            
            $sql = "DELETE FROM activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$days]);
            $activity_deleted = $stmt->rowCount();
            
            return [
                'success' => true, 
                'message' => "Cleaned up {$analysis_deleted} analysis log entries and {$activity_deleted} activity log entries older than {$days} days"
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function optimizeTables() {
        try {
            $sql = "SHOW TABLES FROM webpage_manager";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $optimized = [];
            foreach ($tables as $table) {
                $sql = "OPTIMIZE TABLE {$table}";
                $this->db->exec($sql);
                $optimized[] = $table;
            }
            
            return [
                'success' => true, 
                'message' => 'Optimized ' . count($optimized) . ' tables',
                'tables' => $optimized
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function backupDatabase() {
        try {
            $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
            $backup_path = '../uploads/backups/' . $backup_file;
            
            // Create backups directory if it doesn't exist
            if (!is_dir('../uploads/backups/')) {
                mkdir('../uploads/backups/', 0755, true);
            }
            
            // Get all tables
            $sql = "SHOW TABLES FROM webpage_manager";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $backup_content = "-- Webpage Manager Database Backup\n";
            $backup_content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
            $backup_content .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
            
            foreach ($tables as $table) {
                // Get table structure
                $sql = "SHOW CREATE TABLE {$table}";
                $stmt = $this->db->prepare($sql);
                $stmt->execute();
                $create_table = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $backup_content .= "-- Table: {$table}\n";
                $backup_content .= "DROP TABLE IF EXISTS `{$table}`;\n";
                $backup_content .= $create_table['Create Table'] . ";\n\n";
                
                // Get table data
                $sql = "SELECT * FROM {$table}";
                $stmt = $this->db->prepare($sql);
                $stmt->execute();
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($rows)) {
                    $backup_content .= "-- Data for table {$table}\n";
                    foreach ($rows as $row) {
                        $values = array_map(function($value) {
                            return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                        }, array_values($row));
                        
                        $backup_content .= "INSERT INTO `{$table}` VALUES (" . implode(', ', $values) . ");\n";
                    }
                    $backup_content .= "\n";
                }
            }
            
            $backup_content .= "SET FOREIGN_KEY_CHECKS = 1;\n";
            
            if (file_put_contents($backup_path, $backup_content)) {
                return [
                    'success' => true, 
                    'message' => 'Database backup created successfully',
                    'file' => $backup_file,
                    'path' => $backup_path,
                    'size' => filesize($backup_path)
                ];
            } else {
                return ['success' => false, 'message' => 'Failed to write backup file'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
        }
    }
    
    public function getRecentActivity($limit = 50) {
        try {
            $sql = "SELECT 
                        al.*,
                        u.username
                    FROM activity_log al
                    LEFT JOIN users u ON al.user_id = u.id
                    ORDER BY al.created_at DESC
                    LIMIT ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$limit]);
            $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ['success' => true, 'activities' => $activities];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function getSystemInfo() {
        try {
            // Get database version
            $sql = "SELECT VERSION() as version";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $db_version = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get app settings
            $sql = "SELECT setting_key, setting_value FROM app_settings WHERE is_system = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            return [
                'success' => true,
                'info' => [
                    'database_version' => $db_version['version'],
                    'app_version' => $settings['app_version'] ?? '2.0.0',
                    'schema_version' => $settings['database_version'] ?? '2.0.0',
                    'php_version' => PHP_VERSION,
                    'settings' => $settings
                ]
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
}

// Check authentication
require_once __DIR__ . '/auth_manager.php';
$auth = new AuthManager();

if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Admin-only operations
$adminActions = ['cleanup', 'optimize', 'backup'];
$action = $_GET['action'] ?? $_POST['action'] ?? 'stats';

if (in_array($action, $adminActions) && !$auth->isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Admin privileges required']);
    exit;
}

// Handle the request
try {
    $manager = new DatabaseManager();
    
    switch ($action) {
        case 'stats':
            $result = $manager->getStats();
            break;
            
        case 'cleanup':
            $days = $_POST['days'] ?? 30;
            $result = $manager->cleanupLogs($days);
            break;
            
        case 'optimize':
            $result = $manager->optimizeTables();
            break;
            
        case 'backup':
            $result = $manager->backupDatabase();
            break;
            
        case 'activity':
            $limit = $_GET['limit'] ?? 50;
            $result = $manager->getRecentActivity($limit);
            break;
            
        case 'info':
            $result = $manager->getSystemInfo();
            break;
            
        default:
            $result = ['success' => false, 'message' => 'Invalid action'];
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
