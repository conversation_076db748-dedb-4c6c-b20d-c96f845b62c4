/**
 * Mobile Enhancements for Webpage Manager
 * Touch events, gestures, and mobile-specific functionality
 */

class MobileEnhancements {
    constructor() {
        this.isMobile = this.detectMobile();
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.isScrolling = false;
        
        this.init();
    }
    
    detectMobile() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    init() {
        if (this.isMobile) {
            this.setupMobileFeatures();
            this.setupTouchEvents();
            // Mobile navigation removed - using desktop navigation on mobile
            this.setupMobileModals();
            this.setupMobileTables();
            this.setupMobileUpload();
        }
        
        // Listen for orientation changes
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
        
        // Listen for resize events
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    setupMobileFeatures() {
        // Add mobile class to body
        document.body.classList.add('mobile-device');
        
        // Prevent zoom on input focus (iOS)
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.type !== 'file') {
                input.style.fontSize = '16px';
            }
        });
        
        // Add touch-friendly classes
        const buttons = document.querySelectorAll('.btn, button');
        buttons.forEach(btn => {
            btn.classList.add('touch-target');
        });
        
        // Improve tap targets
        const clickableElements = document.querySelectorAll('a, button, .clickable, .nav-tab');
        clickableElements.forEach(el => {
            el.style.minHeight = '44px';
            el.style.minWidth = '44px';
            el.style.display = 'flex';
            el.style.alignItems = 'center';
            el.style.justifyContent = 'center';
        });
    }
    
    setupTouchEvents() {
        // Add touch event listeners for better mobile interaction
        document.addEventListener('touchstart', (e) => {
            this.touchStartX = e.touches[0].clientX;
            this.touchStartY = e.touches[0].clientY;
            this.isScrolling = false;
        }, { passive: true });
        
        document.addEventListener('touchmove', (e) => {
            if (!this.touchStartX || !this.touchStartY) return;
            
            const touchEndX = e.touches[0].clientX;
            const touchEndY = e.touches[0].clientY;
            const diffX = this.touchStartX - touchEndX;
            const diffY = this.touchStartY - touchEndY;
            
            // Determine if user is scrolling
            if (Math.abs(diffY) > Math.abs(diffX)) {
                this.isScrolling = true;
            }
        }, { passive: true });
        
        // Handle swipe gestures for tables
        const tables = document.querySelectorAll('.table-responsive');
        tables.forEach(table => {
            this.setupTableSwipe(table);
        });
    }
    
    setupTableSwipe(table) {
        let startX = 0;
        let scrollLeft = 0;
        
        table.addEventListener('touchstart', (e) => {
            startX = e.touches[0].pageX - table.offsetLeft;
            scrollLeft = table.scrollLeft;
        }, { passive: true });
        
        table.addEventListener('touchmove', (e) => {
            if (!startX) return;
            
            const x = e.touches[0].pageX - table.offsetLeft;
            const walk = (x - startX) * 2;
            table.scrollLeft = scrollLeft - walk;
        }, { passive: true });
        
        table.addEventListener('touchend', () => {
            startX = 0;
        });
    }
    
    // Mobile navigation methods removed - using desktop navigation on mobile
    
    setupMobileModals() {
        // Improve modal behavior on mobile
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            // Prevent background scrolling when modal is open
            modal.addEventListener('show', () => {
                document.body.style.overflow = 'hidden';
                document.body.style.position = 'fixed';
                document.body.style.width = '100%';
            });
            
            modal.addEventListener('hide', () => {
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
            });
            
            // Close modal on background tap
            modal.addEventListener('touchstart', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal);
                }
            });
        });
    }
    
    setupMobileTables() {
        // Convert tables to mobile-friendly card view
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            this.createMobileCardView(table);
        });
    }
    
    createMobileCardView(table) {
        if (table.querySelector('.mobile-card-view')) return;
        
        const cardContainer = document.createElement('div');
        cardContainer.className = 'mobile-card-view mobile-only';
        
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const card = document.createElement('div');
            card.className = 'mobile-card';
            
            const cardHeader = document.createElement('div');
            cardHeader.className = 'mobile-card-header';
            cardHeader.textContent = cells[0]?.textContent.trim() || 'Item';
            card.appendChild(cardHeader);
            
            const cardContent = document.createElement('div');
            cardContent.className = 'mobile-card-content';
            
            cells.forEach((cell, index) => {
                if (index === 0) return; // Skip first cell as it's used in header
                
                const cardRow = document.createElement('div');
                cardRow.className = 'mobile-card-row';
                
                const label = document.createElement('span');
                label.className = 'mobile-card-label';
                label.textContent = headers[index] || `Field ${index}`;
                
                const value = document.createElement('span');
                value.className = 'mobile-card-value';
                value.innerHTML = cell.innerHTML;
                
                cardRow.appendChild(label);
                cardRow.appendChild(value);
                cardContent.appendChild(cardRow);
            });
            
            card.appendChild(cardContent);
            
            // Add actions if present in original row
            const actions = row.querySelector('.page-actions, .form-actions, .submission-actions');
            if (actions) {
                const cardActions = document.createElement('div');
                cardActions.className = 'mobile-card-actions';
                cardActions.innerHTML = actions.innerHTML;
                card.appendChild(cardActions);
            }
            
            cardContainer.appendChild(card);
        });
        
        table.parentNode.insertBefore(cardContainer, table.nextSibling);
        
        // Hide table on mobile, show cards
        table.classList.add('desktop-only');
    }
    
    setupMobileUpload() {
        // Improve file upload for mobile
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            // Add accept attribute for better mobile file picker
            if (!input.hasAttribute('accept')) {
                input.setAttribute('accept', '.html,.htm,.zip,text/html,application/zip');
            }
            
            // Add capture attribute for camera access if needed
            if (input.classList.contains('camera-upload')) {
                input.setAttribute('capture', 'environment');
            }
        });
        
        // Improve drag and drop for mobile
        const uploadAreas = document.querySelectorAll('.upload-area');
        uploadAreas.forEach(area => {
            // Add visual feedback for touch
            area.addEventListener('touchstart', () => {
                area.classList.add('touch-active');
            }, { passive: true });
            
            area.addEventListener('touchend', () => {
                area.classList.remove('touch-active');
            }, { passive: true });
        });
    }
    
    handleOrientationChange() {
        // Adjust layout for orientation changes
        setTimeout(() => {
            // Recalculate modal positions
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const content = modal.querySelector('.modal-content');
                if (content) {
                    content.style.maxHeight = `${window.innerHeight - 40}px`;
                }
            });
            
            // Update table card views
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                const cardView = table.parentNode.querySelector('.mobile-card-view');
                if (cardView) {
                    cardView.remove();
                    this.createMobileCardView(table);
                }
            });
        }, 300);
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = this.detectMobile();
        
        if (wasMobile !== this.isMobile) {
            // Mobile state changed, reinitialize
            if (this.isMobile) {
                this.setupMobileFeatures();
            } else {
                // Remove mobile-specific elements
                document.body.classList.remove('mobile-device');
                // Mobile bottom navigation removed - using desktop navigation
            }
        }
    }
    
    closeModal(modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.width = '';
    }
    
    // Utility methods for mobile interactions
    showMobileNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification mobile-notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 10);
        
        // Remove after duration
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }
    
    vibrate(pattern = [100]) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    }
    
    // Add haptic feedback for button presses
    addHapticFeedback() {
        const buttons = document.querySelectorAll('.btn, button, .nav-tab');
        buttons.forEach(btn => {
            btn.addEventListener('touchstart', () => {
                this.vibrate([10]);
            }, { passive: true });
        });
    }
}

// Initialize mobile enhancements when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mobileEnhancements = new MobileEnhancements();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileEnhancements;
}
