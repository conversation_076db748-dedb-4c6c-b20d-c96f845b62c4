<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Input Detection Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select, button { padding: 8px; margin: 5px 0; }
        .custom-form { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-secondary { background: #6c757d; }
        .editable { border: 1px solid #ccc; padding: 10px; min-height: 50px; background: white; }
        .interactive-element { background: #28a745; color: white; padding: 10px; border-radius: 4px; cursor: pointer; display: inline-block; margin: 5px; }
    </style>
</head>
<body>
    <h1>Enhanced Input Detection Test Page</h1>
    <p>This page contains various types of inputs to test the enhanced detection system.</p>

    <!-- Traditional HTML Form -->
    <div class="section">
        <h2>1. Traditional HTML Form</h2>
        <form id="contact-form" action="/submit" method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="name">Full Name:</label>
                <input type="text" id="name" name="name" required maxlength="100" placeholder="Enter your full name">
            </div>
            
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number:</label>
                <input type="tel" id="phone" name="phone" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************">
            </div>
            
            <div class="form-group">
                <label for="website">Website:</label>
                <input type="url" id="website" name="website" placeholder="https://example.com">
            </div>
            
            <div class="form-group">
                <label for="age">Age:</label>
                <input type="number" id="age" name="age" min="18" max="120" step="1">
            </div>
            
            <div class="form-group">
                <label for="birthday">Birthday:</label>
                <input type="date" id="birthday" name="birthday">
            </div>
            
            <div class="form-group">
                <label for="appointment">Appointment Time:</label>
                <input type="datetime-local" id="appointment" name="appointment">
            </div>
            
            <div class="form-group">
                <label for="favorite-color">Favorite Color:</label>
                <input type="color" id="favorite-color" name="favorite_color" value="#ff0000">
            </div>
            
            <div class="form-group">
                <label for="volume">Volume Level:</label>
                <input type="range" id="volume" name="volume" min="0" max="100" value="50">
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" minlength="8" required>
            </div>
            
            <div class="form-group">
                <label for="avatar">Profile Picture:</label>
                <input type="file" id="avatar" name="avatar" accept="image/*" capture="user">
            </div>
            
            <div class="form-group">
                <label>Gender:</label>
                <label><input type="radio" name="gender" value="male"> Male</label>
                <label><input type="radio" name="gender" value="female"> Female</label>
                <label><input type="radio" name="gender" value="other"> Other</label>
            </div>
            
            <div class="form-group">
                <label>Interests:</label>
                <label><input type="checkbox" name="interests[]" value="sports"> Sports</label>
                <label><input type="checkbox" name="interests[]" value="music"> Music</label>
                <label><input type="checkbox" name="interests[]" value="travel"> Travel</label>
            </div>
            
            <div class="form-group">
                <label for="country">Country:</label>
                <select id="country" name="country" required>
                    <option value="">Select a country</option>
                    <option value="us">United States</option>
                    <option value="ca">Canada</option>
                    <option value="uk">United Kingdom</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" name="message" rows="4" cols="50" placeholder="Enter your message here..."></textarea>
            </div>
            
            <input type="hidden" name="form_id" value="contact_form_v1">
            
            <div class="form-group">
                <button type="submit">Submit Form</button>
                <button type="reset">Reset Form</button>
                <button type="button" onclick="alert('Custom action')">Custom Action</button>
            </div>
        </form>
    </div>

    <!-- Standalone Inputs -->
    <div class="section">
        <h2>2. Standalone Input Elements</h2>
        <p>These inputs are not inside a form tag:</p>
        
        <div class="form-group">
            <label for="search-box">Search:</label>
            <input type="search" id="search-box" name="search" placeholder="Search products...">
            <button onclick="performSearch()">Search</button>
        </div>
        
        <div class="form-group">
            <label for="newsletter-email">Newsletter Email:</label>
            <input type="email" id="newsletter-email" name="newsletter_email" placeholder="Subscribe to newsletter">
            <button onclick="subscribe()">Subscribe</button>
        </div>
        
        <div class="form-group">
            <label for="quick-note">Quick Note:</label>
            <textarea id="quick-note" name="quick_note" placeholder="Jot down a quick note..."></textarea>
        </div>
        
        <div class="form-group">
            <label for="rating">Rating:</label>
            <select id="rating" name="rating">
                <option value="1">1 Star</option>
                <option value="2">2 Stars</option>
                <option value="3">3 Stars</option>
                <option value="4">4 Stars</option>
                <option value="5">5 Stars</option>
            </select>
        </div>
    </div>

    <!-- Custom Form Implementation -->
    <div class="section">
        <h2>3. Custom Form Implementation</h2>
        <div class="custom-form" data-form="true" data-action="/api/contact" data-method="POST">
            <h3>Contact Us (Custom Implementation)</h3>
            
            <div class="form-group">
                <label>Your Name:</label>
                <input type="text" data-name="customer_name" data-required="true" placeholder="Full name">
            </div>
            
            <div class="form-group">
                <label>Email:</label>
                <input type="email" data-name="customer_email" data-validation="email" placeholder="Email address">
            </div>
            
            <div class="form-group">
                <label>Subject:</label>
                <select data-name="subject" data-required="true">
                    <option value="">Choose a subject</option>
                    <option value="support">Technical Support</option>
                    <option value="sales">Sales Inquiry</option>
                    <option value="general">General Question</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Message:</label>
                <textarea data-name="message" data-maxlength="500" placeholder="Your message..."></textarea>
            </div>
            
            <div class="form-group">
                <div class="btn" data-submit="true" onclick="submitCustomForm()">Send Message</div>
                <div class="btn btn-secondary" onclick="clearCustomForm()">Clear</div>
            </div>
        </div>
    </div>

    <!-- Interactive Elements -->
    <div class="section">
        <h2>4. Interactive Elements</h2>
        
        <div class="form-group">
            <label>Content Editable Area:</label>
            <div class="editable" contenteditable="true" data-name="editable_content" data-placeholder="Click to edit this content...">
                This text can be edited directly. Click to modify.
            </div>
        </div>
        
        <div class="form-group">
            <label>Interactive Buttons:</label>
            <div class="interactive-element" data-action="like" onclick="handleLike()">👍 Like</div>
            <div class="interactive-element" data-action="share" onclick="handleShare()">📤 Share</div>
            <div class="interactive-element" data-action="save" onclick="handleSave()">💾 Save</div>
        </div>
        
        <div class="form-group">
            <label>Custom Controls:</label>
            <div class="btn" role="button" aria-label="Toggle dark mode" onclick="toggleDarkMode()">🌙 Dark Mode</div>
            <div class="btn" role="button" aria-label="Print page" onclick="window.print()">🖨️ Print</div>
        </div>
    </div>

    <!-- ARIA and Accessibility -->
    <div class="section">
        <h2>5. ARIA and Accessibility Features</h2>
        
        <div class="form-group">
            <input type="text" aria-label="Search products" aria-describedby="search-help" placeholder="Product search">
            <div id="search-help">Enter keywords to search our product catalog</div>
        </div>
        
        <div class="form-group">
            <input type="password" aria-label="New password" aria-required="true" aria-invalid="false">
            <div role="alert" id="password-error" style="display: none;">Password must be at least 8 characters</div>
        </div>
        
        <div class="form-group" role="group" aria-labelledby="notification-preferences">
            <div id="notification-preferences">Notification Preferences:</div>
            <label><input type="checkbox" name="notifications[]" value="email"> Email notifications</label>
            <label><input type="checkbox" name="notifications[]" value="sms"> SMS notifications</label>
            <label><input type="checkbox" name="notifications[]" value="push"> Push notifications</label>
        </div>
    </div>

    <script>
        // Sample JavaScript functions for interactive elements
        function performSearch() { console.log('Search performed'); }
        function subscribe() { console.log('Newsletter subscription'); }
        function submitCustomForm() { console.log('Custom form submitted'); }
        function clearCustomForm() { console.log('Custom form cleared'); }
        function handleLike() { console.log('Like button clicked'); }
        function handleShare() { console.log('Share button clicked'); }
        function handleSave() { console.log('Save button clicked'); }
        function toggleDarkMode() { console.log('Dark mode toggled'); }
    </script>
</body>
</html>
