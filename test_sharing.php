<?php
/**
 * Test Sharing Functionality
 * Debug script to test the sharing system
 */

require_once 'config/database.php';

echo "<h1>Sharing System Test</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        die("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Check if required tables exist
    $tables = ['pages', 'page_shares', 'share_access_log'];
    foreach ($tables as $table) {
        $sql = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $exists = $stmt->fetch();
        
        if ($exists) {
            echo "<p>✅ Table '$table' exists</p>";
        } else {
            echo "<p>❌ Table '$table' missing</p>";
        }
    }
    
    // Check if there are any pages
    $sql = "SELECT COUNT(*) as count FROM pages";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $pageCount = $result['count'];
    
    echo "<p>📄 Total pages: $pageCount</p>";
    
    if ($pageCount > 0) {
        // Get first page
        $sql = "SELECT * FROM pages LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $page = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h2>Test Page</h2>";
        echo "<p><strong>ID:</strong> {$page['id']}</p>";
        echo "<p><strong>Title:</strong> " . ($page['title'] ?: 'No title') . "</p>";
        echo "<p><strong>Filename:</strong> {$page['original_filename']}</p>";
        echo "<p><strong>File Path:</strong> {$page['file_path']}</p>";
        echo "<p><strong>File Exists:</strong> " . (file_exists($page['file_path']) ? 'Yes' : 'No') . "</p>";
        
        // Test creating a share
        echo "<h2>Creating Test Share</h2>";
        
        require_once 'includes/sharing_manager.php';
        
        $manager = new SharingManager();
        $result = $manager->createShare($page['id'], [
            'title' => 'Test Share',
            'description' => 'This is a test share',
            'show_forms' => true
        ]);
        
        if ($result['success']) {
            echo "<p>✅ Share created successfully</p>";
            echo "<p><strong>Share URL:</strong> <a href='{$result['share']['share_url']}' target='_blank'>{$result['share']['share_url']}</a></p>";
            echo "<p><strong>Short URL:</strong> <a href='{$result['share']['short_url']}' target='_blank'>{$result['share']['short_url']}</a></p>";
            echo "<p><strong>Share Token:</strong> {$result['share']['share_token']}</p>";
            echo "<p><strong>Short Code:</strong> {$result['share']['short_code']}</p>";
        } else {
            echo "<p>❌ Failed to create share: {$result['message']}</p>";
        }
        
    } else {
        echo "<p>⚠️ No pages found. Please upload a page first.</p>";
        
        // Create a test page
        echo "<h2>Creating Test Page</h2>";
        
        $testHtml = '<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Test Page</h1>
    <form method="POST" action="">
        <label>Name: <input type="text" name="name" required></label><br><br>
        <label>Email: <input type="email" name="email" required></label><br><br>
        <label>Message: <textarea name="message" required></textarea></label><br><br>
        <button type="submit">Submit</button>
    </form>
</body>
</html>';
        
        $testFile = 'uploads/test_page.html';
        
        // Create uploads directory if it doesn't exist
        if (!is_dir('uploads')) {
            mkdir('uploads', 0755, true);
        }
        
        file_put_contents($testFile, $testHtml);
        
        // Insert test page into database
        $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            'test_page.html',
            'test_page.html',
            'Test Page',
            $testFile,
            strlen($testHtml),
            md5($testHtml)
        ]);
        
        $pageId = $db->lastInsertId();
        echo "<p>✅ Test page created with ID: $pageId</p>";
        
        // Create a test share for the new page
        require_once 'includes/sharing_manager.php';
        
        $manager = new SharingManager();
        $result = $manager->createShare($pageId, [
            'title' => 'Test Share',
            'description' => 'This is a test share',
            'show_forms' => true
        ]);
        
        if ($result['success']) {
            echo "<p>✅ Share created successfully</p>";
            echo "<p><strong>Share URL:</strong> <a href='{$result['share']['share_url']}' target='_blank'>{$result['share']['share_url']}</a></p>";
            echo "<p><strong>Short URL:</strong> <a href='{$result['share']['short_url']}' target='_blank'>{$result['share']['short_url']}</a></p>";
        } else {
            echo "<p>❌ Failed to create share: {$result['message']}</p>";
        }
    }
    
    // Check existing shares
    echo "<h2>Existing Shares</h2>";
    $sql = "SELECT ps.*, p.title as page_title, p.original_filename 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.is_active = 1 
            ORDER BY ps.created_at DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $shares = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($shares) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Page</th><th>Title</th><th>Share URL</th><th>Views</th><th>Created</th></tr>";
        foreach ($shares as $share) {
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                      '://' . $_SERVER['HTTP_HOST'] . 
                      rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
            $shareUrl = $baseUrl . '/view.php?token=' . $share['share_token'];
            
            echo "<tr>";
            echo "<td>{$share['id']}</td>";
            echo "<td>{$share['page_title']} ({$share['original_filename']})</td>";
            echo "<td>{$share['title']}</td>";
            echo "<td><a href='$shareUrl' target='_blank'>View Share</a></td>";
            echo "<td>{$share['view_count']}</td>";
            echo "<td>{$share['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No shares found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
