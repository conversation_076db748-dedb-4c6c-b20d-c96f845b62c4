# Authentication System Guide

## Overview

The Webpage Manager now includes a comprehensive authentication system with admin-controlled user registration, role-based access control, and secure session management. This guide covers all authentication features and how to use them.

## Features

### ✅ **Secure Authentication**
- Password hashing with <PERSON><PERSON>'s `password_hash()`
- Session management with timeout (1 hour)
- CSRF protection through session regeneration
- Secure logout with complete session cleanup

### ✅ **Admin-Controlled Registration**
- Only administrators can create new user accounts
- Role assignment during registration (admin, editor, viewer)
- Email and username uniqueness validation
- Password strength requirements (minimum 8 characters)

### ✅ **Role-Based Access Control**
- **Admin**: Full system access, user management, all operations
- **Editor**: Can edit and manage content, create shares, upload files
- **Viewer**: Read-only access to content and data

### ✅ **User Management Interface**
- Complete user CRUD operations
- User activation/deactivation
- Role modification
- Password reset functionality
- Activity tracking

### ✅ **Protected API Endpoints**
- All modification endpoints require authentication
- Admin-only operations are properly protected
- Graceful error handling for unauthorized access

## Getting Started

### 1. Initial Setup

1. **Access the login page**: `http://localhost/Fish/login.php`
2. **Set up admin password**: If this is the first time, you'll be prompted to set up the admin password
3. **Login as admin**: Use username `admin` and your chosen password

### 2. Default Credentials

- **Username**: `admin`
- **Password**: Set during first login (recommended: `admin123456` for testing)
- **Role**: Administrator

### 3. Accessing the System

#### Public Interface
- `index.html` - Main application (shows login prompt if not authenticated)

#### Protected Interface
- `admin.php` - Protected admin interface (requires authentication)
- `login.php` - Login page with admin setup
- `logout.php` - Logout handler

## User Management

### Creating Users

1. Login as an administrator
2. Navigate to the **Users** tab
3. Click **Create User**
4. Fill in the required information:
   - Username (3-50 characters, letters, numbers, underscores)
   - Email address
   - Full name
   - Password (minimum 8 characters)
   - Role (admin, editor, or viewer)
5. Click **Create User**

### Managing Users

#### Edit User
- Click the **Edit** button on any user card
- Modify user information
- Leave password field empty to keep current password
- Click **Update User**

#### Activate/Deactivate User
- Click the **Activate/Deactivate** button
- Inactive users cannot login
- Cannot deactivate your own account

#### Delete User
- Click the **Delete** button
- Confirm the deletion
- Cannot delete your own account
- This action is permanent

## Access Control

### Protected Operations

#### Authentication Required
- File uploads
- Page management (delete, archive)
- Form modifications
- Share creation and management
- Database operations

#### Admin Only
- User management
- Database cleanup and optimization
- System configuration
- User registration

#### Editor Privileges
- Upload and manage files
- Create and manage shares
- Modify forms and content
- View analytics and submissions

#### Viewer Privileges
- View pages and content
- Access analytics (read-only)
- View form submissions (read-only)

## API Endpoints

### Authentication Endpoints

```php
// Check authentication status
GET includes/auth_check.php
Response: {"authenticated": true, "user": {...}}

// User management (admin only)
GET includes/user_manager.php?action=get_users
POST includes/user_manager.php (create_user, update_user, delete_user, toggle_user_status)
```

### Protected Endpoints

All these endpoints now require authentication:

- `includes/upload.php` - File upload (editor+)
- `includes/page_manager.php` - Page operations (editor+)
- `includes/sharing_manager.php` - Share management (editor+)
- `includes/modify_form.php` - Form modifications (editor+)
- `includes/database_manager.php` - Database operations (admin only for modifications)

## Security Features

### Password Security
- Passwords are hashed using `PASSWORD_DEFAULT` algorithm
- No plain text password storage
- Password strength validation

### Session Security
- Session timeout after 1 hour of inactivity
- Session ID regeneration on login
- Secure session cleanup on logout
- Protection against session fixation

### Access Control
- Role-based permissions
- API endpoint protection
- Graceful error handling
- Activity logging

## Troubleshooting

### Common Issues

#### "Authentication required" errors
- Ensure you're logged in
- Check if your session has expired
- Try logging out and back in

#### "Insufficient privileges" errors
- Check your user role
- Contact an administrator for role changes
- Some operations require admin privileges

#### Cannot access Users tab
- Only administrators can see the Users tab
- Contact an administrator if you need user management access

#### Login issues
- Verify username and password
- Check if your account is active
- Contact an administrator if locked out

### Admin Recovery

If you lose admin access:

1. Access the database directly
2. Update the admin user's password hash
3. Or reset the password field to empty and use the setup process

```sql
-- Reset admin password (will prompt for setup)
UPDATE users SET password_hash = '' WHERE username = 'admin';
```

## File Structure

### Authentication Files
```
includes/
├── auth_manager.php      # Core authentication class
├── auth_check.php        # Authentication status API
├── user_manager.php      # User management API
login.php                 # Login interface
logout.php                # Logout handler
admin.php                 # Protected admin interface
```

### Database Tables
- `users` - User accounts and roles
- `activity_log` - User activity tracking (future feature)

## Best Practices

### For Administrators
1. Use strong passwords for all accounts
2. Regularly review user accounts and permissions
3. Deactivate unused accounts
4. Monitor user activity logs
5. Keep user roles minimal (principle of least privilege)

### For Users
1. Use strong, unique passwords
2. Log out when finished
3. Don't share login credentials
4. Report suspicious activity

### For Developers
1. Always check authentication before sensitive operations
2. Use role-based access control appropriately
3. Log important user actions
4. Handle authentication errors gracefully
5. Keep authentication code updated

## Integration Examples

### JavaScript Authentication Check
```javascript
// Check if user is authenticated
fetch('includes/auth_check.php')
    .then(response => response.json())
    .then(data => {
        if (data.authenticated) {
            console.log('User:', data.user);
            // Show authenticated UI
        } else {
            // Redirect to login
            window.location.href = 'login.php';
        }
    });
```

### PHP Authentication Check
```php
require_once 'includes/auth_manager.php';

$auth = new AuthManager();

// Require authentication
$auth->requireAuth('login.php');

// Require admin role
$auth->requireAdmin('login.php');

// Check specific permissions
if ($auth->canEdit()) {
    // Allow editing operations
}
```

## Support

For issues or questions about the authentication system:

1. Check this guide first
2. Review the error messages
3. Check the browser console for JavaScript errors
4. Verify database connectivity
5. Contact your system administrator

---

**Note**: This authentication system is designed for internal use and development environments. For production deployment, consider additional security measures such as HTTPS, rate limiting, and enhanced password policies.
