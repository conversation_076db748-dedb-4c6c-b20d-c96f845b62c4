<?php
/**
 * Test Enhanced Share Modal and Redirect Functionality
 * Verify the improved styling and redirect features work correctly
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Test Enhanced Share Modal & Redirect</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>1. Create Test Page for Enhanced Sharing</h2>";
    
    // Create uploads directory
    if (!is_dir('uploads/pages')) {
        mkdir('uploads/pages', 0755, true);
    }
    
    // Create a test page with multiple forms
    $testPageHtml = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Enhanced Share Test Page</title>
</head>
<body>
    <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <h1>Enhanced Share Test Page</h1>
        <p>This page demonstrates the enhanced share modal functionality with redirect capabilities.</p>
        
        <!-- Contact Form -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2>Contact Form</h2>
            <p>This form will use the redirect URL set in the share modal.</p>
            
            <form name="contact_form" method="POST">
                <div style="margin-bottom: 15px;">
                    <label for="name">Name *</label>
                    <input type="text" id="name" name="name" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label for="email">Email *</label>
                    <input type="email" id="email" name="email" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label for="message">Message *</label>
                    <textarea id="message" name="message" required rows="4" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                </div>
                
                <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                    Send Message
                </button>
            </form>
        </div>
        
        <!-- Newsletter Form -->
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2>Newsletter Signup</h2>
            <p>This form will also use the share redirect URL.</p>
            
            <form name="newsletter_form" method="POST">
                <div style="margin-bottom: 15px;">
                    <label for="newsletter_email">Email Address *</label>
                    <input type="email" id="newsletter_email" name="email" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label>
                        <input type="checkbox" name="marketing_consent" value="yes">
                        I agree to receive marketing emails
                    </label>
                </div>
                
                <button type="submit" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                    Subscribe
                </button>
            </form>
        </div>
        
        <!-- Survey Form -->
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2>Quick Survey</h2>
            <p>Help us improve by answering this quick survey.</p>
            
            <form name="survey_form" method="POST">
                <div style="margin-bottom: 15px;">
                    <label for="rating">How would you rate our service?</label>
                    <select id="rating" name="rating" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">Select rating</option>
                        <option value="excellent">Excellent</option>
                        <option value="good">Good</option>
                        <option value="average">Average</option>
                        <option value="poor">Poor</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label for="feedback">Additional Feedback</label>
                    <textarea id="feedback" name="feedback" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                </div>
                
                <button type="submit" style="background: #ffc107; color: #212529; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                    Submit Survey
                </button>
            </form>
        </div>
        
        <!-- Information Section -->
        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2>About This Test</h2>
            <p>This page is designed to test the enhanced share modal functionality:</p>
            <ul>
                <li><strong>Enhanced Styling:</strong> Beautiful gradient modal with improved UX</li>
                <li><strong>Redirect Functionality:</strong> Set a redirect URL in the share modal</li>
                <li><strong>Form Integration:</strong> All forms will redirect to the specified URL</li>
                <li><strong>Mobile Optimized:</strong> Responsive design for all devices</li>
            </ul>
        </div>
    </div>
</body>
</html>';
    
    $testFile = 'uploads/pages/enhanced_share_test_' . time() . '.html';
    file_put_contents($testFile, $testPageHtml);
    
    // Store page in database
    $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash, has_forms) 
            VALUES (?, ?, ?, ?, ?, ?, 1)";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        basename($testFile),
        'enhanced_share_test.html',
        'Enhanced Share Modal Test Page',
        $testFile,
        strlen($testPageHtml),
        hash('sha256', $testPageHtml)
    ]);
    
    $pageId = $db->lastInsertId();
    echo "<p>✅ Test page created with ID: $pageId</p>";
    
    echo "<h2>2. Test Enhanced Share Creation</h2>";
    
    // Create a share with redirect URL using the sharing manager
    require_once 'includes/sharing_manager.php';
    $sharingManager = new SharingManager($db);
    
    $shareOptions = [
        'title' => 'Enhanced Share Modal Test',
        'description' => 'Testing the enhanced share modal with beautiful styling and redirect functionality',
        'show_forms' => true,
        'show_metadata' => true,
        'allow_download' => false,
        'redirect_url' => 'https://example.com/thank-you-enhanced'
    ];
    
    $shareResult = $sharingManager->createShare($pageId, $shareOptions);
    
    if ($shareResult['success']) {
        $share = $shareResult['share'];
        echo "<p>✅ Enhanced share created successfully</p>";
        echo "<p><strong>Share ID:</strong> {$share['id']}</p>";
        echo "<p><strong>Share Token:</strong> {$share['share_token']}</p>";
        echo "<p><strong>Short Code:</strong> {$share['short_code']}</p>";
        echo "<p><strong>Redirect URL:</strong> {$share['redirect_url']}</p>";
    } else {
        echo "<p>❌ Failed to create share: {$shareResult['message']}</p>";
    }
    
    echo "<h2>3. Enhanced Modal Features</h2>";
    
    echo "<div style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 12px; margin: 20px 0;'>";
    echo "<h3>🎨 Enhanced Share Modal Features</h3>";
    echo "<ul>";
    echo "<li><strong>Beautiful Gradient Design:</strong> Modern purple gradient background</li>";
    echo "<li><strong>Glass Morphism Effect:</strong> Semi-transparent modal with backdrop blur</li>";
    echo "<li><strong>Organized Sections:</strong> Grouped form fields with icons and descriptions</li>";
    echo "<li><strong>Enhanced Checkboxes:</strong> Custom styled checkboxes with descriptions</li>";
    echo "<li><strong>Redirect URL Field:</strong> Dedicated field for post-submission redirects</li>";
    echo "<li><strong>Responsive Design:</strong> Optimized for mobile and desktop</li>";
    echo "<li><strong>Smooth Animations:</strong> Fade-in and slide-in effects</li>";
    echo "<li><strong>Better Typography:</strong> Improved fonts and spacing</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>4. Redirect Functionality Test</h2>";
    
    if (isset($share)) {
        $shareUrl = "http://{$_SERVER['HTTP_HOST']}" . rtrim(dirname($_SERVER['SCRIPT_NAME']), '/') . "/view.php?token={$share['share_token']}";
        
        echo "<div style='background: #e7f3ff; color: #004085; padding: 20px; border-radius: 8px;'>";
        echo "<h4>🔗 Test Share URL</h4>";
        echo "<p><strong>Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
        echo "<p><strong>Redirect URL:</strong> {$share['redirect_url']}</p>";
        echo "<p><strong>Instructions:</strong></p>";
        echo "<ol>";
        echo "<li>Click the share URL above</li>";
        echo "<li>Fill out any of the forms on the page</li>";
        echo "<li>Submit the form</li>";
        echo "<li>You should be redirected to: {$share['redirect_url']}</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    echo "<h2>5. How to Use Enhanced Share Modal</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007cba;'>";
    echo "<h4>📖 Usage Instructions</h4>";
    echo "<ol>";
    echo "<li><strong>Open Main Application:</strong> <a href='index.html' target='_blank'>Go to index.html</a></li>";
    echo "<li><strong>Navigate to Share Tab:</strong> Click on the Share tab</li>";
    echo "<li><strong>Click Create Share:</strong> Click the 'Create New Share' button</li>";
    echo "<li><strong>Experience Enhanced Modal:</strong> Notice the beautiful new design</li>";
    echo "<li><strong>Fill Form Sections:</strong>";
    echo "<ul>";
    echo "<li><strong>Page Selection:</strong> Choose the page to share</li>";
    echo "<li><strong>Share Information:</strong> Add title and description</li>";
    echo "<li><strong>Form Behavior:</strong> Set redirect URL for form submissions</li>";
    echo "<li><strong>Security & Access:</strong> Configure password and expiration</li>";
    echo "<li><strong>Advanced Options:</strong> Enable download and metadata</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Create Share:</strong> Click the enhanced 'Create Share' button</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>6. Styling Improvements Summary</h2>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
    echo "<h4>✨ Visual Enhancements</h4>";
    echo "<ul>";
    echo "<li><strong>Modal Background:</strong> Beautiful gradient with enhanced backdrop blur</li>";
    echo "<li><strong>Form Sections:</strong> Organized groups with icons and hover effects</li>";
    echo "<li><strong>Input Fields:</strong> Enhanced styling with focus states and transitions</li>";
    echo "<li><strong>Buttons:</strong> Gradient buttons with shadow effects and animations</li>";
    echo "<li><strong>Checkboxes:</strong> Custom styled with descriptions and hover states</li>";
    echo "<li><strong>Typography:</strong> Improved fonts, spacing, and hierarchy</li>";
    echo "<li><strong>Responsive:</strong> Mobile-optimized layout and interactions</li>";
    echo "<li><strong>Animations:</strong> Smooth transitions and micro-interactions</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>7. Technical Implementation</h2>";
    
    echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px;'>";
    echo "<h4>🔧 Technical Details</h4>";
    echo "<ul>";
    echo "<li><strong>Database:</strong> Added redirect_url column to page_shares table</li>";
    echo "<li><strong>Frontend:</strong> Enhanced HTML structure with organized sections</li>";
    echo "<li><strong>CSS:</strong> Added comprehensive styling for enhanced modal</li>";
    echo "<li><strong>JavaScript:</strong> Updated to handle redirect URL field</li>";
    echo "<li><strong>Backend:</strong> Modified sharing manager to store redirect URLs</li>";
    echo "<li><strong>View System:</strong> Updated to use share redirect URLs</li>";
    echo "<li><strong>Form Handler:</strong> Enhanced to process redirect URLs</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
    echo "<h2>❌ Test Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h2>Summary</h2>";
echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🎉 Enhanced Share Modal Complete!</h3>";
echo "<ul>";
echo "<li>✅ Beautiful modern styling with gradients and glass morphism</li>";
echo "<li>✅ Organized form sections with icons and descriptions</li>";
echo "<li>✅ Redirect URL functionality for form submissions</li>";
echo "<li>✅ Enhanced checkboxes and input styling</li>";
echo "<li>✅ Mobile-responsive design</li>";
echo "<li>✅ Smooth animations and transitions</li>";
echo "<li>✅ Professional typography and spacing</li>";
echo "<li>✅ Database integration for redirect URLs</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='index.html'>Main Application (Test Enhanced Modal)</a></li>";
echo "<li><a href='update_database_for_redirect.php'>Database Update Status</a></li>";
echo "<li><a href='test_enhanced_styling.php'>Enhanced Styling Test</a></li>";
echo "</ul>";

?>

<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
</style>
