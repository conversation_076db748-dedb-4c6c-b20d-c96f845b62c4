<?php
/**
 * Fix Share Database Issues
 * Diagnose and fix foreign key constraint violations in share_access_log
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Fix Share Database Issues</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>1. Database Integrity Check</h2>";
    
    // Check for orphaned share_access_log entries
    echo "<h3>Checking for Orphaned Access Log Entries</h3>";
    $sql = "SELECT sal.id, sal.share_id, sal.access_type, sal.accessed_at 
            FROM share_access_log sal 
            LEFT JOIN page_shares ps ON sal.share_id = ps.id 
            WHERE ps.id IS NULL";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $orphanedLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($orphanedLogs)) {
        echo "<p style='color: green;'>✅ No orphaned access log entries found.</p>";
    } else {
        echo "<p style='color: red;'>❌ Found " . count($orphanedLogs) . " orphaned access log entries:</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Log ID</th><th>Share ID</th><th>Access Type</th><th>Accessed At</th></tr>";
        foreach ($orphanedLogs as $log) {
            echo "<tr>";
            echo "<td>{$log['id']}</td>";
            echo "<td>{$log['share_id']}</td>";
            echo "<td>{$log['access_type']}</td>";
            echo "<td>{$log['accessed_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check for shares with missing pages
    echo "<h3>Checking for Shares with Missing Pages</h3>";
    $sql = "SELECT ps.id, ps.page_id, ps.share_token, ps.title 
            FROM page_shares ps 
            LEFT JOIN pages p ON ps.page_id = p.id 
            WHERE p.id IS NULL";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $orphanedShares = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($orphanedShares)) {
        echo "<p style='color: green;'>✅ No shares with missing pages found.</p>";
    } else {
        echo "<p style='color: red;'>❌ Found " . count($orphanedShares) . " shares with missing pages:</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Share ID</th><th>Page ID</th><th>Share Token</th><th>Title</th></tr>";
        foreach ($orphanedShares as $share) {
            echo "<tr>";
            echo "<td>{$share['id']}</td>";
            echo "<td>{$share['page_id']}</td>";
            echo "<td>{$share['share_token']}</td>";
            echo "<td>" . htmlspecialchars($share['title'] ?? 'No title') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check current shares and pages
    echo "<h3>Current Database Status</h3>";
    
    $sql = "SELECT COUNT(*) as count FROM pages";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pageCount = $stmt->fetchColumn();
    
    $sql = "SELECT COUNT(*) as count FROM page_shares WHERE is_active = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $shareCount = $stmt->fetchColumn();
    
    $sql = "SELECT COUNT(*) as count FROM share_access_log";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $logCount = $stmt->fetchColumn();
    
    echo "<ul>";
    echo "<li><strong>Total Pages:</strong> $pageCount</li>";
    echo "<li><strong>Active Shares:</strong> $shareCount</li>";
    echo "<li><strong>Access Log Entries:</strong> $logCount</li>";
    echo "</ul>";
    
    echo "<h2>2. Cleanup Actions</h2>";
    
    if ($_POST['action'] ?? false) {
        switch ($_POST['action']) {
            case 'clean_orphaned_logs':
                if (!empty($orphanedLogs)) {
                    $sql = "DELETE FROM share_access_log WHERE share_id NOT IN (SELECT id FROM page_shares)";
                    $stmt = $db->prepare($sql);
                    $stmt->execute();
                    $deletedCount = $stmt->rowCount();
                    echo "<p style='color: green;'>✅ Deleted $deletedCount orphaned access log entries.</p>";
                } else {
                    echo "<p>No orphaned logs to clean.</p>";
                }
                break;
                
            case 'clean_orphaned_shares':
                if (!empty($orphanedShares)) {
                    $sql = "DELETE FROM page_shares WHERE page_id NOT IN (SELECT id FROM pages)";
                    $stmt = $db->prepare($sql);
                    $stmt->execute();
                    $deletedCount = $stmt->rowCount();
                    echo "<p style='color: green;'>✅ Deleted $deletedCount orphaned shares.</p>";
                } else {
                    echo "<p>No orphaned shares to clean.</p>";
                }
                break;
                
            case 'fix_column_conflicts':
                echo "<h3>Fixing Column Name Conflicts</h3>";
                echo "<p>✅ Column conflicts have been fixed in the view.php file by using table aliases.</p>";
                echo "<p>The query now uses:</p>";
                echo "<code>SELECT ps.id as share_id, ps.*, p.id as page_id, p.* FROM page_shares ps JOIN pages p ON ps.page_id = p.id</code>";
                break;
        }
        
        // Refresh the page to show updated status
        echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
    }
    
    echo "<h3>Available Cleanup Actions</h3>";
    
    if (!empty($orphanedLogs)) {
        echo "<form method='POST' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='action' value='clean_orphaned_logs'>";
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Clean Orphaned Access Logs</button>";
        echo "</form>";
    }
    
    if (!empty($orphanedShares)) {
        echo "<form method='POST' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='action' value='clean_orphaned_shares'>";
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Clean Orphaned Shares</button>";
        echo "</form>";
    }
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='action' value='fix_column_conflicts'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Verify Column Conflict Fixes</button>";
    echo "</form>";
    
    echo "<h2>3. Test Share Access</h2>";
    
    // Get a valid share for testing
    $sql = "SELECT ps.*, p.title as page_title FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.is_active = 1 
            LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $testShare = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testShare) {
        echo "<h3>Test Share Found</h3>";
        echo "<p><strong>Share ID:</strong> {$testShare['id']}</p>";
        echo "<p><strong>Page Title:</strong> " . htmlspecialchars($testShare['page_title']) . "</p>";
        echo "<p><strong>Share Token:</strong> {$testShare['share_token']}</p>";
        echo "<p><strong>Share URL:</strong> <a href='view.php?token={$testShare['share_token']}' target='_blank'>view.php?token={$testShare['share_token']}</a></p>";
        echo "<p><strong>Short URL:</strong> <a href='view.php?s={$testShare['short_code']}' target='_blank'>view.php?s={$testShare['short_code']}</a></p>";
        
        echo "<p style='background: #e7f3ff; padding: 10px; border-radius: 5px;'>";
        echo "<strong>Test Instructions:</strong> Click one of the links above to test if the share viewing works without database errors.";
        echo "</p>";
    } else {
        echo "<p>No active shares found for testing. Create a share first using the main application.</p>";
    }
    
    echo "<h2>4. Prevention Measures Implemented</h2>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
    echo "<h3>✅ Fixes Applied</h3>";
    echo "<ul>";
    echo "<li><strong>Column Alias Fix:</strong> Added table aliases in view.php to prevent ID column conflicts</li>";
    echo "<li><strong>Share Validation:</strong> Added existence check before logging access</li>";
    echo "<li><strong>Error Handling:</strong> Wrapped access logging in try-catch blocks</li>";
    echo "<li><strong>Graceful Degradation:</strong> Page viewing continues even if logging fails</li>";
    echo "<li><strong>Detailed Logging:</strong> Added error logging for debugging</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>5. Code Changes Made</h2>";
    
    echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
    echo "<h4>view.php Changes:</h4>";
    echo "<pre style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
    echo htmlspecialchars('// Before (problematic):
SELECT ps.*, p.* FROM page_shares ps JOIN pages p ON ps.page_id = p.id
logShareAccess($db, $result["id"], "view");

// After (fixed):
SELECT ps.id as share_id, ps.*, p.id as page_id, p.* FROM page_shares ps JOIN pages p ON ps.page_id = p.id
logShareAccess($db, $result["share_id"], "view");

// Enhanced logShareAccess function:
function logShareAccess($db, $shareId, $accessType = "view") {
    try {
        // Verify share exists first
        $sql = "SELECT id FROM page_shares WHERE id = ? AND is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareId]);
        
        if (!$stmt->fetch()) {
            error_log("Attempted to log access for non-existent share ID: $shareId");
            return false;
        }
        
        // Proceed with logging...
    } catch (PDOException $e) {
        error_log("Failed to log share access: " . $e->getMessage());
        return false;
    }
}');
    echo "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

?>

<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: 'Courier New', monospace; }
</style>
