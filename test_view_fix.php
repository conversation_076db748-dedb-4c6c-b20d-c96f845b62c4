<?php
/**
 * Test the fixed view.php functionality
 */

echo "<h1>Testing Fixed View.php</h1>";

try {
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Get existing shares
    $sql = "SELECT ps.*, p.title as page_title, p.original_filename 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.is_active = 1 
            ORDER BY ps.created_at DESC 
            LIMIT 5";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $shares = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($shares) > 0) {
        echo "<h2>Available Shares to Test</h2>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Page</th><th>Title</th><th>Share URL</th><th>Test Link</th></tr>";
        
        foreach ($shares as $share) {
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                      '://' . $_SERVER['HTTP_HOST'] . 
                      rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
            $shareUrl = $baseUrl . '/view.php?token=' . $share['share_token'];
            
            echo "<tr>";
            echo "<td>{$share['page_title']} ({$share['original_filename']})</td>";
            echo "<td>{$share['title']}</td>";
            echo "<td><code>" . htmlspecialchars($shareUrl) . "</code></td>";
            echo "<td><a href='$shareUrl' target='_blank'>Test Share</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>Test Instructions</h2>";
        echo "<ol>";
        echo "<li>Click on any 'Test Share' link above</li>";
        echo "<li>The page should load without PHP errors</li>";
        echo "<li>If the page has forms, they should be enhanced for submission</li>";
        echo "<li>Try filling out and submitting a form</li>";
        echo "<li>Check the database tab in the main app for submissions</li>";
        echo "</ol>";
        
    } else {
        echo "<p>⚠️ No shares found. Creating a test share...</p>";
        
        // Check if we have any pages
        $sql = "SELECT * FROM pages LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $page = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$page) {
            echo "<p>❌ No pages found. Please upload a page first using the main application.</p>";
        } else {
            // Create a test share
            require_once 'includes/sharing_manager.php';
            
            $manager = new SharingManager();
            $result = $manager->createShare($page['id'], [
                'title' => 'Test Share - ' . ($page['title'] ?: $page['original_filename']),
                'description' => 'Test share created by fix verification script',
                'show_forms' => true
            ]);
            
            if ($result['success']) {
                echo "<p>✅ Test share created successfully!</p>";
                echo "<p><strong>Share URL:</strong> <a href='{$result['share']['share_url']}' target='_blank'>{$result['share']['share_url']}</a></p>";
                echo "<p>Click the link above to test the fixed sharing functionality.</p>";
            } else {
                echo "<p>❌ Failed to create test share: {$result['message']}</p>";
            }
        }
    }
    
    echo "<h2>Error Checking</h2>";
    echo "<p>The following errors have been fixed in view.php:</p>";
    echo "<ul>";
    echo "<li>✅ Undefined variable \$page</li>";
    echo "<li>✅ Null values passed to setAttribute()</li>";
    echo "<li>✅ Headers already sent warnings</li>";
    echo "<li>✅ Proper error handling and output buffering</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Quick Links</h2>";
echo "<p><a href='index.html'>Main Application</a></p>";
echo "<p><a href='test_sharing.php'>Sharing System Test</a></p>";
echo "<p><a href='init_database.php'>Database Initialization</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
</style>
