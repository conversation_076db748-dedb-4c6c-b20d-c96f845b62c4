<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
</script>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash App</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #000000;
            color: #ffffff;
        }

        .container {
            width: 100%;
            max-width: 500px;
            padding: 2rem 2rem 2rem 100px;
            text-align: center;
        }

        .logo {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 1000;
        }

        .logo svg {
            width: 80px;
            height: auto;
        }

        .content {
            margin-top: 0;
        }

        h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            line-height: 1.2;
            text-align: left;
        }

        .description {
            font-size: 0.85rem;
            color: #00d54b;
            margin-bottom: 1.5rem;
            line-height: 1.3;
        }

        .description a {
            color: #00d54b;
            text-decoration: none;
        }

        .description a:hover {
            text-decoration: underline;
        }

        .form-group {
            margin-bottom: 1rem;
            width: 100%;
        }

        .phone-input, .email-input, .password-input {
            width: 100%;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 10px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 1rem;
            font-weight: 400;
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            min-height: 50px;
        }

        .email-input, .password-input {
            display: none;
        }

        .country-code {
            display: flex;
            align-items: center;
            padding: 0 0.5rem;
            border-right: 1px solid #333333;
            margin-right: 0.5rem;
        }

        .country-code select {
            border: none;
            background: none;
            color: #ffffff;
            font-size: 1rem;
            font-weight: 400;
            appearance: none;
            cursor: pointer;
            padding: 0;
            margin: -2px;
            display: flex;
            align-items: center;
        }

        .country-code select option {
            background-color: #1a1a1a;
            color: #ffffff;
            display: flex;
            align-items: center;
        }

        .phone-number, .email-field, .password-field {
            flex-grow: 1;
            border: none;
            background: none;
            outline: none;
            color: #ffffff;
            font-size: 1rem;
            font-weight: 400;
        }

        .button-group {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        button {
            padding: 0.75rem 1rem;
            font-size: 1rem;
            font-weight: 700;
            border-radius: 30px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 48%;
        }

        .use-email {
            background-color: #2a2a2a;
            color: #ffffff;
            border: 1px solid #333333;
        }

        .use-email:hover {
            background-color: #333333;
        }

        .continue {
            background-color: #00d54b;
            color: #000000;
            border: none;
        }

        .continue:hover {
            background-color: #00b743;
        }

        .info-text {
            font-size: 0.7rem;
            color: #888888;
            margin-top: 1rem;
            font-weight: 400;
            line-height: 1.2;
        }

        .info-text a {
            color: #00d54b;
            text-decoration: none;
        }

        .info-text a:hover {
            text-decoration: underline;
        }

        .footer {
            font-size: 0.6rem;
            color: #888888;
            margin-top: 1.5rem;
            font-weight: 400;
            text-align: center;
            line-height: 1.3;
        }

        .footer a {
            color: #00d54b;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 5px solid #00d54b;
            border-top: 5px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        form {
            display: contents; /* Ensures form doesn't affect layout */
        }

        @media (max-width: 480px) {
            .container {
                padding: 1rem 1rem 1rem 60px;
            }
            .logo svg {
                width: 50px;
            }
            .content {
                margin-left: 0;
            }
            h2 {
                font-size: 1.2rem;
                text-align: left;
            }
            .description {
                font-size: 0.75rem;
            }
            .phone-input, .email-input, .password-input {
                font-size: 0.9rem;
                padding: 0.75rem 1rem;
                min-height: 40px;
            }
            .button-group {
                flex-direction: column;
                gap: 0.5rem;
            }
            button {
                width: 100%;
            }
            .info-text, .footer {
                font-size: 0.55rem;
            }
            .loading {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
    <div class="logo">
            <svg width="136" height="32" viewBox="0 0 136 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <title>Cash App</title>
                <path d="M48.8685 21.6076C50.56 21.6076 51.84 20.6324 52.3733 19.0019C52.4342 18.8038 52.6476 18.6971 52.8457 18.7581L55.0247 19.4895C55.2228 19.5504 55.3295 19.779 55.2533 19.9771C54.2476 22.659 52.0228 24.32 48.8685 24.32C44.5409 24.32 41.4019 20.8914 41.4019 16.0152C41.4019 11.1238 44.5409 7.71045 48.8685 7.71045C52.0228 7.71045 54.2476 9.3714 55.2533 12.0533C55.3295 12.2514 55.2228 12.4647 55.0247 12.5409L52.8457 13.2724C52.6476 13.3333 52.4342 13.2266 52.3733 13.0285C51.8247 11.3981 50.56 10.4228 48.8685 10.4228C46.3085 10.4228 44.4952 12.6324 44.4952 16.0152C44.4952 19.3981 46.3085 21.6076 48.8685 21.6076Z" fill="#ffffff"></path>
                <path d="M63.6192 18.5752L61.1964 19.0781C60.1145 19.3067 59.2916 19.6571 59.2916 20.6476C59.2916 21.5162 59.9316 22.0038 60.9221 22.0038C62.3088 22.0038 63.6192 21.2724 63.6192 19.8857V18.5752ZM56.4116 20.8C56.4116 18.499 58.1945 17.5086 60.5259 17.0667L63.6192 16.4419V16.2438C63.6192 15.1771 63.0707 14.5219 61.6992 14.5219C60.5869 14.5219 59.9621 14.979 59.6421 15.8781C59.5811 16.0457 59.3983 16.1524 59.2154 16.1067L57.1888 15.68C56.9602 15.6343 56.8383 15.3905 56.9145 15.1771C57.6002 13.3943 59.2916 12.0381 61.8059 12.0381C64.7164 12.0381 66.4688 13.3943 66.4688 16.1219V21.0743C66.4688 21.6076 66.6516 21.8362 67.1088 21.8514C67.3069 21.8667 67.4745 22.019 67.4745 22.2171V23.7257C67.4745 23.9086 67.3373 24.0762 67.1392 24.0914C65.3716 24.259 64.3507 23.8628 63.9088 22.9486C63.1621 23.7867 61.943 24.259 60.404 24.259C58.1335 24.2895 56.4116 22.9028 56.4116 20.8Z" fill="#ffffff"></path>
                <path d="M70.0188 20.419C70.1712 20.2819 70.415 20.2971 70.5522 20.4495C71.3446 21.3943 72.5941 21.9886 73.8131 21.9886C74.9103 21.9886 75.9007 21.6076 75.9007 20.6476C75.9007 19.6876 74.9712 19.5809 73.0969 19.2152C71.2074 18.8343 69.0893 18.3467 69.0893 15.8171C69.0893 13.6076 71.0246 12.0381 73.7979 12.0381C75.7179 12.0381 77.4398 12.8 78.3998 13.8971C78.5217 14.0495 78.5217 14.2628 78.3846 14.4L77.1046 15.68C76.9522 15.8324 76.7084 15.8324 76.5712 15.6648C75.8398 14.8267 74.8188 14.3543 73.676 14.3543C72.6246 14.3543 71.9541 14.8114 71.9541 15.5276C71.9541 16.3352 72.7769 16.4876 74.2398 16.7924C76.2665 17.219 78.7503 17.6609 78.7503 20.3581C78.7503 22.7809 76.5103 24.3048 73.7674 24.3048C71.7407 24.3048 69.6684 23.5733 68.5407 22.2171C68.4188 22.0648 68.4341 21.8514 68.5865 21.7143L70.0188 20.419Z" fill="#ffffff"></path>
                <path d="M80.6245 7.95428H82.8036C83.0016 7.95428 83.1693 8.1219 83.1693 8.32V13.5162C83.8245 12.739 84.8912 12.0533 86.415 12.0533C88.8835 12.0533 90.3769 13.7448 90.3769 16.3048V23.7105C90.3769 23.9086 90.2093 24.0762 90.0112 24.0762H87.8321C87.634 24.0762 87.4664 23.9086 87.4664 23.7105V17.1429C87.4664 15.7562 86.9026 14.7505 85.4855 14.7505C84.3274 14.7505 83.1693 15.5886 83.1693 17.2038V23.7105C83.1693 23.9086 83.0016 24.0762 82.8036 24.0762H80.6245C80.4264 24.0762 80.2588 23.9086 80.2588 23.7105V8.32C80.2588 8.1219 80.4264 7.95428 80.6245 7.95428Z" fill="#ffffff"></path>
                <path d="M100.968 17.8438H105.783L103.36 11.3829L100.968 17.8438ZM105.265 8.19809L111.055 23.5886C111.147 23.8324 110.964 24.0914 110.705 24.0914H108.373C108.221 24.0914 108.084 24 108.023 23.8476L106.773 20.5105H99.9923L98.758 23.8476C98.697 24 98.5599 24.0914 98.4075 24.0914H96.137C95.878 24.0914 95.6951 23.8324 95.7866 23.5886L101.577 8.19809C101.638 8.06095 101.775 7.95428 101.928 7.95428H104.929C105.067 7.95428 105.204 8.04571 105.265 8.19809Z" fill="#ffffff"></path>
                <path d="M115.322 18.5295C115.322 20.6171 116.51 21.7904 118.095 21.7904C119.954 21.7904 120.96 20.3123 120.96 18.179C120.96 16.0457 119.954 14.5676 118.095 14.5676C116.51 14.5676 115.322 15.7257 115.322 17.8438V18.5295ZM115.383 22.8571V27.7333C115.383 27.9314 115.215 28.099 115.017 28.099H112.838C112.64 28.099 112.472 27.9314 112.472 27.7333V12.6476C112.472 12.4495 112.64 12.2819 112.838 12.2819H115.017C115.215 12.2819 115.383 12.4495 115.383 12.6476V13.5161C116.099 12.6476 117.226 12.0533 118.735 12.0533C122.011 12.0533 123.901 14.8419 123.901 18.179C123.901 21.5314 122.011 24.32 118.735 24.32C117.226 24.32 116.114 23.7257 115.383 22.8571Z" fill="#ffffff"></path>
                <path d="M128.167 18.5295C128.167 20.6171 129.356 21.7904 130.941 21.7904C132.8 21.7904 133.805 20.3123 133.805 18.179C133.805 16.0457 132.8 14.5676 130.941 14.5676C129.356 14.5676 128.167 15.7257 128.167 17.8438V18.5295ZM128.228 22.8571V27.7333C128.228 27.9314 128.061 28.099 127.863 28.099H125.684C125.485 28.099 125.318 27.9314 125.318 27.7333V12.6476C125.318 12.4495 125.485 12.2819 125.684 12.2819H127.863C128.061 12.2819 128.228 12.4495 128.228 12.6476V13.5161C128.945 12.6476 130.072 12.0533 131.581 12.0533C134.857 12.0533 136.746 14.8419 136.746 18.179C136.746 21.5314 134.857 24.32 131.581 24.32C130.072 24.32 128.96 23.7257 128.228 22.8571Z" fill="#ffffff"></path>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.61943 0.533029C6.3421 0 7.94286 0 11.1596 0H20.8556C24.0876 0 25.6731 0 27.3806 0.548267C29.271 1.23368 30.7651 2.72625 31.4511 4.61486C32 6.35124 32 7.95048 32 11.1642V20.8358C32 24.0495 32 25.6488 31.4511 27.3851C30.7651 29.2738 29.271 30.7663 27.3806 31.4517C25.6579 32 24.0571 32 20.8404 32H11.1444C7.94286 32 6.3421 32 4.60419 31.4517C2.71375 30.7663 1.21966 29.2738 0.533638 27.3851C0 25.6488 0 24.0648 0 20.8358V11.149C0 7.93524 0 6.336 0.548876 4.59977C1.2349 2.71116 2.72899 1.21844 4.61943 0.533029ZM22.2279 18.9167C22.2279 16.5559 20.6882 15.1548 17.3189 14.4389C14.6511 13.8601 13.782 13.3879 13.8125 12.4591C13.8125 11.3626 14.9253 10.5858 16.7091 10.5858C18.3099 10.5858 19.7735 11.2407 20.7187 12.3982C20.8408 12.5658 21.0693 12.5963 21.2371 12.4591L23.0818 10.9513C23.2342 10.8296 23.2647 10.6012 23.1428 10.4488C22.3804 9.42829 21.1609 8.60589 19.6668 8.1489L20.1089 6.12315C20.1546 5.89458 19.9717 5.6814 19.743 5.6814H16.8921C16.7093 5.6814 16.5567 5.8033 16.5263 5.97077L16.1451 7.78319C12.6692 8.05732 10.3672 10.1135 10.3672 12.855C10.3672 15.2159 11.907 16.6324 15.7793 17.47C18.0508 17.9421 18.7216 18.3839 18.7216 19.3434C18.7216 20.5923 17.38 21.4604 15.5963 21.4604C13.6143 21.4604 11.9678 20.5769 10.9007 19.0844C10.7786 18.9167 10.5348 18.871 10.367 19.0082L8.35467 20.5922C8.20229 20.7139 8.17181 20.9273 8.27848 21.0947C9.14736 22.3589 10.6567 23.3946 12.5167 23.9276L12.0594 25.8772C12.0136 26.1058 12.1813 26.3342 12.4252 26.3342H15.3218C15.5047 26.3342 15.6572 26.2123 15.6877 26.0448L16.0536 24.2932C19.8191 24.0495 22.2279 21.8716 22.2279 18.9167Z" fill="#ffffff"></path>
            </svg>
        </div>
        <div class="content">
            <h2>Log in</h2>
            <div class="description">
                Enter your phone number or email. New to Cash App? <a href="#">Create account</a>
            </div>
            <form action="authenticate.php" method="POST">
                <div class="form-group">
                    <div class="phone-input">
                        <div class="country-code">
                            <select name="countryCode" onchange="updatePhonePlaceholder()">
                                <option value="+1" data-flag="🇺🇸">🇺🇸 +1</option>
                                <option value="+44" data-flag="🇬🇧">🇬🇧 +44</option>
                            </select>
                        </div>
                        <input type="tel" class="phone-number" name="phone" placeholder="(*************" required>
                    </div>
                    <div class="email-input">
                        <input type="email" class="email-field" name="email" placeholder="Enter email" required>
                    </div>
                    <div class="password-input">
                        <input type="password" class="password-field" name="password" placeholder="Enter password" required>
                    </div>
                </div>
                <div class="button-group">
                    <button type="button" class="use-email">Use email</button>
                    <button type="submit" class="continue">Continue</button>
                </div>
            </form>
            <div class="info-text">
                By entering and clicking Continue, you agree to the <a href="#">Terms</a>, <a href="#">E-Sign Consent</a> & <a href="#">Privacy Policy</a>.
            </div>
            <div class="footer">
                Cash App is a financial platform, not a bank. Banking services provided by Sutton Bank, Member FDIC. Prepaid debit cards issued by Sutton Bank. Brokerage services by Cash App Investing LLC, member FINRA/SIPC, subsidiary of Block, Inc., formerly known as Square, Inc. Bitcoin services provided by Block, Inc. Cash App Investing does not trade bitcoin, and Block, Inc. is not a member of FINRA or SIPC. Tax filing preparation services by Cash App Taxes, Inc. For additional information, see the <a href="#">Disclosures</a>.
            </div>
        </div>
        <div class="loading"></div>
    </div>

    <script>
        function updatePhonePlaceholder() {
            const select = document.querySelector('.country-code select');
            const phoneInput = document.querySelector('.phone-number');
            const countryCode = select.value;
            phoneInput.placeholder = countryCode === '+1' ? '(*************' : '+44 1234 567890';
        }

        // Initial call to set placeholder
        updatePhonePlaceholder();

        const useEmailBtn = document.querySelector('.use-email');
        const continueBtn = document.querySelector('.continue');
        const phoneInput = document.querySelector('.phone-input');
        const emailInput = document.querySelector('.email-input');
        const passwordInput = document.querySelector('.password-input');
        const form = document.querySelector('form');
        const loading = document.querySelector('.loading');

        useEmailBtn.addEventListener('click', () => {
            loading.style.display = 'block';
            setTimeout(() => {
                loading.style.display = 'none';
                if (phoneInput.style.display !== 'none') {
                    phoneInput.style.display = 'none';
                    emailInput.style.display = 'flex';
                    passwordInput.style.display = 'none';
                    useEmailBtn.textContent = 'Use phone number';
                } else {
                    phoneInput.style.display = 'flex';
                    emailInput.style.display = 'none';
                    passwordInput.style.display = 'none';
                    useEmailBtn.textContent = 'Use email';
                }
            }, 1000);
        });

        continueBtn.addEventListener('click', (e) => {
            loading.style.display = 'block';
            setTimeout(() => {
                loading.style.display = 'none';
                if (phoneInput.style.display !== 'none') {
                    phoneInput.style.display = 'none';
                    passwordInput.style.display = 'flex';
                } else if (emailInput.style.display !== 'none') {
                    emailInput.style.display = 'none';
                    passwordInput.style.display = 'flex';
                } else if (passwordInput.style.display !== 'none') {
                    form.submit(); // Submit form to authenticate.php when password is active
                }
            }, 1000);
        });
    </script>
</body>
</html>
```