// Main Application JavaScript
class WebpageManager {
    constructor() {
        this.init();
    }

    init() {
        this.checkAuthentication();
        this.setupEventListeners();
        this.loadPages();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = btn.dataset.tab || btn.getAttribute('data-tab');
                if (tabName) {
                    this.switchTab(tabName);
                }
            });
        });

        // File upload
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');

        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
            uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
            uploadArea.addEventListener('drop', this.handleDrop.bind(this));
            fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        }

        // Search functionality
        const searchPages = document.getElementById('search-pages');
        const refreshPages = document.getElementById('refresh-pages');
        if (searchPages) searchPages.addEventListener('input', this.searchPages.bind(this));
        if (refreshPages) refreshPages.addEventListener('click', this.loadPages.bind(this));

        // Page management
        const statusFilter = document.getElementById('status-filter');
        const projectFilter = document.getElementById('project-filter');
        const selectAllPages = document.getElementById('select-all-pages');
        const bulkActionsBtn = document.getElementById('bulk-actions-btn');
        if (statusFilter) statusFilter.addEventListener('change', this.loadPages.bind(this));
        if (projectFilter) projectFilter.addEventListener('change', this.loadPages.bind(this));
        if (selectAllPages) selectAllPages.addEventListener('click', this.toggleSelectAll.bind(this));
        if (bulkActionsBtn) bulkActionsBtn.addEventListener('click', this.toggleBulkActions.bind(this));

        // Delete modal
        const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
        if (confirmDeleteBtn) confirmDeleteBtn.addEventListener('click', this.confirmDelete.bind(this));

        // Database actions
        const generateDb = document.getElementById('generate-db');
        const exportSql = document.getElementById('export-sql');
        if (generateDb) generateDb.addEventListener('click', this.generateDatabase.bind(this));
        if (exportSql) exportSql.addEventListener('click', this.exportSQL.bind(this));

        // Sharing actions
        const createShare = document.getElementById('create-share');
        const createShortUrl = document.getElementById('create-short-url');
        const refreshShares = document.getElementById('refresh-shares');
        if (createShare) createShare.addEventListener('click', this.showShareModal.bind(this));
        if (createShortUrl) createShortUrl.addEventListener('click', this.showUrlModal.bind(this));
        if (refreshShares) refreshShares.addEventListener('click', this.loadShares.bind(this));

        // User management actions (will be set up after auth check)
        this.setupUserManagementListeners();

        // Share form
        const shareForm = document.getElementById('share-form');
        const urlForm = document.getElementById('url-form');
        const editShareForm = document.getElementById('edit-share-form');
        if (shareForm) shareForm.addEventListener('submit', this.createShare.bind(this));
        if (urlForm) urlForm.addEventListener('submit', this.createShortUrl.bind(this));
        if (editShareForm) editShareForm.addEventListener('submit', this.updateShare.bind(this));

        // Upload mode change
        document.querySelectorAll('input[name="upload-mode"]').forEach(radio => {
            radio.addEventListener('change', this.handleUploadModeChange.bind(this));
        });

        // Project form
        const projectForm = document.getElementById('project-form');
        if (projectForm) projectForm.addEventListener('submit', this.createProject.bind(this));

        // Forms tab initialization
        this.initializeFormsTab();

        // Database tab initialization
        this.initializeDatabaseTab();

        // Modal
        const modalClose = document.getElementById('modal-close');
        const pageModal = document.getElementById('page-modal');
        if (modalClose) modalClose.addEventListener('click', this.closeModal.bind(this));
        if (pageModal) {
            pageModal.addEventListener('click', (e) => {
                if (e.target.id === 'page-modal') this.closeModal();
            });
        }
    }

    switchTab(tabName) {
        if (!tabName) {
            console.error('No tab name provided to switchTab');
            return;
        }

        // Remove active class from all tabs and content
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // Add active class to selected tab and content
        document.querySelectorAll(`[data-tab="${tabName}"]`).forEach(btn => {
            btn.classList.add('active');
        });

        const tabContent = document.getElementById(`${tabName}-tab`);
        if (tabContent) {
            tabContent.classList.add('active');
        } else {
            console.error('Tab content not found:', `${tabName}-tab`);
        }

        // Load specific tab content
        if (tabName === 'manage') {
            this.loadPages();
        } else if (tabName === 'forms') {
            this.loadPagesForForms();
        } else if (tabName === 'database') {
            this.switchDatabaseSection('structure');
        } else if (tabName === 'sharing') {
            this.loadShares();
        } else if (tabName === 'users') {
            this.loadUsers();
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        document.getElementById('upload-area').classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        document.getElementById('upload-area').classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        document.getElementById('upload-area').classList.remove('dragover');
        const files = e.dataTransfer.files;
        this.uploadFiles(files);
    }

    handleFileSelect(e) {
        const files = e.target.files;
        this.uploadFiles(files);
    }

    async uploadFiles(files) {
        const formData = new FormData();
        const progressBar = document.getElementById('upload-progress');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        const resultsDiv = document.getElementById('upload-results');

        // Show progress bar
        progressBar.style.display = 'block';
        resultsDiv.innerHTML = '';

        // Add files to form data
        Array.from(files).forEach((file, index) => {
            formData.append(`files[${index}]`, file);
        });

        // Add upload configuration
        const uploadMode = document.querySelector('input[name="upload-mode"]:checked').value;
        const projectName = document.getElementById('project-input').value.trim();

        formData.append('upload_type', uploadMode);
        formData.append('project_name', projectName);

        try {
            console.log('Uploading files:', files);
            console.log('Upload mode:', uploadMode);
            console.log('Project name:', projectName);

            const response = await fetch('includes/enhanced_upload.php', {
                method: 'POST',
                body: formData
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const responseText = await response.text();
            console.log('Response text:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
                console.log('Parsed result:', result);
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                console.error('Raw response:', responseText);
                throw new Error('Invalid JSON response: ' + responseText.substring(0, 200));
            }

            // Hide progress bar
            progressBar.style.display = 'none';

            // Show enhanced results
            this.displayEnhancedUploadResults(result);

            // Refresh pages if successful
            if (result.success) {
                this.showSuccess(`Upload completed! ${result.successful_uploads}/${result.total_files} files uploaded successfully.`);
                this.loadPages();
            } else {
                this.showError(result.message || 'Upload failed');
            }

        } catch (error) {
            console.error('Upload error:', error);
            progressBar.style.display = 'none';
            this.showError('Upload failed: ' + error.message);
        }
    }

    displayUploadResults(result) {
        const resultsDiv = document.getElementById('upload-results');
        
        if (result.files) {
            result.files.forEach(file => {
                const resultItem = document.createElement('div');
                resultItem.className = `result-item ${file.success ? 'success' : 'error'}`;
                resultItem.innerHTML = `
                    <i class="fas ${file.success ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                    <span>${file.name}: ${file.message}</span>
                `;
                resultsDiv.appendChild(resultItem);
            });
        }

        if (result.message) {
            const messageItem = document.createElement('div');
            messageItem.className = `result-item ${result.success ? 'success' : 'error'}`;
            messageItem.innerHTML = `
                <i class="fas ${result.success ? 'fa-info-circle' : 'fa-exclamation-circle'}"></i>
                <span>${result.message}</span>
            `;
            resultsDiv.appendChild(messageItem);
        }
    }

    async loadPages() {
        try {
            // Try the new page manager first, fallback to old system
            const statusFilter = document.getElementById('status-filter');
            const projectFilter = document.getElementById('project-filter');

            let url = 'includes/page_manager.php?action=get_pages';

            if (statusFilter && statusFilter.value && statusFilter.value !== 'all') {
                url += `&status=${statusFilter.value}`;
            }
            if (projectFilter && projectFilter.value) {
                url += `&project_id=${projectFilter.value}`;
            }

            const response = await fetch(url);
            const result = await response.json();

            if (result.success) {
                this.displayPages(result.pages);
                if (this.updatePageStats) {
                    this.updatePageStats(result.pages);
                }
                this.loadProjects(); // Update project filter
            } else {
                // Fallback to old system
                console.log('Falling back to old page loading system');
                await this.loadPagesOld();
            }
        } catch (error) {
            console.error('Error loading pages with new system, trying fallback:', error);
            // Fallback to old system
            await this.loadPagesOld();
        }
    }

    async loadPagesOld() {
        try {
            const response = await fetch('includes/get_pages.php');
            const pages = await response.json();
            this.displayPages(pages);
        } catch (error) {
            console.error('Error loading pages:', error);
            this.showError('Failed to load pages');
        }
    }

    displayPages(pages) {
        const pagesGrid = document.getElementById('pages-grid');

        if (!pages || pages.length === 0) {
            pagesGrid.innerHTML = '<div class="no-pages">No pages found. Upload some HTML files to get started!</div>';
            return;
        }

        pagesGrid.innerHTML = pages.map(page => {
            // Normalize page data for compatibility
            const pageStatus = page.status || 'active';
            const pageTitle = page.title || page.original_filename || page.filename;
            const pageFilename = page.original_filename || page.filename;
            const formCount = page.form_count || page.forms_count || 0;
            const assetCount = page.asset_count || 0;
            const shareCount = page.share_count || 0;

            return `
                <div class="page-card" data-page-id="${page.id}">
                    <div class="page-header">
                        <div class="page-select">
                            <input type="checkbox" class="page-checkbox" data-page-id="${page.id}" onchange="app.updateSelection()">
                        </div>
                        <div class="page-title">
                            <h3>${pageTitle}</h3>
                            ${page.project_name ? `<span class="project-badge" style="background-color: ${page.project_color || '#667eea'}">${page.project_name}</span>` : ''}
                            <span class="status-badge status-${pageStatus}">${pageStatus}</span>
                        </div>
                        <div class="page-actions">
                            <button class="btn btn-sm" onclick="event.stopPropagation(); app.viewPage(${page.id})" title="View Page">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm" onclick="event.stopPropagation(); app.sharePage(${page.id})" title="Share Page">
                                <i class="fas fa-share-alt"></i>
                            </button>
                            ${pageStatus === 'archived' ?
                                `<button class="btn btn-sm btn-success" onclick="event.stopPropagation(); app.restorePage(${page.id})" title="Restore Page">
                                    <i class="fas fa-undo"></i>
                                </button>` :
                                `<button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); app.archivePage(${page.id})" title="Archive Page">
                                    <i class="fas fa-archive"></i>
                                </button>`
                            }
                            <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); app.deletePage(${page.id})" title="Delete Page">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="page-info">
                        <p><strong>File:</strong> ${pageFilename}</p>
                        <p><strong>Size:</strong> ${this.formatFileSize(page.file_size)}</p>
                        <p><strong>Uploaded:</strong> ${new Date(page.created_at).toLocaleDateString()}</p>
                        <div class="page-stats">
                            <span class="stat"><i class="fas fa-wpforms"></i> ${formCount} forms</span>
                            <span class="stat"><i class="fas fa-file"></i> ${assetCount} assets</span>
                            <span class="stat"><i class="fas fa-share"></i> ${shareCount} shares</span>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    async showPageDetails(pageId) {
        try {
            this.currentPageId = pageId; // Store current page ID for editing
            const response = await fetch(`includes/get_page_details.php?id=${pageId}`);
            const pageDetails = await response.json();

            document.getElementById('modal-title').textContent = pageDetails.title || pageDetails.filename;
            document.getElementById('modal-body').innerHTML = this.generatePageDetailsHTML(pageDetails);
            document.getElementById('page-modal').style.display = 'block';
        } catch (error) {
            console.error('Error loading page details:', error);
            this.showError('Failed to load page details');
        }
    }

    generatePageDetailsHTML(page) {
        let html = `
            <div class="page-details">
                <h4>Page Information</h4>
                <p><strong>Filename:</strong> ${page.filename}</p>
                <p><strong>Title:</strong> ${page.title || 'No title'}</p>
                <p><strong>File Size:</strong> ${this.formatFileSize(page.file_size)}</p>
                <p><strong>Imported:</strong> ${new Date(page.created_at).toLocaleString()}</p>
        `;

        if (page.forms && page.forms.length > 0) {
            html += `
                <div class="forms-header">
                    <h4>Forms Found (${page.forms.length})</h4>
                    <button class="btn btn-primary btn-sm" onclick="app.toggleEditMode()">
                        <i class="fas fa-edit"></i> <span id="edit-mode-text">Edit Forms</span>
                    </button>
                </div>
                <div class="forms-list" id="forms-list">
            `;

            page.forms.forEach((form, index) => {
                html += this.generateFormHTML(form, index, page.id);
            });

            html += '</div>';
        } else {
            html += '<p>No forms found in this page.</p>';
        }

        html += '</div>';
        return html;
    }

    generateFormHTML(form, index, pageId) {
        const isEditMode = this.editMode || false;

        let html = `
            <div class="form-item" data-form-id="${form.id}">
                <div class="form-header">
                    <h5>Form ${index + 1}</h5>
                    ${isEditMode ? `<button class="btn-icon" onclick="app.saveForm(${form.id})" title="Save Form"><i class="fas fa-save"></i></button>` : ''}
                </div>

                <div class="form-attributes">
                    <div class="form-attr">
                        <label>Action:</label>
                        ${isEditMode ?
                            `<input type="text" class="form-input" data-field="form_action" value="${form.form_action || ''}" placeholder="Form action URL">` :
                            `<span>${form.form_action || 'Not specified'}</span>`
                        }
                    </div>
                    <div class="form-attr">
                        <label>Method:</label>
                        ${isEditMode ?
                            `<select class="form-input" data-field="form_method">
                                <option value="GET" ${form.form_method === 'GET' ? 'selected' : ''}>GET</option>
                                <option value="POST" ${form.form_method === 'POST' ? 'selected' : ''}>POST</option>
                            </select>` :
                            `<span>${form.form_method || 'GET'}</span>`
                        }
                    </div>
                    <div class="form-attr">
                        <label>Encoding:</label>
                        ${isEditMode ?
                            `<select class="form-input" data-field="form_enctype">
                                <option value="">Default</option>
                                <option value="application/x-www-form-urlencoded" ${form.form_enctype === 'application/x-www-form-urlencoded' ? 'selected' : ''}>URL Encoded</option>
                                <option value="multipart/form-data" ${form.form_enctype === 'multipart/form-data' ? 'selected' : ''}>Multipart</option>
                                <option value="text/plain" ${form.form_enctype === 'text/plain' ? 'selected' : ''}>Plain Text</option>
                            </select>` :
                            `<span>${form.form_enctype || 'Default'}</span>`
                        }
                    </div>
                </div>
        `;

        if (form.fields && form.fields.length > 0) {
            html += `
                <div class="fields-section">
                    <div class="fields-header">
                        <h6>Fields (${form.fields.length})</h6>
                        ${isEditMode ? `<button class="btn-icon" onclick="app.addField(${form.id})" title="Add Field"><i class="fas fa-plus"></i></button>` : ''}
                    </div>
                    <div class="fields-list">
            `;

            form.fields.forEach(field => {
                html += this.generateFieldHTML(field, isEditMode);
            });

            html += `
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    generateFieldHTML(field, isEditMode) {
        let html = `
            <div class="field-item" data-field-id="${field.id}">
                <div class="field-header">
                    <div class="field-basic-info">
                        ${isEditMode ?
                            `<input type="text" class="field-input field-name-input" data-field="field_name" value="${field.field_name || ''}" placeholder="Field name">` :
                            `<span class="field-name">${field.field_name || 'Unnamed'}</span>`
                        }
                        ${isEditMode ?
                            `<select class="field-input field-type-select" data-field="field_type">
                                <option value="text" ${field.field_type === 'text' ? 'selected' : ''}>Text</option>
                                <option value="email" ${field.field_type === 'email' ? 'selected' : ''}>Email</option>
                                <option value="password" ${field.field_type === 'password' ? 'selected' : ''}>Password</option>
                                <option value="number" ${field.field_type === 'number' ? 'selected' : ''}>Number</option>
                                <option value="tel" ${field.field_type === 'tel' ? 'selected' : ''}>Phone</option>
                                <option value="url" ${field.field_type === 'url' ? 'selected' : ''}>URL</option>
                                <option value="date" ${field.field_type === 'date' ? 'selected' : ''}>Date</option>
                                <option value="datetime-local" ${field.field_type === 'datetime-local' ? 'selected' : ''}>DateTime</option>
                                <option value="time" ${field.field_type === 'time' ? 'selected' : ''}>Time</option>
                                <option value="checkbox" ${field.field_type === 'checkbox' ? 'selected' : ''}>Checkbox</option>
                                <option value="radio" ${field.field_type === 'radio' ? 'selected' : ''}>Radio</option>
                                <option value="select" ${field.field_type === 'select' ? 'selected' : ''}>Select</option>
                                <option value="textarea" ${field.field_type === 'textarea' ? 'selected' : ''}>Textarea</option>
                                <option value="file" ${field.field_type === 'file' ? 'selected' : ''}>File</option>
                                <option value="hidden" ${field.field_type === 'hidden' ? 'selected' : ''}>Hidden</option>
                            </select>` :
                            `<span class="field-type">${field.field_type}</span>`
                        }
                    </div>
                    ${isEditMode ?
                        `<div class="field-actions">
                            <button class="btn-icon" onclick="app.saveField(${field.id})" title="Save Field"><i class="fas fa-save"></i></button>
                            <button class="btn-icon btn-danger" onclick="app.deleteField(${field.id})" title="Delete Field"><i class="fas fa-trash"></i></button>
                        </div>` :
                        `${field.field_required ? '<span class="field-required">Required</span>' : ''}`
                    }
                </div>
        `;

        if (isEditMode) {
            html += `
                <div class="field-attributes">
                    <div class="field-attr-row">
                        <div class="field-attr">
                            <label>Placeholder:</label>
                            <input type="text" class="field-input" data-field="field_placeholder" value="${field.field_placeholder || ''}" placeholder="Placeholder text">
                        </div>
                        <div class="field-attr">
                            <label>Default Value:</label>
                            <input type="text" class="field-input" data-field="field_value" value="${field.field_value || ''}" placeholder="Default value">
                        </div>
                    </div>
                    <div class="field-attr-row">
                        <div class="field-attr checkbox-attr">
                            <label><input type="checkbox" data-field="field_required" ${field.field_required ? 'checked' : ''}> Required</label>
                        </div>
                        <div class="field-attr checkbox-attr">
                            <label><input type="checkbox" data-field="field_readonly" ${field.field_readonly ? 'checked' : ''}> Read Only</label>
                        </div>
                        <div class="field-attr checkbox-attr">
                            <label><input type="checkbox" data-field="field_disabled" ${field.field_disabled ? 'checked' : ''}> Disabled</label>
                        </div>
                    </div>
                    <div class="field-attr-row">
                        <div class="field-attr">
                            <label>Min Length:</label>
                            <input type="number" class="field-input" data-field="field_minlength" value="${field.field_minlength || ''}" placeholder="Min length">
                        </div>
                        <div class="field-attr">
                            <label>Max Length:</label>
                            <input type="number" class="field-input" data-field="field_maxlength" value="${field.field_maxlength || ''}" placeholder="Max length">
                        </div>
                    </div>
                    <div class="field-attr-row">
                        <div class="field-attr">
                            <label>Pattern:</label>
                            <input type="text" class="field-input" data-field="field_pattern" value="${field.field_pattern || ''}" placeholder="Regex pattern">
                        </div>
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    closeModal() {
        document.getElementById('page-modal').style.display = 'none';
        this.editMode = false;
    }

    searchPages(e) {
        const searchTerm = e.target.value.toLowerCase();
        const pageCards = document.querySelectorAll('.page-card');
        
        pageCards.forEach(card => {
            const text = card.textContent.toLowerCase();
            card.style.display = text.includes(searchTerm) ? 'block' : 'none';
        });
    }

    async loadDatabaseStructure() {
        try {
            const response = await fetch('includes/get_database_structure.php');
            const structure = await response.json();
            this.displayDatabaseStructure(structure);
        } catch (error) {
            console.error('Error loading database structure:', error);
        }
    }

    displayDatabaseStructure(structure) {
        const dbTables = document.getElementById('db-tables');
        
        if (!structure || structure.length === 0) {
            dbTables.innerHTML = '<p>No database structure generated yet. Import some pages with forms first.</p>';
            return;
        }

        // Display database tables and structure
        dbTables.innerHTML = structure.map(table => `
            <div class="table-structure">
                <h4>${table.name}</h4>
                <div class="table-fields">
                    ${table.fields.map(field => `
                        <div class="field-row">
                            <span class="field-name">${field.name}</span>
                            <span class="field-type">${field.type}</span>
                            ${field.constraints ? `<span class="field-constraints">${field.constraints}</span>` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }

    async generateDatabase() {
        try {
            const response = await fetch('includes/generate_database.php', {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('Database structure generated successfully!');
                this.loadDatabaseStructure();
            } else {
                this.showError(result.message || 'Failed to generate database structure');
            }
        } catch (error) {
            console.error('Error generating database:', error);
            this.showError('Failed to generate database structure');
        }
    }

    async exportSQL() {
        try {
            const response = await fetch('includes/export_sql.php');
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'database_structure.sql';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Error exporting SQL:', error);
            this.showError('Failed to export SQL');
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // Add to page
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // Form editing methods
    toggleEditMode() {
        this.editMode = !this.editMode;
        const editButton = document.getElementById('edit-mode-text');
        if (editButton) {
            editButton.textContent = this.editMode ? 'Exit Edit Mode' : 'Edit Forms';
        }

        // Refresh the current page details
        const modal = document.getElementById('page-modal');
        if (modal.style.display === 'block') {
            const pageId = this.currentPageId;
            if (pageId) {
                this.showPageDetails(pageId);
            }
        }
    }

    async saveForm(formId) {
        const formElement = document.querySelector(`[data-form-id="${formId}"]`);
        if (!formElement) return;

        const updates = {};
        const inputs = formElement.querySelectorAll('.form-input[data-field]');

        inputs.forEach(input => {
            const field = input.dataset.field;
            updates[field] = input.value;
        });

        try {
            const formData = new FormData();
            formData.append('action', 'update_form');
            formData.append('form_id', formId);
            formData.append('updates', JSON.stringify(updates));

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Form updated successfully!');
            } else {
                this.showError(result.message || 'Failed to update form');
            }
        } catch (error) {
            console.error('Error saving form:', error);
            this.showError('Failed to save form changes');
        }
    }

    async saveField(fieldId) {
        const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
        if (!fieldElement) return;

        const updates = {};
        const inputs = fieldElement.querySelectorAll('.field-input[data-field]');

        inputs.forEach(input => {
            const field = input.dataset.field;
            if (input.type === 'checkbox') {
                updates[field] = input.checked ? 1 : 0;
            } else {
                updates[field] = input.value;
            }
        });

        try {
            const formData = new FormData();
            formData.append('action', 'update_field');
            formData.append('field_id', fieldId);
            formData.append('updates', JSON.stringify(updates));

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Field updated successfully!');
            } else {
                this.showError(result.message || 'Failed to update field');
            }
        } catch (error) {
            console.error('Error saving field:', error);
            this.showError('Failed to save field changes');
        }
    }

    async deleteField(fieldId) {
        if (!confirm('Are you sure you want to delete this field?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'delete_field');
            formData.append('field_id', fieldId);

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Field deleted successfully!');
                // Remove the field element from DOM
                const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
                if (fieldElement) {
                    fieldElement.remove();
                }
            } else {
                this.showError(result.message || 'Failed to delete field');
            }
        } catch (error) {
            console.error('Error deleting field:', error);
            this.showError('Failed to delete field');
        }
    }

    async addField(formId) {
        const fieldName = prompt('Enter field name:');
        if (!fieldName) return;

        const fieldType = prompt('Enter field type (text, email, password, etc.):', 'text');
        if (!fieldType) return;

        const fieldData = {
            field_name: fieldName,
            field_type: fieldType,
            field_required: 0
        };

        try {
            const formData = new FormData();
            formData.append('action', 'add_field');
            formData.append('form_id', formId);
            formData.append('field_data', JSON.stringify(fieldData));

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Field added successfully!');
                // Refresh the page details to show the new field
                if (this.currentPageId) {
                    this.showPageDetails(this.currentPageId);
                }
            } else {
                this.showError(result.message || 'Failed to add field');
            }
        } catch (error) {
            console.error('Error adding field:', error);
            this.showError('Failed to add field');
        }
    }

    // Page viewing and sharing methods
    viewPage(pageId) {
        const url = `view.php?id=${pageId}`;
        window.open(url, '_blank');
    }

    sharePage(pageId) {
        this.currentSharePageId = pageId;
        this.showShareModal();

        // Pre-select the page in the dropdown
        const pageSelect = document.getElementById('share-page-select');
        pageSelect.value = pageId;
    }

    async showShareModal() {
        // Load pages for the dropdown
        await this.loadPagesForSharing();
        document.getElementById('share-modal').style.display = 'block';
    }

    closeShareModal() {
        document.getElementById('share-modal').style.display = 'none';
        document.getElementById('share-form').reset();
    }

    showUrlModal() {
        document.getElementById('url-modal').style.display = 'block';
    }

    closeUrlModal() {
        document.getElementById('url-modal').style.display = 'none';
        document.getElementById('url-form').reset();
    }

    showEditShareModal(share) {
        // Populate form with share data
        document.getElementById('edit-share-id').value = share.id;
        document.getElementById('edit-share-title').value = share.title || '';
        document.getElementById('edit-share-description').value = share.description || '';
        document.getElementById('edit-share-password').value = ''; // Don't show existing password
        document.getElementById('edit-share-redirect-url').value = share.redirect_url || '';

        // Handle expires_at
        if (share.expires_at) {
            const date = new Date(share.expires_at);
            const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
            document.getElementById('edit-share-expires').value = localDateTime.toISOString().slice(0, 16);
        } else {
            document.getElementById('edit-share-expires').value = '';
        }

        // Handle max_views
        document.getElementById('edit-share-max-views').value = share.max_views || '';

        // Handle checkboxes
        document.getElementById('edit-share-show-forms').checked = share.show_forms == 1;
        document.getElementById('edit-share-allow-download').checked = share.allow_download == 1;
        document.getElementById('edit-share-show-metadata').checked = share.show_metadata == 1;

        // Show modal
        document.getElementById('edit-share-modal').style.display = 'block';
    }

    closeEditShareModal() {
        document.getElementById('edit-share-modal').style.display = 'none';
        document.getElementById('edit-share-form').reset();
    }

    async loadPagesForSharing() {
        try {
            const response = await fetch('includes/get_pages.php');
            const pages = await response.json();

            const pageSelect = document.getElementById('share-page-select');
            pageSelect.innerHTML = '<option value="">Choose a page to share...</option>';

            pages.forEach(page => {
                const option = document.createElement('option');
                option.value = page.id;
                option.textContent = `${page.title || page.filename} (${page.forms_count || 0} forms)`;
                pageSelect.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading pages for sharing:', error);
        }
    }

    async createShare(e) {
        e.preventDefault();

        const formData = new FormData();
        formData.append('action', 'create_share');
        formData.append('page_id', document.getElementById('share-page-select').value);

        // Validate redirect URL if provided
        const redirectUrl = document.getElementById('share-redirect-url').value.trim();
        let validatedRedirectUrl = null;

        if (redirectUrl) {
            // Simple URL validation
            try {
                new URL(redirectUrl);
                validatedRedirectUrl = redirectUrl;
            } catch (e) {
                // If URL is invalid, show warning but don't prevent submission
                console.warn('Invalid redirect URL provided:', redirectUrl);
                this.showNotification('Warning: Invalid redirect URL format. Share will be created without redirect.', 'warning');
            }
        }

        const options = {
            title: document.getElementById('share-title').value,
            description: document.getElementById('share-description').value,
            password: document.getElementById('share-password').value,
            expires_at: document.getElementById('share-expires').value || null,
            max_views: document.getElementById('share-max-views').value || null,
            show_forms: document.getElementById('share-show-forms').checked,
            allow_download: document.getElementById('share-allow-download').checked,
            show_metadata: document.getElementById('share-show-metadata').checked,
            redirect_url: validatedRedirectUrl
        };

        formData.append('options', JSON.stringify(options));

        try {
            const response = await fetch('includes/sharing_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Share created successfully!');
                this.closeShareModal();
                this.loadShares();
                this.showShareResult(result.share);
            } else {
                this.showError(result.message || 'Failed to create share');
            }
        } catch (error) {
            console.error('Error creating share:', error);
            this.showError('Failed to create share');
        }
    }

    async createShortUrl(e) {
        e.preventDefault();

        const formData = new FormData();
        formData.append('action', 'create_short_url');
        formData.append('original_url', document.getElementById('original-url').value);

        const options = {
            title: document.getElementById('url-title').value,
            description: document.getElementById('url-description').value,
            expires_at: document.getElementById('url-expires').value || null
        };

        formData.append('options', JSON.stringify(options));

        try {
            const response = await fetch('includes/sharing_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Short URL created successfully!');
                this.closeUrlModal();
                this.showUrlResult(result.short_url);
            } else {
                this.showError(result.message || 'Failed to create short URL');
            }
        } catch (error) {
            console.error('Error creating short URL:', error);
            this.showError('Failed to create short URL');
        }
    }

    async loadShares() {
        try {
            const response = await fetch('includes/sharing_manager.php?action=get_shares');
            const result = await response.json();

            if (result.success) {
                this.displayShares(result.shares);
            } else {
                this.showError(result.message || 'Failed to load shares');
            }
        } catch (error) {
            console.error('Error loading shares:', error);
            this.showError('Failed to load shares');
        }
    }

    displayShares(shares) {
        const sharesGrid = document.getElementById('shares-grid');

        if (!shares || shares.length === 0) {
            sharesGrid.innerHTML = '<p>No shares created yet. Create your first share using the button above.</p>';
            return;
        }

        sharesGrid.innerHTML = shares.map(share => `
            <div class="share-card ${share.is_expired ? 'expired' : ''} ${share.is_view_limit_reached ? 'limit-reached' : ''}">
                <div class="share-header">
                    <h4>${share.title || share.page_title}</h4>
                    <div class="share-status">
                        ${share.is_expired ? '<span class="status-badge expired">Expired</span>' : ''}
                        ${share.is_view_limit_reached ? '<span class="status-badge limit">Limit Reached</span>' : ''}
                        ${share.has_password ? '<span class="status-badge protected">Protected</span>' : ''}
                    </div>
                </div>

                <div class="share-info">
                    <div class="share-stats">
                        <span><i class="fas fa-eye"></i> ${share.view_count} views</span>
                        ${share.max_views ? `<span><i class="fas fa-limit"></i> Max: ${share.max_views}</span>` : ''}
                        ${share.expires_at ? `<span><i class="fas fa-clock"></i> Expires: ${new Date(share.expires_at).toLocaleDateString()}</span>` : ''}
                    </div>
                </div>

                <div class="share-urls">
                    <div class="url-group">
                        <label>Share URL:</label>
                        <div class="url-input-group">
                            <input type="text" value="${share.share_url}" readonly onclick="this.select()">
                            <button class="btn-icon" onclick="app.copyToClipboard('${share.share_url}')" title="Copy URL">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="url-group">
                        <label>Short URL:</label>
                        <div class="url-input-group">
                            <input type="text" value="${share.short_url}" readonly onclick="this.select()">
                            <button class="btn-icon" onclick="app.copyToClipboard('${share.short_url}')" title="Copy Short URL">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="share-actions">
                    <button class="btn-icon" onclick="window.open('${share.share_url}', '_blank')" title="View Shared Page">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                    <button class="btn-icon" onclick="app.editShare(${share.id})" title="Edit Share">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="app.viewShareAnalytics(${share.id})" title="View Analytics">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="app.deleteShare(${share.id})" title="Delete Share">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    showShareResult(share) {
        const notification = document.createElement('div');
        notification.className = 'notification success share-result';
        notification.innerHTML = `
            <h4>Share Created Successfully!</h4>
            <div class="result-urls">
                <div class="url-result">
                    <label>Share URL:</label>
                    <input type="text" value="${share.share_url}" readonly onclick="this.select()">
                    <button onclick="app.copyToClipboard('${share.share_url}')">Copy</button>
                </div>
                <div class="url-result">
                    <label>Short URL:</label>
                    <input type="text" value="${share.short_url}" readonly onclick="this.select()">
                    <button onclick="app.copyToClipboard('${share.short_url}')">Copy</button>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 10000); // Show for 10 seconds
    }

    showUrlResult(shortUrl) {
        const notification = document.createElement('div');
        notification.className = 'notification success url-result';
        notification.innerHTML = `
            <h4>Short URL Created!</h4>
            <div class="url-result">
                <input type="text" value="${shortUrl.short_url}" readonly onclick="this.select()">
                <button onclick="app.copyToClipboard('${shortUrl.short_url}')">Copy</button>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showSuccess('Copied to clipboard!');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showSuccess('Copied to clipboard!');
        });
    }

    async deleteShare(shareId) {
        if (!confirm('Are you sure you want to delete this share?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'delete_share');
            formData.append('share_id', shareId);

            const response = await fetch('includes/sharing_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Share deleted successfully!');
                this.loadShares();
            } else {
                this.showError(result.message || 'Failed to delete share');
            }
        } catch (error) {
            console.error('Error deleting share:', error);
            this.showError('Failed to delete share');
        }
    }

    async editShare(shareId) {
        try {
            // Get share details
            const response = await fetch(`includes/sharing_manager.php?action=get_share&share_id=${shareId}`);
            const result = await response.json();

            if (result.success) {
                this.showEditShareModal(result.share);
            } else {
                this.showError(result.message || 'Failed to load share details');
            }
        } catch (error) {
            console.error('Error loading share details:', error);
            this.showError('Failed to load share details');
        }
    }

    async updateShare(e) {
        e.preventDefault();

        const shareId = document.getElementById('edit-share-id').value;
        const formData = new FormData();
        formData.append('action', 'update_share');
        formData.append('share_id', shareId);

        // Validate redirect URL if provided
        const redirectUrl = document.getElementById('edit-share-redirect-url').value.trim();
        let validatedRedirectUrl = null;

        if (redirectUrl) {
            // Simple URL validation
            try {
                new URL(redirectUrl);
                validatedRedirectUrl = redirectUrl;
            } catch (e) {
                // If URL is invalid, show warning but don't prevent submission
                console.warn('Invalid redirect URL provided:', redirectUrl);
                this.showNotification('Warning: Invalid redirect URL format. Share will be updated without redirect.', 'warning');
            }
        }

        const updates = {
            title: document.getElementById('edit-share-title').value,
            description: document.getElementById('edit-share-description').value,
            password: document.getElementById('edit-share-password').value,
            expires_at: document.getElementById('edit-share-expires').value || null,
            max_views: document.getElementById('edit-share-max-views').value || null,
            show_forms: document.getElementById('edit-share-show-forms').checked,
            allow_download: document.getElementById('edit-share-allow-download').checked,
            show_metadata: document.getElementById('edit-share-show-metadata').checked,
            redirect_url: validatedRedirectUrl
        };

        formData.append('updates', JSON.stringify(updates));

        try {
            const response = await fetch('includes/sharing_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Share updated successfully!');
                this.closeEditShareModal();
                this.loadShares();
            } else {
                this.showError(result.message || 'Failed to update share');
            }
        } catch (error) {
            console.error('Error updating share:', error);
            this.showError('Failed to update share');
        }
    }

    viewShareAnalytics(shareId) {
        // TODO: Implement analytics modal
        this.showInfo('Analytics feature coming soon!');
    }

    showInfo(message) {
        this.showNotification(message, 'info');
    }

    showWarning(message) {
        this.showNotification(message, 'warning');
    }

    // Enhanced upload methods
    handleUploadModeChange(e) {
        const mode = e.target.value;
        const uploadTitle = document.getElementById('upload-title');
        const uploadSubtitle = document.getElementById('upload-subtitle');
        const fileTypes = document.getElementById('file-types');
        const fileInput = document.getElementById('file-input');

        if (mode === 'zip') {
            uploadTitle.textContent = 'Drag & Drop ZIP Files Here';
            uploadSubtitle.textContent = 'Upload complete websites as ZIP archives';
            fileTypes.textContent = 'Supported: ZIP files containing HTML, CSS, JS, Images';
            fileInput.accept = '.zip';
            fileInput.multiple = true;
        } else {
            uploadTitle.textContent = 'Drag & Drop Files Here';
            uploadSubtitle.textContent = 'Upload HTML pages with their assets';
            fileTypes.textContent = 'Supported: HTML, CSS, JS, Images, Fonts, ZIP';
            fileInput.accept = '.html,.htm,.css,.js,.png,.jpg,.jpeg,.gif,.svg,.woff,.woff2,.ttf,.otf,.zip';
            fileInput.multiple = true;
        }
    }

    displayEnhancedUploadResults(result) {
        const resultsDiv = document.getElementById('upload-results');

        if (!result.results || result.results.length === 0) {
            resultsDiv.innerHTML = '<p>No files processed.</p>';
            return;
        }

        let html = '<div class="upload-summary">';
        html += `<h3>Upload Results (${result.successful_uploads}/${result.total_files} successful)</h3>`;
        html += '</div>';

        html += '<div class="upload-details">';

        result.results.forEach(fileResult => {
            html += `<div class="result-item ${fileResult.success ? 'success' : 'error'}">`;
            html += `<div class="result-header">`;
            html += `<i class="fas ${fileResult.success ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>`;
            html += `<span class="file-name">${fileResult.name}</span>`;
            html += `<span class="file-type">${fileResult.type || fileResult.file_type || 'file'}</span>`;
            html += `</div>`;

            html += `<div class="result-message">${fileResult.message}</div>`;

            if (fileResult.success) {
                if (fileResult.type === 'zip' && fileResult.pages) {
                    html += `<div class="zip-details">`;
                    html += `<strong>Pages created:</strong> ${fileResult.pages_created}`;
                    html += `<ul class="pages-list">`;
                    fileResult.pages.forEach(page => {
                        html += `<li>${page.name} (${page.associated_files || 0} assets)</li>`;
                    });
                    html += `</ul>`;
                    html += `</div>`;
                } else if (fileResult.page_id) {
                    html += `<div class="file-details">`;
                    html += `<button class="btn btn-sm" onclick="app.viewPage(${fileResult.page_id})">View Page</button>`;
                    html += `<button class="btn btn-sm" onclick="app.sharePage(${fileResult.page_id})">Share Page</button>`;
                    html += `</div>`;
                }
            }

            html += `</div>`;
        });

        html += '</div>';
        resultsDiv.innerHTML = html;
    }

    // Project management
    showCreateProjectModal() {
        document.getElementById('project-modal').style.display = 'block';
    }

    closeProjectModal() {
        document.getElementById('project-modal').style.display = 'none';
        document.getElementById('project-form').reset();
    }

    async createProject(e) {
        e.preventDefault();

        const formData = new FormData();
        formData.append('action', 'create_project');
        formData.append('name', document.getElementById('project-name').value);
        formData.append('description', document.getElementById('project-description').value);
        formData.append('color', document.getElementById('project-color').value);

        try {
            const response = await fetch('includes/project_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Project created successfully!');
                this.closeProjectModal();
                this.loadProjects();
            } else {
                this.showError(result.message || 'Failed to create project');
            }
        } catch (error) {
            console.error('Error creating project:', error);
            this.showError('Failed to create project');
        }
    }

    async loadProjects() {
        try {
            const response = await fetch('includes/project_manager.php?action=get_projects');
            const result = await response.json();

            if (result.success) {
                const projectFilter = document.getElementById('project-filter');

                // Note: Project input is now a text field, no need to populate dropdown
                // Projects are created automatically when a new name is entered

                // Update project filter dropdown (if it exists)
                if (projectFilter) {
                    projectFilter.innerHTML = '<option value="">All Projects</option>';
                    result.projects.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.id;
                        option.textContent = project.name;
                        projectFilter.appendChild(option);
                    });
                }
            }
        } catch (error) {
            console.error('Error loading projects:', error);
        }
    }

    // Page Management Methods
    deletePage(pageId) {
        // Check if delete modal exists, otherwise use simple confirmation
        const deleteModal = document.getElementById('delete-modal');
        if (deleteModal) {
            this.showDeleteModal([pageId], 'single');
        } else {
            // Fallback to simple confirmation
            this.simpleDeletePage(pageId);
        }
    }

    async simpleDeletePage(pageId) {
        console.log('Delete page called for ID:', pageId);

        const confirmDelete = confirm('Are you sure you want to delete this page?\n\nThis will remove the page from the database and optionally delete associated files.');
        if (!confirmDelete) return;

        const deleteFiles = confirm('Do you also want to delete the associated files (HTML, CSS, JS, images)?\n\nClick OK to delete files, Cancel to keep files on disk.');

        try {
            const formData = new FormData();
            formData.append('page_id', pageId);
            formData.append('delete_files', deleteFiles);

            console.log('Sending delete request for page:', pageId, 'deleteFiles:', deleteFiles);

            const response = await fetch('includes/delete_page.php', {
                method: 'POST',
                body: formData
            });

            console.log('Delete response status:', response.status);

            const responseText = await response.text();
            console.log('Delete response text:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
                console.log('Delete result:', result);
            } catch (parseError) {
                console.error('JSON parse error in delete:', parseError);
                console.error('Raw delete response:', responseText);
                throw new Error('Invalid JSON response from delete: ' + responseText.substring(0, 200));
            }

            if (result.success) {
                this.showSuccess(result.message || 'Page deleted successfully');
                this.loadPages();
            } else {
                this.showError(result.message || 'Failed to delete page');
            }
        } catch (error) {
            console.error('Error deleting page:', error);
            this.showError('Failed to delete page: ' + error.message);
        }
    }

    async archivePage(pageId) {
        console.log('Archive page called for ID:', pageId);

        const confirmArchive = confirm('Are you sure you want to archive this page?\n\nArchived pages can be restored later.');
        if (!confirmArchive) return;

        try {
            const formData = new FormData();
            formData.append('page_id', pageId);
            formData.append('status', 'archived');

            const response = await fetch('includes/update_page_status.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Page archived successfully');
                this.loadPages();
            } else {
                this.showError(result.message || 'Failed to archive page');
            }
        } catch (error) {
            console.error('Error archiving page:', error);
            this.showError('Failed to archive page');
        }
    }

    async restorePage(pageId) {
        console.log('Restore page called for ID:', pageId);

        try {
            const formData = new FormData();
            formData.append('page_id', pageId);
            formData.append('status', 'active');

            const response = await fetch('includes/update_page_status.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Page restored successfully');
                this.loadPages();
            } else {
                this.showError(result.message || 'Failed to restore page');
            }
        } catch (error) {
            console.error('Error restoring page:', error);
            this.showError('Failed to restore page');
        }
    }

    async performPageAction(action, data, successMessage) {
        try {
            const formData = new FormData();
            formData.append('action', action);
            Object.keys(data).forEach(key => {
                formData.append(key, data[key]);
            });

            const response = await fetch('includes/page_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(successMessage);
                this.loadPages();
            } else {
                this.showError(result.message || 'Action failed');
            }
        } catch (error) {
            console.error('Error performing page action:', error);
            this.showError('Action failed');
        }
    }

    // Selection Management
    updateSelection() {
        const checkboxes = document.querySelectorAll('.page-checkbox:checked');
        const bulkActionsBtn = document.getElementById('bulk-actions-btn');
        const bulkActionsPanel = document.getElementById('bulk-actions-panel');
        const selectedCount = document.querySelector('.selected-count');

        if (checkboxes.length > 0) {
            if (bulkActionsBtn) bulkActionsBtn.style.display = 'block';
            if (selectedCount) selectedCount.textContent = `${checkboxes.length} page${checkboxes.length > 1 ? 's' : ''} selected`;
        } else {
            if (bulkActionsBtn) bulkActionsBtn.style.display = 'none';
            if (bulkActionsPanel) bulkActionsPanel.style.display = 'none';
        }
    }

    // Statistics Update
    updatePageStats(pages) {
        const totalPagesEl = document.getElementById('total-pages');
        const activePagesEl = document.getElementById('active-pages');
        const archivedPagesEl = document.getElementById('archived-pages');
        const totalSizeEl = document.getElementById('total-size');

        if (totalPagesEl) {
            const totalPages = pages.length;
            const activePages = pages.filter(p => (p.status || 'active') === 'active').length;
            const archivedPages = pages.filter(p => p.status === 'archived').length;
            const totalSize = pages.reduce((sum, p) => sum + (p.file_size || 0), 0);

            totalPagesEl.textContent = totalPages;
            activePagesEl.textContent = activePages;
            archivedPagesEl.textContent = archivedPages;
            totalSizeEl.textContent = this.formatFileSize(totalSize);
        }
    }

    toggleSelectAll() {
        const selectAllBtn = document.getElementById('select-all-pages');
        const checkboxes = document.querySelectorAll('.page-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !allChecked;
        });

        selectAllBtn.innerHTML = allChecked ?
            '<i class="fas fa-check-square"></i> Select All' :
            '<i class="fas fa-square"></i> Deselect All';

        this.updateSelection();
    }

    toggleBulkActions() {
        const panel = document.getElementById('bulk-actions-panel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    clearSelection() {
        document.querySelectorAll('.page-checkbox').forEach(cb => cb.checked = false);
        this.updateSelection();
    }

    // Bulk Actions
    bulkArchive() {
        const selectedIds = this.getSelectedPageIds();
        if (selectedIds.length === 0) return;

        this.performBulkAction('archive', selectedIds, 'Pages archived successfully');
    }

    bulkRestore() {
        const selectedIds = this.getSelectedPageIds();
        if (selectedIds.length === 0) return;

        this.performBulkAction('restore', selectedIds, 'Pages restored successfully');
    }

    bulkDelete(deleteFiles) {
        const selectedIds = this.getSelectedPageIds();
        if (selectedIds.length === 0) return;

        this.showDeleteModal(selectedIds, 'bulk', deleteFiles);
    }

    getSelectedPageIds() {
        return Array.from(document.querySelectorAll('.page-checkbox:checked'))
            .map(cb => parseInt(cb.dataset.pageId));
    }

    async performBulkAction(action, pageIds, successMessage) {
        try {
            const formData = new FormData();
            formData.append('action', `${action}_multiple`);
            formData.append('page_ids', JSON.stringify(pageIds));

            const response = await fetch('includes/page_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(successMessage);
                this.loadPages();
                this.clearSelection();
            } else {
                this.showError(result.message || 'Bulk action failed');
            }
        } catch (error) {
            console.error('Error performing bulk action:', error);
            this.showError('Bulk action failed');
        }
    }

    // Delete Modal Management
    showDeleteModal(pageIds, type, deleteFiles = true) {
        const modal = document.getElementById('delete-modal');
        const title = document.getElementById('delete-modal-title');
        const message = document.getElementById('delete-modal-message');
        const details = document.getElementById('delete-details');
        const checkbox = document.getElementById('delete-files-checkbox');
        const confirmBtn = document.getElementById('confirm-delete-btn');

        // Store data for confirmation
        this.deleteData = { pageIds, type, deleteFiles };

        // Update modal content
        if (type === 'single') {
            title.textContent = 'Delete Page';
            message.textContent = 'Are you sure you want to delete this page?';
        } else {
            title.textContent = 'Delete Multiple Pages';
            message.textContent = `Are you sure you want to delete ${pageIds.length} pages?`;
        }

        checkbox.checked = deleteFiles;
        details.innerHTML = `
            <p><strong>This action will:</strong></p>
            <ul>
                <li>Remove page(s) from the database</li>
                <li>Delete all associated forms and fields</li>
                <li>Remove all sharing links</li>
                <li>${deleteFiles ? 'Delete all associated files (HTML, CSS, JS, images)' : 'Keep files on disk (database records only)'}</li>
            </ul>
            <p class="warning-text">This action cannot be undone!</p>
        `;

        modal.style.display = 'block';
    }

    closeDeleteModal() {
        document.getElementById('delete-modal').style.display = 'none';
        this.deleteData = null;
    }

    async confirmDelete() {
        if (!this.deleteData) return;

        const { pageIds, type } = this.deleteData;
        const deleteFiles = document.getElementById('delete-files-checkbox').checked;

        try {
            const formData = new FormData();

            if (type === 'single') {
                formData.append('action', 'delete_page');
                formData.append('page_id', pageIds[0]);
            } else {
                formData.append('action', 'delete_multiple');
                formData.append('page_ids', JSON.stringify(pageIds));
            }

            formData.append('delete_files', deleteFiles);

            const response = await fetch('includes/page_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(result.message);
                this.loadPages();
                this.clearSelection();
                this.closeDeleteModal();
            } else {
                this.showError(result.message || 'Deletion failed');
            }
        } catch (error) {
            console.error('Error deleting pages:', error);
            this.showError('Deletion failed');
        }
    }

    // Statistics Update
    updatePageStats(pages) {
        const totalPages = pages.length;
        const activePages = pages.filter(p => p.status === 'active').length;
        const archivedPages = pages.filter(p => p.status === 'archived').length;
        const totalSize = pages.reduce((sum, p) => sum + (p.file_size || 0), 0);

        document.getElementById('total-pages').textContent = totalPages;
        document.getElementById('active-pages').textContent = activePages;
        document.getElementById('archived-pages').textContent = archivedPages;
        document.getElementById('total-size').textContent = this.formatFileSize(totalSize);
    }

    // Form Management Methods
    initializeFormsTab() {
        // Load pages for form selection when forms tab is activated
        document.querySelector('[data-tab="forms"]').addEventListener('click', () => {
            this.loadPagesForForms();
        });
    }

    async loadPagesForForms() {
        try {
            const response = await fetch('includes/get_pages.php');
            const pages = await response.json();

            const pageSelect = document.getElementById('page-select-forms');
            if (pageSelect) {
                pageSelect.innerHTML = '<option value="">Choose a page...</option>';

                pages.forEach(page => {
                    const option = document.createElement('option');
                    option.value = page.id;
                    option.textContent = page.title || page.original_filename || page.filename;
                    pageSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading pages for forms:', error);
            this.showError('Failed to load pages');
        }
    }

    async loadPageForms() {
        const pageSelect = document.getElementById('page-select-forms');
        const formsContainer = document.getElementById('forms-container');

        if (!pageSelect || !formsContainer) return;

        const pageId = pageSelect.value;

        if (!pageId) {
            formsContainer.innerHTML = `
                <div class="no-forms-message">
                    <i class="fas fa-wpforms"></i>
                    <h3>No Page Selected</h3>
                    <p>Select a page to view and edit its forms.</p>
                </div>
            `;
            return;
        }

        try {
            const response = await fetch(`includes/get_forms.php?page_id=${pageId}`);
            const result = await response.json();

            if (result.success && result.forms && result.forms.length > 0) {
                this.displayFormsEditor(result.forms);
            } else {
                formsContainer.innerHTML = `
                    <div class="no-forms-message">
                        <i class="fas fa-wpforms"></i>
                        <h3>No Forms Found</h3>
                        <p>This page doesn't contain any forms or form elements.</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading forms:', error);
            this.showError('Failed to load forms');
        }
    }

    displayFormsEditor(forms) {
        const formsContainer = document.getElementById('forms-container');

        formsContainer.innerHTML = forms.map((form, formIndex) => `
            <div class="form-editor" data-form-id="${form.id}">
                <div class="form-header">
                    <h3>
                        <i class="fas fa-wpforms"></i>
                        ${form.form_name || form.name || `Form ${formIndex + 1}`}
                        <span class="form-type">${form.type || 'form'}</span>
                    </h3>
                    <div class="form-info">
                        <span class="field-count">${form.fields ? form.fields.length : 0} fields</span>
                        <span class="form-method">${form.form_method || 'GET'}</span>
                        ${form.form_action ? `<span class="form-action">${form.form_action}</span>` : ''}
                    </div>
                </div>

                <div class="form-attributes">
                    <div class="form-attr">
                        <label>Action URL:</label>
                        <input type="text" value="${form.form_action || ''}" onchange="app.updateFormAttribute(${form.id}, 'form_action', this.value)" placeholder="Form action URL">
                    </div>
                    <div class="form-attr">
                        <label>Method:</label>
                        <select onchange="app.updateFormAttribute(${form.id}, 'form_method', this.value)">
                            <option value="GET" ${form.form_method === 'GET' ? 'selected' : ''}>GET</option>
                            <option value="POST" ${form.form_method === 'POST' ? 'selected' : ''}>POST</option>
                        </select>
                    </div>
                    <div class="form-attr">
                        <label>Encoding:</label>
                        <select onchange="app.updateFormAttribute(${form.id}, 'form_enctype', this.value)">
                            <option value="">Default</option>
                            <option value="application/x-www-form-urlencoded" ${form.form_enctype === 'application/x-www-form-urlencoded' ? 'selected' : ''}>URL Encoded</option>
                            <option value="multipart/form-data" ${form.form_enctype === 'multipart/form-data' ? 'selected' : ''}>Multipart</option>
                            <option value="text/plain" ${form.form_enctype === 'text/plain' ? 'selected' : ''}>Plain Text</option>
                        </select>
                    </div>
                </div>

                <div class="form-fields">
                    ${form.fields ? form.fields.map((field, fieldIndex) => this.renderFieldEditor(field, form.id, fieldIndex)).join('') : ''}
                </div>

                <div class="form-actions">
                    <button class="btn btn-sm btn-success" onclick="app.addFieldToForm(${form.id})">
                        <i class="fas fa-plus"></i> Add Field
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="app.saveFormChanges(${form.id})">
                        <i class="fas fa-save"></i> Save Form
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderFieldEditor(field, formId, fieldIndex) {
        return `
            <div class="field-editor" data-field-id="${field.id}">
                <div class="field-header">
                    <div class="field-title">
                        <strong>${field.field_name || 'Unnamed Field'}</strong>
                        <span class="field-type">${field.field_type}</span>
                        ${field.field_label ? `<span class="field-label">${field.field_label}</span>` : ''}
                    </div>
                    <div class="field-actions">
                        <button class="btn-icon" onclick="app.toggleFieldDetails(${field.id})" title="Edit Field">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="app.deleteFormField(${field.id})" title="Remove Field">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="field-details" id="field-details-${field.id}" style="display: none;">
                    <div class="field-grid">
                        <div class="field-group">
                            <label>Field Name:</label>
                            <input type="text" value="${field.field_name || ''}" onchange="app.updateFieldAttribute(${field.id}, 'field_name', this.value)">
                        </div>

                        <div class="field-group">
                            <label>Field Type:</label>
                            <select onchange="app.updateFieldAttribute(${field.id}, 'field_type', this.value)">
                                ${this.getFieldTypeOptions(field.field_type)}
                            </select>
                        </div>

                        <div class="field-group">
                            <label>Label:</label>
                            <input type="text" value="${field.field_label || ''}" onchange="app.updateFieldAttribute(${field.id}, 'field_label', this.value)">
                        </div>

                        <div class="field-group">
                            <label>Placeholder:</label>
                            <input type="text" value="${field.field_placeholder || ''}" onchange="app.updateFieldAttribute(${field.id}, 'field_placeholder', this.value)">
                        </div>

                        <div class="field-group">
                            <label>Default Value:</label>
                            <input type="text" value="${field.field_value || ''}" onchange="app.updateFieldAttribute(${field.id}, 'field_value', this.value)">
                        </div>

                        <div class="field-group">
                            <label class="checkbox-label">
                                <input type="checkbox" ${field.field_required ? 'checked' : ''} onchange="app.updateFieldAttribute(${field.id}, 'field_required', this.checked)">
                                Required
                            </label>
                        </div>

                        <div class="field-group">
                            <label class="checkbox-label">
                                <input type="checkbox" ${field.field_readonly ? 'checked' : ''} onchange="app.updateFieldAttribute(${field.id}, 'field_readonly', this.checked)">
                                Read Only
                            </label>
                        </div>

                        <div class="field-group">
                            <label class="checkbox-label">
                                <input type="checkbox" ${field.field_disabled ? 'checked' : ''} onchange="app.updateFieldAttribute(${field.id}, 'field_disabled', this.checked)">
                                Disabled
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getFieldTypeOptions(currentType) {
        const types = [
            'text', 'email', 'password', 'tel', 'url', 'search',
            'number', 'range', 'date', 'datetime-local', 'month', 'week', 'time',
            'color', 'file', 'hidden', 'radio', 'checkbox', 'select', 'textarea',
            'submit', 'button', 'reset'
        ];

        return types.map(type =>
            `<option value="${type}" ${type === currentType ? 'selected' : ''}>${type}</option>`
        ).join('');
    }

    // Form and Field Management Actions
    async updateFormAttribute(formId, attribute, value) {
        try {
            const updates = {};
            updates[attribute] = value;

            const formData = new FormData();
            formData.append('action', 'update_form');
            formData.append('form_id', formId);
            formData.append('updates', JSON.stringify(updates));

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Form updated successfully!');
            } else {
                this.showError(result.message || 'Failed to update form');
            }
        } catch (error) {
            console.error('Error updating form:', error);
            this.showError('Failed to update form');
        }
    }

    async updateFieldAttribute(fieldId, attribute, value) {
        try {
            const updates = {};
            updates[attribute] = value;

            const formData = new FormData();
            formData.append('action', 'update_field');
            formData.append('field_id', fieldId);
            formData.append('updates', JSON.stringify(updates));

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Field updated successfully!');
            } else {
                this.showError(result.message || 'Failed to update field');
            }
        } catch (error) {
            console.error('Error updating field:', error);
            this.showError('Failed to update field');
        }
    }

    toggleFieldDetails(fieldId) {
        const details = document.getElementById(`field-details-${fieldId}`);
        if (details) {
            details.style.display = details.style.display === 'none' ? 'block' : 'none';
        }
    }

    async deleteFormField(fieldId) {
        if (!confirm('Are you sure you want to delete this field?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'delete_field');
            formData.append('field_id', fieldId);

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Field deleted successfully!');
                // Remove the field element from DOM
                const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
                if (fieldElement) {
                    fieldElement.remove();
                }
            } else {
                this.showError(result.message || 'Failed to delete field');
            }
        } catch (error) {
            console.error('Error deleting field:', error);
            this.showError('Failed to delete field');
        }
    }

    async addFieldToForm(formId) {
        const fieldName = prompt('Enter field name:');
        if (!fieldName) return;

        const fieldType = prompt('Enter field type (text, email, password, etc.):', 'text');
        if (!fieldType) return;

        try {
            const fieldData = {
                field_name: fieldName,
                field_type: fieldType
            };

            const formData = new FormData();
            formData.append('action', 'add_field');
            formData.append('form_id', formId);
            formData.append('field_data', JSON.stringify(fieldData));

            const response = await fetch('includes/modify_form.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Field added successfully!');
                // Refresh the forms display
                this.loadPageForms();
            } else {
                this.showError(result.message || 'Failed to add field');
            }
        } catch (error) {
            console.error('Error adding field:', error);
            this.showError('Failed to add field');
        }
    }

    async saveFormChanges(formId) {
        this.showSuccess('Form changes saved!');
    }

    async saveAllForms() {
        this.showSuccess('All form changes saved!');
    }

    async resetForms() {
        if (confirm('Are you sure you want to reset all changes?')) {
            this.loadPageForms();
            this.showInfo('Forms reset to original state');
        }
    }

    // Enhanced Database Tab Methods
    initializeDatabaseTab() {
        // Database section navigation
        document.querySelectorAll('.db-nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchDatabaseSection(e.target.dataset.section);
            });
        });

        // Form submissions functionality
        document.getElementById('filter-submissions')?.addEventListener('click', this.filterSubmissions.bind(this));
        document.getElementById('export-submissions')?.addEventListener('click', this.exportSubmissions.bind(this));
        document.getElementById('refresh-submissions')?.addEventListener('click', this.loadFormSubmissions.bind(this));
    }

    switchDatabaseSection(section) {
        // Remove active class from all nav buttons and sections
        document.querySelectorAll('.db-nav-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.db-section').forEach(sec => sec.classList.remove('active'));

        // Add active class to selected nav button and section
        document.querySelector(`[data-section="${section}"]`).classList.add('active');
        document.getElementById(`db-${section}-section`).classList.add('active');

        // Load section-specific content
        switch (section) {
            case 'structure':
                this.loadDatabaseStructure();
                break;
            case 'submissions':
                this.loadFormSubmissions();
                break;
            case 'analytics':
                this.loadAnalytics();
                break;
        }
    }

    async loadFormSubmissions() {
        try {
            const response = await fetch('includes/get_form_submissions.php');
            const result = await response.json();

            if (result.success) {
                this.displayFormSubmissions(result.submissions);
                this.loadSubmissionFilters(result.pages, result.forms);
            } else {
                this.showError(result.message || 'Failed to load form submissions');
            }
        } catch (error) {
            console.error('Error loading form submissions:', error);
            this.showError('Failed to load form submissions');
        }
    }

    displayFormSubmissions(submissions) {
        const container = document.getElementById('submissions-container');

        if (!submissions || submissions.length === 0) {
            container.innerHTML = `
                <div class="no-submissions-message">
                    <i class="fas fa-paper-plane"></i>
                    <h3>No Form Submissions</h3>
                    <p>Form submissions from shared pages will appear here.</p>
                </div>
            `;
            return;
        }

        const table = `
            <table class="submissions-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Page</th>
                        <th>Form</th>
                        <th>Source</th>
                        <th>Data Preview</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${submissions.map(submission => `
                        <tr class="submission-row" onclick="app.showSubmissionDetails(${submission.id})">
                            <td class="submission-date">${new Date(submission.submitted_at).toLocaleString()}</td>
                            <td>${submission.page_title || submission.page_filename}</td>
                            <td>${submission.form_name || 'Unnamed Form'}</td>
                            <td>
                                <div class="submission-source">
                                    <span class="source-badge ${submission.share_id ? 'shared' : 'direct'}">
                                        ${submission.share_id ? 'Shared' : 'Direct'}
                                    </span>
                                </div>
                            </td>
                            <td class="submission-data">${this.formatSubmissionPreview(submission.form_data)}</td>
                            <td>
                                <button class="btn-icon" onclick="event.stopPropagation(); app.showSubmissionDetails(${submission.id})" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" onclick="event.stopPropagation(); app.exportSingleSubmission(${submission.id})" title="Export">
                                    <i class="fas fa-download"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = table;
    }

    formatSubmissionPreview(formDataJson) {
        try {
            // Handle null, undefined, or empty values
            if (!formDataJson || formDataJson === 'null' || formDataJson === '') {
                return 'No data';
            }

            // If it's already an object, use it directly
            let data;
            if (typeof formDataJson === 'object') {
                data = formDataJson;
            } else {
                // Try to parse as JSON
                data = JSON.parse(formDataJson);
            }

            // Check if data is empty
            if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
                return 'Empty form';
            }

            const fields = Object.keys(data).slice(0, 3); // Show first 3 fields
            const preview = fields.map(field => {
                const value = data[field];
                const displayValue = value && value.length > 20 ? value.substring(0, 20) + '...' : value;
                return `${field}: ${displayValue}`;
            }).join(', ');

            return preview.length > 50 ? preview.substring(0, 50) + '...' : preview;
        } catch (error) {
            console.error('Error formatting submission preview:', error, 'Data:', formDataJson);
            return 'Invalid data format';
        }
    }

    loadSubmissionFilters(pages, forms) {
        // Load page filter
        const pageFilter = document.getElementById('submissions-page-filter');
        if (pageFilter) {
            pageFilter.innerHTML = '<option value="">All Pages</option>';
            pages.forEach(page => {
                const option = document.createElement('option');
                option.value = page.id;
                option.textContent = page.title || page.filename;
                pageFilter.appendChild(option);
            });
        }

        // Load form filter
        const formFilter = document.getElementById('submissions-form-filter');
        if (formFilter) {
            formFilter.innerHTML = '<option value="">All Forms</option>';
            forms.forEach(form => {
                const option = document.createElement('option');
                option.value = form.id;
                option.textContent = form.form_name || `Form ${form.id}`;
                formFilter.appendChild(option);
            });
        }
    }

    async filterSubmissions() {
        const pageId = document.getElementById('submissions-page-filter')?.value;
        const formId = document.getElementById('submissions-form-filter')?.value;
        const dateFrom = document.getElementById('submissions-date-from')?.value;
        const dateTo = document.getElementById('submissions-date-to')?.value;

        const params = new URLSearchParams();
        if (pageId) params.append('page_id', pageId);
        if (formId) params.append('form_id', formId);
        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);

        try {
            const response = await fetch(`includes/get_form_submissions.php?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.displayFormSubmissions(result.submissions);
            } else {
                this.showError(result.message || 'Failed to filter submissions');
            }
        } catch (error) {
            console.error('Error filtering submissions:', error);
            this.showError('Failed to filter submissions');
        }
    }

    async showSubmissionDetails(submissionId) {
        try {
            const response = await fetch(`includes/get_form_submissions.php?id=${submissionId}`);
            const result = await response.json();

            if (result.success && result.submission) {
                this.displaySubmissionModal(result.submission);
            } else {
                this.showError('Failed to load submission details');
            }
        } catch (error) {
            console.error('Error loading submission details:', error);
            this.showError('Failed to load submission details');
        }
    }

    displaySubmissionModal(submission) {
        let formData;
        try {
            // Handle different data formats
            if (typeof submission.form_data === 'object') {
                formData = submission.form_data;
            } else if (submission.form_data) {
                formData = JSON.parse(submission.form_data);
            } else {
                formData = {};
            }
        } catch (error) {
            console.error('Error parsing form data:', error);
            formData = { error: 'Unable to parse form data', raw_data: submission.form_data };
        }

        const modal = document.createElement('div');
        modal.className = 'submission-modal';
        modal.innerHTML = `
            <div class="submission-modal-content">
                <div class="submission-modal-header">
                    <h3>Form Submission Details</h3>
                    <button class="modal-close-btn" onclick="this.closest('.submission-modal').remove()">×</button>
                </div>
                <div class="submission-details">
                    <div class="detail-group">
                        <h4>Submission Info</h4>
                        <div class="detail-item">
                            <span class="detail-label">Date:</span>
                            <span class="detail-value">${new Date(submission.submitted_at).toLocaleString()}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Page:</span>
                            <span class="detail-value">${submission.page_title || submission.page_filename}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Form:</span>
                            <span class="detail-value">${submission.form_name || 'Unnamed Form'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Source:</span>
                            <span class="detail-value">${submission.share_id ? 'Shared Page' : 'Direct Access'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">IP Address:</span>
                            <span class="detail-value">${submission.ip_address}</span>
                        </div>
                    </div>
                    <div class="detail-group">
                        <h4>Form Data</h4>
                        ${Object.entries(formData).map(([key, value]) => `
                            <div class="detail-item">
                                <span class="detail-label">${key}:</span>
                                <span class="detail-value">${value}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    async exportSubmissions() {
        try {
            const response = await fetch('includes/export_submissions.php');
            const blob = await response.blob();

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `form_submissions_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showSuccess('Submissions exported successfully!');
        } catch (error) {
            console.error('Error exporting submissions:', error);
            this.showError('Failed to export submissions');
        }
    }

    async loadAnalytics() {
        try {
            const response = await fetch('includes/get_analytics.php');
            const result = await response.json();

            if (result.success) {
                this.displayAnalytics(result.analytics);
            } else {
                this.showError(result.message || 'Failed to load analytics');
            }
        } catch (error) {
            console.error('Error loading analytics:', error);
            this.showError('Failed to load analytics');
        }
    }

    displayAnalytics(analytics) {
        // Update analytics cards
        document.getElementById('total-submissions').textContent = analytics.total_submissions || 0;
        document.getElementById('monthly-submissions').textContent = analytics.monthly_submissions || 0;
        document.getElementById('active-shares').textContent = analytics.active_shares || 0;
        document.getElementById('total-views').textContent = analytics.total_views || 0;
    }

    // Authentication Methods
    async checkAuthentication() {
        // If user is already authenticated (from server-side), use that data
        if (window.isAuthenticated && window.authUser) {
            this.currentUser = window.authUser;
            this.updateAuthUI();
            return;
        }

        try {
            const response = await fetch('includes/auth_check.php');
            const result = await response.json();

            this.currentUser = result.authenticated ? result.user : null;
            this.updateAuthUI();

        } catch (error) {
            console.error('Error checking authentication:', error);
            this.currentUser = null;
            this.updateAuthUI();
        }
    }

    updateAuthUI() {
        const headerAuth = document.getElementById('header-auth');
        const usersTab = document.querySelector('.tab-btn[data-tab="users"]');

        if (this.currentUser) {
            // User is logged in
            headerAuth.innerHTML = `
                <div class="auth-user-info">
                    <div class="user-avatar">${this.currentUser.full_name.charAt(0).toUpperCase()}</div>
                    <div class="user-details">
                        <div class="user-name">${this.currentUser.full_name}</div>
                        <div class="user-role">${this.currentUser.role}</div>
                    </div>
                </div>
                <div class="auth-actions">
                    <a href="logout.php" class="auth-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            `;

            // Show admin-only elements
            if (this.currentUser.role === 'admin') {
                document.querySelectorAll('.admin-only').forEach(el => {
                    el.classList.add('show');
                });
                if (usersTab) {
                    usersTab.style.display = 'flex';
                }
            }

        } else {
            // User is not logged in
            headerAuth.innerHTML = `
                <div class="auth-actions">
                    <a href="login.php" class="auth-btn login-btn">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </a>
                </div>
            `;

            // Hide admin-only elements
            document.querySelectorAll('.admin-only').forEach(el => {
                el.classList.remove('show');
            });
            if (usersTab) {
                usersTab.style.display = 'none';
            }
        }
    }

    setupUserManagementListeners() {
        // Set up listeners after DOM is ready
        setTimeout(() => {
            const createUserBtn = document.getElementById('create-user');
            const refreshUsersBtn = document.getElementById('refresh-users');

            if (createUserBtn) {
                createUserBtn.addEventListener('click', this.showUserModal.bind(this));
            }
            if (refreshUsersBtn) {
                refreshUsersBtn.addEventListener('click', this.loadUsers.bind(this));
            }

            // User form submission
            const userForm = document.getElementById('user-form');
            if (userForm) {
                userForm.addEventListener('submit', this.handleUserSubmit.bind(this));
            }
        }, 100);
    }

    // User Management Methods
    async loadUsers() {
        if (!this.currentUser || this.currentUser.role !== 'admin') {
            return;
        }

        try {
            const response = await fetch('includes/user_manager.php?action=get_users');
            const result = await response.json();

            if (result.success) {
                this.displayUsers(result.users);
            } else {
                this.showError(result.message || 'Failed to load users');
            }
        } catch (error) {
            console.error('Error loading users:', error);
            this.showError('Failed to load users');
        }
    }

    displayUsers(users) {
        const usersGrid = document.getElementById('users-grid');
        if (!usersGrid) return;

        if (users.length === 0) {
            usersGrid.innerHTML = `
                <div class="no-users-message">
                    <i class="fas fa-users"></i>
                    <h3>No Users Found</h3>
                    <p>Create your first user to get started.</p>
                </div>
            `;
            return;
        }

        usersGrid.innerHTML = users.map(user => `
            <div class="user-card">
                <div class="user-card-header">
                    <div class="user-avatar">${user.full_name.charAt(0).toUpperCase()}</div>
                    <div class="user-info">
                        <h4>${user.full_name}</h4>
                        <p>@${user.username}</p>
                    </div>
                </div>

                <div class="user-details">
                    <div class="user-detail">
                        <span class="label">Email:</span>
                        <span class="value">${user.email}</span>
                    </div>
                    <div class="user-detail">
                        <span class="label">Role:</span>
                        <span class="value">
                            <span class="user-role-badge ${user.role}">${user.role_text}</span>
                        </span>
                    </div>
                    <div class="user-detail">
                        <span class="label">Status:</span>
                        <span class="value">
                            <span class="user-status ${user.is_active ? 'active' : 'inactive'}">${user.status_text}</span>
                        </span>
                    </div>
                    <div class="user-detail">
                        <span class="label">Last Login:</span>
                        <span class="value">${user.last_login_formatted}</span>
                    </div>
                    <div class="user-detail">
                        <span class="label">Created:</span>
                        <span class="value">${user.created_at_formatted}</span>
                    </div>
                </div>

                <div class="user-actions">
                    <button class="btn btn-sm btn-primary" onclick="app.editUser(${user.id})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm ${user.is_active ? 'btn-warning' : 'btn-success'}"
                            onclick="app.toggleUserStatus(${user.id})">
                        <i class="fas fa-${user.is_active ? 'pause' : 'play'}"></i>
                        ${user.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                    ${user.id !== this.currentUser.id ? `
                        <button class="btn btn-sm btn-danger" onclick="app.deleteUser(${user.id}, '${user.username}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    showUserModal(userId = null) {
        const modal = document.getElementById('user-modal');
        const title = document.getElementById('user-modal-title');
        const form = document.getElementById('user-form');
        const submitBtn = document.getElementById('user-submit-btn');
        const passwordGroup = document.getElementById('password-group');
        const newPasswordGroup = document.getElementById('new-password-group');

        if (userId) {
            // Edit mode
            title.textContent = 'Edit User';
            submitBtn.innerHTML = '<i class="fas fa-save"></i> Update User';
            passwordGroup.style.display = 'none';
            newPasswordGroup.style.display = 'block';

            // Load user data
            this.loadUserForEdit(userId);
        } else {
            // Create mode
            title.textContent = 'Create User';
            submitBtn.innerHTML = '<i class="fas fa-save"></i> Create User';
            passwordGroup.style.display = 'block';
            newPasswordGroup.style.display = 'none';
            form.reset();
            document.getElementById('user-id').value = '';
        }

        modal.style.display = 'block';
    }

    closeUserModal() {
        document.getElementById('user-modal').style.display = 'none';
    }

    async handleUserSubmit(e) {
        e.preventDefault();

        const formData = new FormData();
        const userId = document.getElementById('user-id').value;

        if (userId) {
            // Update user
            formData.append('action', 'update_user');
            formData.append('user_id', userId);
            formData.append('username', document.getElementById('user-username').value);
            formData.append('email', document.getElementById('user-email').value);
            formData.append('full_name', document.getElementById('user-full-name').value);
            formData.append('role', document.getElementById('user-role').value);

            const newPassword = document.getElementById('user-new-password').value;
            if (newPassword) {
                formData.append('new_password', newPassword);
            }
        } else {
            // Create user
            formData.append('action', 'create_user');
            formData.append('username', document.getElementById('user-username').value);
            formData.append('email', document.getElementById('user-email').value);
            formData.append('full_name', document.getElementById('user-full-name').value);
            formData.append('role', document.getElementById('user-role').value);
            formData.append('password', document.getElementById('user-password').value);
        }

        try {
            const response = await fetch('includes/user_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(result.message);
                this.closeUserModal();
                this.loadUsers();
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            console.error('Error saving user:', error);
            this.showError('Failed to save user');
        }
    }

    async editUser(userId) {
        this.showUserModal(userId);
    }

    async loadUserForEdit(userId) {
        // This would typically load user data from the server
        // For now, we'll extract it from the displayed user card
        const userCards = document.querySelectorAll('.user-card');
        // Implementation would depend on how we store user data
        // For simplicity, we'll skip this for now
    }

    async toggleUserStatus(userId) {
        if (!confirm('Are you sure you want to change this user\'s status?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'toggle_user_status');
            formData.append('user_id', userId);

            const response = await fetch('includes/user_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(result.message);
                this.loadUsers();
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            console.error('Error toggling user status:', error);
            this.showError('Failed to update user status');
        }
    }

    async deleteUser(userId, username) {
        if (!confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'delete_user');
            formData.append('user_id', userId);

            const response = await fetch('includes/user_manager.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(result.message);
                this.loadUsers();
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            this.showError('Failed to delete user');
        }
    }
}

// Initialize the application when DOM is ready
let app;

function initializeApp() {
    app = new WebpageManager();
}

// Check if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for it
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM is already loaded
    initializeApp();
}
