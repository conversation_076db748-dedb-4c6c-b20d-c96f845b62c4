<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - Form Submitted Successfully</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            margin: 20px;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            animation: pulse 2s infinite;
        }
        
        .success-icon::after {
            content: '✓';
            color: white;
            font-size: 40px;
            font-weight: bold;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .message {
            color: #666;
            font-size: 1.2em;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
        
        .details h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .details p {
            margin: 10px 0;
            color: #6c757d;
        }
        
        .back-button {
            display: inline-block;
            background: #007cba;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .back-button:hover {
            background: #005a8b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,124,186,0.3);
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #adb5bd;
            font-size: 0.9em;
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .message {
                font-size: 1.1em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon"></div>
        
        <h1>Thank You!</h1>
        
        <div class="message">
            Your form has been submitted successfully. We appreciate you taking the time to contact us.
        </div>
        
        <div class="details">
            <h3>What happens next?</h3>
            <p><strong>✓ Confirmation:</strong> Your submission has been received and stored securely.</p>
            <p><strong>✓ Processing:</strong> Our team will review your information promptly.</p>
            <p><strong>✓ Response:</strong> We'll get back to you as soon as possible.</p>
        </div>
        
        <a href="javascript:history.back()" class="back-button">← Go Back</a>
        
        <div class="footer">
            <p>This page demonstrates the redirect functionality of the Webpage Manager form system.</p>
            <p id="timestamp"></p>
        </div>
    </div>

    <script>
        // Display current timestamp
        document.getElementById('timestamp').textContent = 'Redirected at: ' + new Date().toLocaleString();
        
        // Get URL parameters to show submission details
        const urlParams = new URLSearchParams(window.location.search);
        const submissionId = urlParams.get('submission_id');
        const formName = urlParams.get('form_name');
        
        if (submissionId || formName) {
            const details = document.querySelector('.details');
            let additionalInfo = '<h3>Submission Details</h3>';
            
            if (submissionId) {
                additionalInfo += '<p><strong>Submission ID:</strong> ' + submissionId + '</p>';
            }
            
            if (formName) {
                additionalInfo += '<p><strong>Form Name:</strong> ' + formName + '</p>';
            }
            
            details.innerHTML += additionalInfo;
        }
        
        // Auto-redirect back after 10 seconds (optional)
        let countdown = 10;
        const countdownElement = document.createElement('p');
        countdownElement.style.color = '#6c757d';
        countdownElement.style.fontSize = '0.9em';
        document.querySelector('.footer').appendChild(countdownElement);
        
        const updateCountdown = () => {
            if (countdown > 0) {
                countdownElement.textContent = `Auto-redirect in ${countdown} seconds (click anywhere to cancel)`;
                countdown--;
                setTimeout(updateCountdown, 1000);
            } else {
                window.history.back();
            }
        };
        
        // Start countdown
        setTimeout(updateCountdown, 2000);
        
        // Cancel auto-redirect on any user interaction
        document.addEventListener('click', () => {
            countdown = 0;
            countdownElement.textContent = 'Auto-redirect cancelled';
        });
        
        document.addEventListener('keydown', () => {
            countdown = 0;
            countdownElement.textContent = 'Auto-redirect cancelled';
        });
    </script>
</body>
</html>
