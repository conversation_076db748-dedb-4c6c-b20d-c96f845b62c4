<?php
require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get all generated tables
    $sql = "SELECT 
                gt.table_name,
                gt.table_sql,
                gt.created_at,
                f.form_name,
                f.form_action,
                p.filename,
                p.title as page_title
            FROM generated_tables gt
            JOIN forms f ON gt.form_id = f.id
            JOIN pages p ON f.page_id = p.id
            ORDER BY gt.created_at";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Generate SQL export
    $sqlExport = "-- Database Structure Export\n";
    $sqlExport .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
    $sqlExport .= "-- Webpage Manager - Form-based Database Structure\n\n";

    $sqlExport .= "-- Disable foreign key checks for import\n";
    $sqlExport .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";

    foreach ($tables as $table) {
        $sqlExport .= "-- Table: {$table['table_name']}\n";
        $sqlExport .= "-- Source: {$table['filename']}";
        if ($table['form_name']) {
            $sqlExport .= " (Form: {$table['form_name']})";
        }
        if ($table['form_action']) {
            $sqlExport .= " (Action: {$table['form_action']})";
        }
        $sqlExport .= "\n";
        $sqlExport .= "-- Created: {$table['created_at']}\n";
        
        // Add DROP TABLE statement
        $sqlExport .= "DROP TABLE IF EXISTS `{$table['table_name']}`;\n";
        
        // Add CREATE TABLE statement
        $sqlExport .= $table['table_sql'] . "\n\n";
    }

    $sqlExport .= "-- Re-enable foreign key checks\n";
    $sqlExport .= "SET FOREIGN_KEY_CHECKS = 1;\n\n";

    // Add some sample INSERT statements or comments about usage
    $sqlExport .= "-- Usage Examples:\n";
    $sqlExport .= "-- The tables above were generated from HTML forms found in your imported pages.\n";
    $sqlExport .= "-- Each table corresponds to a form and contains fields matching the form inputs.\n";
    $sqlExport .= "-- You can now use these tables to store form submissions.\n\n";

    foreach ($tables as $table) {
        $sqlExport .= "-- Example INSERT for {$table['table_name']}:\n";
        $sqlExport .= "-- INSERT INTO `{$table['table_name']}` (field1, field2) VALUES ('value1', 'value2');\n\n";
    }

    // Set headers for file download
    header('Content-Type: application/sql');
    header('Content-Disposition: attachment; filename="webpage_manager_database_' . date('Y-m-d_H-i-s') . '.sql"');
    header('Content-Length: ' . strlen($sqlExport));

    echo $sqlExport;

} catch (PDOException $e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
