<?php
/**
 * Test script for page viewing functionality
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get all pages
    $sql = "SELECT * FROM pages ORDER BY created_at DESC LIMIT 5";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h1>Page Viewing Test</h1>";
    
    if (empty($pages)) {
        echo "<p>No pages found. Please import some pages first.</p>";
        echo "<p><a href='index.html'>Go to Import Pages</a></p>";
    } else {
        echo "<h2>Available Pages:</h2>";
        foreach ($pages as $page) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h3>" . htmlspecialchars($page['title'] ?: $page['filename']) . "</h3>";
            echo "<p><strong>File:</strong> " . htmlspecialchars($page['filename']) . "</p>";
            echo "<p><strong>Size:</strong> " . number_format($page['file_size']) . " bytes</p>";
            echo "<p><strong>Created:</strong> " . $page['created_at'] . "</p>";
            
            // Test links
            echo "<div style='margin-top: 10px;'>";
            echo "<a href='view.php?id=" . $page['id'] . "' target='_blank' style='background: #667eea; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>View Page</a>";
            
            // Check if file exists
            if (file_exists($page['file_path'])) {
                echo "<span style='color: green;'>✓ File exists</span>";
            } else {
                echo "<span style='color: red;'>✗ File not found: " . htmlspecialchars($page['file_path']) . "</span>";
            }
            echo "</div>";
            echo "</div>";
        }
    }
    
    // Test sharing functionality
    echo "<h2>Sharing Test:</h2>";
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<p>Test the sharing manager API:</p>";
    echo "<button onclick='testSharingAPI()' style='background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer;'>Test Sharing API</button>";
    echo "<div id='sharing-result' style='margin-top: 10px;'></div>";
    echo "</div>";
    
    // Test asset serving
    echo "<h2>Asset Server Test:</h2>";
    $sql = "SELECT * FROM associated_files LIMIT 3";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assets)) {
        echo "<p>No associated files found.</p>";
    } else {
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        foreach ($assets as $asset) {
            echo "<div style='margin: 5px 0;'>";
            echo "<strong>" . htmlspecialchars($asset['original_filename']) . "</strong> ";
            echo "(" . htmlspecialchars($asset['file_type']) . ") ";
            echo "<a href='assets.php?file=" . urlencode($asset['filename']) . "' target='_blank'>Test Asset</a>";
            echo "</div>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}
?>

<script>
async function testSharingAPI() {
    const resultDiv = document.getElementById('sharing-result');
    resultDiv.innerHTML = 'Testing...';
    
    try {
        const response = await fetch('includes/sharing_manager.php?action=get_shares');
        const result = await response.json();
        
        if (result.success) {
            resultDiv.innerHTML = `
                <div style="color: green; background: #d4edda; padding: 10px; border-radius: 4px;">
                    ✓ Sharing API is working! Found ${result.shares.length} shares.
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style="color: red; background: #f8d7da; padding: 10px; border-radius: 4px;">
                    ✗ API Error: ${result.message}
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div style="color: red; background: #f8d7da; padding: 10px; border-radius: 4px;">
                ✗ Network Error: ${error.message}
            </div>
        `;
    }
}
</script>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}
</style>
