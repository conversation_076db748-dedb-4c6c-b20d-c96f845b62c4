<?php
require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get all generated tables with their form information
    $sql = "SELECT 
                gt.id,
                gt.table_name,
                gt.table_sql,
                gt.created_at,
                f.form_name,
                f.form_action,
                p.filename,
                p.title as page_title,
                COUNT(ff.id) as field_count
            FROM generated_tables gt
            JOIN forms f ON gt.form_id = f.id
            JOIN pages p ON f.page_id = p.id
            LEFT JOIN form_fields ff ON f.id = ff.form_id
            GROUP BY gt.id
            ORDER BY gt.created_at DESC";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $structure = [];

    foreach ($tables as $table) {
        // Parse the SQL to extract field information
        $fields = $this->parseTableSQL($table['table_sql']);
        
        $structure[] = [
            'id' => $table['id'],
            'name' => $table['table_name'],
            'form_name' => $table['form_name'],
            'form_action' => $table['form_action'],
            'page_filename' => $table['filename'],
            'page_title' => $table['page_title'],
            'field_count' => $table['field_count'],
            'created_at' => $table['created_at'],
            'fields' => $fields,
            'sql' => $table['table_sql']
        ];
    }

    echo json_encode($structure);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}

function parseTableSQL($sql) {
    $fields = [];
    
    // Extract field definitions from CREATE TABLE statement
    if (preg_match('/CREATE TABLE[^(]*\((.*)\)/is', $sql, $matches)) {
        $fieldDefinitions = $matches[1];
        
        // Split by comma, but be careful with constraints that might contain commas
        $lines = explode("\n", $fieldDefinitions);
        
        foreach ($lines as $line) {
            $line = trim($line, " \t\n\r\0\x0B,");
            
            // Skip empty lines and constraints
            if (empty($line) || 
                strpos($line, 'PRIMARY KEY') !== false ||
                strpos($line, 'UNIQUE KEY') !== false ||
                strpos($line, 'FOREIGN KEY') !== false ||
                strpos($line, 'INDEX') !== false ||
                strpos($line, 'KEY') !== false) {
                continue;
            }
            
            // Parse field definition
            if (preg_match('/`([^`]+)`\s+([A-Z]+(?:\([^)]+\))?)\s*(.*)/i', $line, $fieldMatches)) {
                $fieldName = $fieldMatches[1];
                $fieldType = $fieldMatches[2];
                $constraints = trim($fieldMatches[3]);
                
                $fields[] = [
                    'name' => $fieldName,
                    'type' => $fieldType,
                    'constraints' => $constraints
                ];
            }
        }
    }
    
    return $fields;
}
?>
