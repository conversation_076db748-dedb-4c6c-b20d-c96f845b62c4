<?php
/**
 * Project Manager for Webpage Manager v2.0
 * Handles project creation, management, and organization
 */

require_once '../config/database.php';

header('Content-Type: application/json');

class ProjectManager {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    public function createProject($name, $description = '', $color = '#667eea') {
        try {
            // Generate slug from name
            $slug = $this->generateSlug($name);
            
            // Check if slug already exists
            $sql = "SELECT id FROM projects WHERE slug = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$slug]);
            
            if ($stmt->fetch()) {
                $slug = $slug . '_' . time();
            }
            
            // Insert project
            $sql = "INSERT INTO projects (name, description, slug, color, created_by) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$name, $description, $slug, $color, 1]);
            
            $projectId = $this->db->lastInsertId();
            
            // Log activity
            $this->logActivity('project_created', 'project', $projectId, [
                'name' => $name,
                'slug' => $slug
            ]);
            
            return [
                'success' => true,
                'project' => [
                    'id' => $projectId,
                    'name' => $name,
                    'description' => $description,
                    'slug' => $slug,
                    'color' => $color
                ]
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function getProjects() {
        try {
            $sql = "SELECT 
                        p.*,
                        COUNT(pg.id) as page_count,
                        u.username as created_by_name
                    FROM projects p
                    LEFT JOIN pages pg ON p.id = pg.project_id
                    LEFT JOIN users u ON p.created_by = u.id
                    WHERE p.is_active = 1
                    GROUP BY p.id
                    ORDER BY p.created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ['success' => true, 'projects' => $projects];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function getProject($projectId) {
        try {
            $sql = "SELECT 
                        p.*,
                        COUNT(pg.id) as page_count,
                        u.username as created_by_name
                    FROM projects p
                    LEFT JOIN pages pg ON p.id = pg.project_id
                    LEFT JOIN users u ON p.created_by = u.id
                    WHERE p.id = ? AND p.is_active = 1
                    GROUP BY p.id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$projectId]);
            $project = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$project) {
                return ['success' => false, 'message' => 'Project not found'];
            }
            
            // Get project pages
            $sql = "SELECT * FROM pages WHERE project_id = ? AND status = 'active' ORDER BY created_at DESC";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$projectId]);
            $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $project['pages'] = $pages;
            
            return ['success' => true, 'project' => $project];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function updateProject($projectId, $updates) {
        try {
            $allowedFields = ['name', 'description', 'color', 'is_active'];
            $setParts = [];
            $values = [];
            
            foreach ($updates as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    $setParts[] = "$field = ?";
                    $values[] = $value;
                }
            }
            
            if (empty($setParts)) {
                return ['success' => false, 'message' => 'No valid fields to update'];
            }
            
            // Update slug if name changed
            if (isset($updates['name'])) {
                $slug = $this->generateSlug($updates['name']);
                $setParts[] = "slug = ?";
                $values[] = $slug;
            }
            
            $values[] = $projectId;
            $sql = "UPDATE projects SET " . implode(', ', $setParts) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($values);
            
            if ($result) {
                $this->logActivity('project_updated', 'project', $projectId, $updates);
                return ['success' => true, 'message' => 'Project updated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update project'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function deleteProject($projectId) {
        try {
            // Check if project has pages
            $sql = "SELECT COUNT(*) FROM pages WHERE project_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$projectId]);
            $pageCount = $stmt->fetchColumn();
            
            if ($pageCount > 0) {
                return ['success' => false, 'message' => 'Cannot delete project with existing pages. Move or delete pages first.'];
            }
            
            // Soft delete project
            $sql = "UPDATE projects SET is_active = 0 WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$projectId]);
            
            if ($result) {
                $this->logActivity('project_deleted', 'project', $projectId);
                return ['success' => true, 'message' => 'Project deleted successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to delete project'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    public function movePageToProject($pageId, $projectId) {
        try {
            // Verify project exists
            $sql = "SELECT id FROM projects WHERE id = ? AND is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$projectId]);
            
            if (!$stmt->fetch()) {
                return ['success' => false, 'message' => 'Project not found'];
            }
            
            // Update page project
            $sql = "UPDATE pages SET project_id = ? WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$projectId, $pageId]);
            
            if ($result) {
                $this->logActivity('page_moved', 'page', $pageId, ['new_project_id' => $projectId]);
                return ['success' => true, 'message' => 'Page moved to project successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to move page'];
            }
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    private function generateSlug($name) {
        // Convert to lowercase and replace spaces with hyphens
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        return $slug ?: 'project';
    }
    
    private function logActivity($action, $entityType, $entityId, $data = []) {
        try {
            $sql = "INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                1, // Default user ID
                $action,
                $entityType,
                $entityId,
                json_encode($data),
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // Log errors silently
            error_log("Activity logging failed: " . $e->getMessage());
        }
    }
}

// Handle the request
try {
    $manager = new ProjectManager();
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'create_project':
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $color = $_POST['color'] ?? '#667eea';
            $result = $manager->createProject($name, $description, $color);
            break;
            
        case 'get_projects':
            $result = $manager->getProjects();
            break;
            
        case 'get_project':
            $projectId = $_GET['project_id'] ?? 0;
            $result = $manager->getProject($projectId);
            break;
            
        case 'update_project':
            $projectId = $_POST['project_id'] ?? 0;
            $updates = $_POST['updates'] ?? [];
            $result = $manager->updateProject($projectId, $updates);
            break;
            
        case 'delete_project':
            $projectId = $_POST['project_id'] ?? 0;
            $result = $manager->deleteProject($projectId);
            break;
            
        case 'move_page':
            $pageId = $_POST['page_id'] ?? 0;
            $projectId = $_POST['project_id'] ?? 0;
            $result = $manager->movePageToProject($pageId, $projectId);
            break;
            
        default:
            $result = ['success' => false, 'message' => 'Invalid action'];
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
