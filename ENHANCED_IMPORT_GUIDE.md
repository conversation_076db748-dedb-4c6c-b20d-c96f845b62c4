# Enhanced Import System Guide

This guide explains the new enhanced import system that supports individual page imports with associated files and ZIP archive uploads.

## Overview

The enhanced import system provides two powerful ways to import web content:

### 🔄 **Individual File Import**
- Upload HTML pages along with their CSS, JS, and image files
- Automatic asset detection and linking
- Maintains original file relationships
- Perfect for single pages or small collections

### 📦 **ZIP Archive Import**
- Upload complete websites as ZIP files
- Preserves folder structure and file relationships
- Automatically extracts and processes all content
- Ideal for complete website imports

## Key Features

### 🎯 **Smart Asset Detection**
- **Automatic Discovery**: Scans HTML for referenced assets
- **Path Resolution**: Handles relative and absolute paths correctly
- **File Linking**: Creates database relationships between pages and assets
- **Asset Serving**: Ensures shared pages display correctly with all assets

### 🗂️ **Project Organization**
- **Project-Based Import**: Organize pages into logical projects
- **Custom Projects**: Create projects with names, descriptions, and colors
- **Easy Management**: Move pages between projects as needed
- **Visual Organization**: Color-coded project identification

### 🔍 **Enhanced Analysis**
- **Form Detection**: Automatically finds and analyzes forms
- **Metadata Extraction**: Captures page titles, descriptions, keywords
- **File Validation**: Ensures only safe file types are processed
- **Error Reporting**: Detailed feedback on import success/failure

## How to Use

### 1. **Choose Import Mode**

**Individual Files Mode:**
- Select "Individual Files" radio button
- Upload HTML pages with their associated assets
- Files are automatically linked based on references in HTML

**ZIP Archive Mode:**
- Select "ZIP Archive" radio button
- Upload ZIP files containing complete websites
- Folder structure is preserved and analyzed

### 2. **Select Project**

**Use Existing Project:**
- Choose from the dropdown list
- Default project is available by default

**Create New Project:**
- Click "New Project" button
- Enter project name and description
- Choose a color for visual identification
- Project is immediately available for use

### 3. **Upload Files**

**Drag & Drop:**
- Drag files or ZIP archives to the upload area
- Multiple files supported in both modes
- Visual feedback during upload process

**File Selection:**
- Click the upload area to open file browser
- Select multiple files or ZIP archives
- Supported formats automatically filtered

### 4. **Monitor Progress**

**Upload Progress:**
- Real-time progress bar during upload
- Status updates for each file processed
- Error reporting for failed uploads

**Detailed Results:**
- Success/failure status for each file
- ZIP extraction details with page counts
- Asset linking information
- Quick action buttons for viewing/sharing

## Supported File Types

### 📄 **Web Files**
- **HTML**: .html, .htm
- **Stylesheets**: .css
- **Scripts**: .js, .mjs
- **Images**: .png, .jpg, .jpeg, .gif, .svg, .webp, .bmp
- **Fonts**: .woff, .woff2, .ttf, .otf
- **Documents**: .pdf, .txt

### 📦 **Archive Files**
- **ZIP**: .zip (with automatic extraction)

### 🔒 **Security Features**
- File type validation
- Size limit enforcement (50MB for ZIP files)
- Path traversal protection
- Malicious file detection

## ZIP Archive Structure

### ✅ **Recommended Structure**
```
website.zip
├── index.html
├── about.html
├── css/
│   ├── style.css
│   └── bootstrap.css
├── js/
│   ├── main.js
│   └── jquery.js
├── images/
│   ├── logo.png
│   └── background.jpg
└── fonts/
    ├── font.woff2
    └── icons.ttf
```

### 🎯 **Processing Logic**
1. **Extract ZIP**: Temporary extraction to secure directory
2. **Find HTML Files**: Locate all .html and .htm files
3. **Analyze References**: Parse HTML for asset references
4. **Resolve Paths**: Handle relative and absolute asset paths
5. **Copy Assets**: Move referenced files to asset directory
6. **Create Database Entries**: Link pages with their assets
7. **Cleanup**: Remove temporary extraction directory

## Asset Linking

### 🔗 **How It Works**
The system automatically detects and links assets by scanning HTML for:

**CSS Links:**
```html
<link rel="stylesheet" href="css/style.css">
<link href="../styles/main.css" rel="stylesheet">
```

**JavaScript Sources:**
```html
<script src="js/main.js"></script>
<script type="module" src="./modules/app.js"></script>
```

**Image Sources:**
```html
<img src="images/logo.png" alt="Logo">
<img src="../assets/background.jpg">
```

**CSS Background Images:**
```css
background-image: url('images/bg.jpg');
background: url("../assets/texture.png");
```

### 🎨 **Path Resolution**
- **Relative Paths**: `css/style.css`, `../images/logo.png`
- **Absolute Paths**: `/assets/main.css`, `/images/header.jpg`
- **Root-Relative**: Resolved from ZIP root directory
- **External URLs**: Ignored (not downloaded)

## Project Management

### 📁 **Creating Projects**
1. Click "New Project" button in import interface
2. Enter descriptive project name
3. Add optional description for context
4. Choose color for visual identification
5. Project becomes immediately available

### 🎨 **Project Features**
- **Visual Organization**: Color-coded identification
- **Page Grouping**: Logical organization of related pages
- **Easy Navigation**: Quick project switching
- **Statistics**: Page counts and project metrics

### 📊 **Project Benefits**
- **Better Organization**: Group related pages together
- **Team Collaboration**: Share projects with team members
- **Client Work**: Separate different client projects
- **Version Control**: Organize different versions of sites

## Best Practices

### 📦 **For ZIP Imports**
1. **Clean Structure**: Organize files in logical folders
2. **Relative Paths**: Use relative paths in HTML/CSS
3. **Complete Assets**: Include all referenced files
4. **Reasonable Size**: Keep ZIP files under 50MB
5. **Test Locally**: Verify site works before zipping

### 📄 **For Individual Imports**
1. **Upload HTML First**: Start with main HTML files
2. **Include Assets**: Upload all referenced CSS, JS, images
3. **Consistent Naming**: Use clear, descriptive filenames
4. **Check References**: Ensure HTML references match uploaded files
5. **Organize by Project**: Use projects to group related pages

### 🔧 **General Tips**
1. **File Names**: Use web-safe filenames (no spaces, special chars)
2. **Image Optimization**: Optimize images for web before upload
3. **CSS/JS Minification**: Consider minifying for production
4. **Testing**: Always test imported pages after upload
5. **Backup**: Keep original files as backup

## Troubleshooting

### ❌ **Common Issues**

**Assets Not Loading:**
- Check file paths in HTML/CSS
- Ensure all referenced files were uploaded
- Verify file names match exactly (case-sensitive)

**ZIP Extraction Fails:**
- Check ZIP file integrity
- Ensure ZIP is not password protected
- Verify file size is under limit

**Upload Errors:**
- Check file types are supported
- Verify file sizes are within limits
- Ensure stable internet connection

**Missing Assets:**
- Check HTML references use relative paths
- Ensure assets are in ZIP file
- Verify folder structure is correct

### 🔧 **Solutions**

**Re-upload Missing Files:**
1. Switch to Individual Files mode
2. Upload missing assets
3. System will automatically link them

**Fix Path Issues:**
1. Edit HTML to use correct relative paths
2. Re-upload the corrected files
3. System will update asset links

**Large File Handling:**
1. Split large ZIPs into smaller archives
2. Upload assets separately if needed
3. Optimize images to reduce size

## Advanced Features

### 🔍 **Automatic Analysis**
- **Form Detection**: Finds all forms and fields
- **Metadata Extraction**: Captures SEO information
- **Asset Inventory**: Catalogs all referenced files
- **Validation Rules**: Extracts form validation requirements

### 📊 **Import Statistics**
- **Success Rates**: Track successful vs failed imports
- **File Counts**: Monitor pages and assets imported
- **Project Metrics**: View project-level statistics
- **Error Tracking**: Detailed error reporting and logging

### 🔄 **Integration Features**
- **Sharing Ready**: Imported pages immediately shareable
- **Form Editing**: All forms available for modification
- **Database Generation**: Ready for table creation
- **Version Control**: Track changes and updates

This enhanced import system provides a robust foundation for managing web content with proper asset relationships and organizational structure!
