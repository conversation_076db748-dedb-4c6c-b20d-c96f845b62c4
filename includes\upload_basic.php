<?php
// Basic upload handler for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

// Log everything for debugging
error_log("Upload request received");
error_log("POST: " . print_r($_POST, true));
error_log("FILES: " . print_r($_FILES, true));

try {
    // Check if files were uploaded
    if (!isset($_FILES) || empty($_FILES)) {
        echo json_encode([
            'success' => false,
            'message' => 'No files received',
            'debug' => [
                'POST' => $_POST,
                'FILES' => $_FILES,
                'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD']
            ]
        ]);
        exit;
    }

    // Create uploads directory
    $uploadDir = dirname(__DIR__) . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0777, true)) {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to create uploads directory',
                'upload_dir' => $uploadDir
            ]);
            exit;
        }
    }

    $results = [];
    $files = $_FILES;

    // Handle different file input formats
    if (isset($files['files'])) {
        $files = $files['files'];
    } else {
        // Find the first file input
        $files = reset($_FILES);
    }

    // Handle single file vs multiple files
    if (!is_array($files['name'])) {
        $files = [
            'name' => [$files['name']],
            'tmp_name' => [$files['tmp_name']],
            'size' => [$files['size']],
            'type' => [$files['type']],
            'error' => [$files['error']]
        ];
    }

    // Process each file
    for ($i = 0; $i < count($files['name']); $i++) {
        $fileName = $files['name'][$i];
        $tmpName = $files['tmp_name'][$i];
        $fileSize = $files['size'][$i];
        $fileType = $files['type'][$i];
        $fileError = $files['error'][$i];

        if ($fileError !== UPLOAD_ERR_OK) {
            $results[] = [
                'success' => false,
                'name' => $fileName,
                'message' => 'Upload error code: ' . $fileError,
                'error_code' => $fileError
            ];
            continue;
        }

        // Generate unique filename
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $baseName = pathinfo($fileName, PATHINFO_FILENAME);
        $uniqueName = $baseName . '_' . time() . '_' . uniqid() . '.' . $extension;
        $targetPath = $uploadDir . $uniqueName;

        // Move uploaded file
        if (move_uploaded_file($tmpName, $targetPath)) {
            $results[] = [
                'success' => true,
                'name' => $fileName,
                'message' => 'File uploaded successfully',
                'unique_name' => $uniqueName,
                'size' => $fileSize,
                'type' => $fileType,
                'path' => $targetPath
            ];
        } else {
            $results[] = [
                'success' => false,
                'name' => $fileName,
                'message' => 'Failed to move uploaded file',
                'tmp_name' => $tmpName,
                'target_path' => $targetPath,
                'upload_dir_writable' => is_writable($uploadDir)
            ];
        }
    }

    echo json_encode([
        'success' => true,
        'message' => 'Upload processing completed',
        'files' => $results,
        'debug' => [
            'upload_dir' => $uploadDir,
            'upload_dir_exists' => is_dir($uploadDir),
            'upload_dir_writable' => is_writable($uploadDir),
            'files_count' => count($results)
        ]
    ]);

} catch (Exception $e) {
    error_log("Upload exception: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
