<?php
/**
 * Test Enhanced System
 * Comprehensive test for form data collection and ZIP extraction
 */

require_once 'config/database.php';
require_once 'includes/deployment_config.php';

echo "<h1>Enhanced System Test</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Test deployment configuration
    echo "<h2>Deployment Configuration Test</h2>";
    
    $config = new DeploymentConfig();
    $status = $config->getDeploymentStatus();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    foreach ($status as $key => $value) {
        $displayValue = is_bool($value) ? ($value ? 'Yes' : 'No') : $value;
        $statusIcon = (is_bool($value) && $value) || (!is_bool($value) && !empty($value)) ? '✅' : '❌';
        echo "<tr><td>" . ucfirst(str_replace('_', ' ', $key)) . "</td><td>$displayValue</td><td>$statusIcon</td></tr>";
    }
    echo "</table>";
    
    // Test deployment functionality
    echo "<h2>Deployment Tests</h2>";
    
    $tests = $config->testDeployment();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Test</th><th>Status</th><th>Message</th></tr>";
    
    foreach ($tests as $test) {
        $statusIcon = $test['status'] ? '✅' : '❌';
        echo "<tr><td>{$test['name']}</td><td>$statusIcon</td><td>{$test['message']}</td></tr>";
    }
    echo "</table>";
    
    // Test enhanced form submissions table
    echo "<h2>Enhanced Form Submissions Table Test</h2>";
    
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $expectedColumns = [
        'page_id', 'form_id', 'share_id', 'form_name', 'submission_data',
        'visitor_session', 'ip_address', 'user_agent', 'referrer',
        'browser_name', 'browser_version', 'os_name', 'device_type',
        'submission_source', 'status', 'priority', 'tags', 'notes'
    ];
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Present</th></tr>";
    
    $presentColumns = array_column($columns, 'Field');
    
    foreach ($expectedColumns as $expectedCol) {
        $isPresent = in_array($expectedCol, $presentColumns);
        $statusIcon = $isPresent ? '✅' : '❌';
        $type = '';
        
        if ($isPresent) {
            foreach ($columns as $col) {
                if ($col['Field'] === $expectedCol) {
                    $type = $col['Type'];
                    break;
                }
            }
        }
        
        echo "<tr><td>$expectedCol</td><td>$type</td><td>$statusIcon</td></tr>";
    }
    echo "</table>";
    
    // Test ZIP extractions table
    echo "<h2>ZIP Extractions Table Test</h2>";
    
    try {
        $sql = "DESCRIBE zip_extractions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $zipColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>✅ zip_extractions table exists</p>";
        echo "<p><strong>Columns:</strong> " . implode(', ', array_column($zipColumns, 'Field')) . "</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ zip_extractions table missing or inaccessible</p>";
    }
    
    // Test form submission with enhanced data
    echo "<h2>Enhanced Form Submission Test</h2>";
    
    // Get a test share
    $sql = "SELECT ps.*, p.title as page_title 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.is_active = 1 AND ps.show_forms = 1 
            LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $testShare = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testShare) {
        echo "<p>✅ Found test share: {$testShare['page_title']}</p>";
        
        // Simulate enhanced form submission
        $testData = [
            'page_id' => $testShare['page_id'],
            'form_id' => null,
            'share_id' => $testShare['id'],
            'form_name' => 'test_enhanced_form',
            'submission_data' => json_encode([
                'name' => 'Enhanced Test User',
                'email' => '<EMAIL>',
                'message' => 'Testing enhanced form submission system'
            ]),
            'visitor_session' => hash('sha256', '127.0.0.1' . 'test-agent' . date('Y-m-d')),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Enhanced Test Agent',
            'referrer' => 'http://test.com',
            'browser_name' => 'Chrome',
            'browser_version' => '91.0',
            'os_name' => 'Windows',
            'device_type' => 'desktop',
            'submission_source' => 'shared'
        ];
        
        $sql = "INSERT INTO form_submissions (
                    page_id, form_id, share_id, form_name, submission_data,
                    visitor_session, ip_address, user_agent, referrer,
                    browser_name, browser_version, os_name, device_type,
                    submission_source, submitted_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $testData['page_id'],
            $testData['form_id'],
            $testData['share_id'],
            $testData['form_name'],
            $testData['submission_data'],
            $testData['visitor_session'],
            $testData['ip_address'],
            $testData['user_agent'],
            $testData['referrer'],
            $testData['browser_name'],
            $testData['browser_version'],
            $testData['os_name'],
            $testData['device_type'],
            $testData['submission_source']
        ]);
        
        $submissionId = $db->lastInsertId();
        echo "<p>✅ Enhanced form submission created with ID: $submissionId</p>";
        
        // Verify the submission
        $sql = "SELECT * FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$submissionId]);
        $submission = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($submission) {
            echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Stored Enhanced Data:</h4>";
            echo "<p><strong>Form Name:</strong> {$submission['form_name']}</p>";
            echo "<p><strong>Visitor Session:</strong> {$submission['visitor_session']}</p>";
            echo "<p><strong>Browser:</strong> {$submission['browser_name']} {$submission['browser_version']}</p>";
            echo "<p><strong>OS:</strong> {$submission['os_name']}</p>";
            echo "<p><strong>Device:</strong> {$submission['device_type']}</p>";
            echo "<p><strong>Source:</strong> {$submission['submission_source']}</p>";
            echo "<p><strong>Status:</strong> {$submission['status']}</p>";
            echo "<p><strong>Priority:</strong> {$submission['priority']}</p>";
            echo "</div>";
        }
        
    } else {
        echo "<p>⚠️ No test shares available. Creating one...</p>";
        
        // Get a page to share
        $sql = "SELECT * FROM pages LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $page = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($page) {
            $shareToken = bin2hex(random_bytes(16));
            $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
            
            $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
                    VALUES (?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$page['id'], $shareToken, $shortCode, 'Enhanced Test Share']);
            
            $shareId = $db->lastInsertId();
            echo "<p>✅ Test share created with ID: $shareId</p>";
            
            $shareUrl = $config->generateShareUrl($shareToken);
            echo "<p><strong>Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
        }
    }
    
    // Test URL generation
    echo "<h2>URL Generation Test</h2>";
    
    $baseUrl = $config->getBaseUrl();
    $shareUrl = $config->generateShareUrl('test-token');
    $shortUrl = $config->generateShortUrl('TEST123');
    
    echo "<p><strong>Base URL:</strong> $baseUrl</p>";
    echo "<p><strong>Sample Share URL:</strong> $shareUrl</p>";
    echo "<p><strong>Sample Short URL:</strong> $shortUrl</p>";
    
    // Show existing submissions with enhanced data
    echo "<h2>Recent Enhanced Submissions</h2>";
    
    $sql = "SELECT 
                fs.*,
                p.title as page_title,
                p.original_filename,
                ps.title as share_title
            FROM form_submissions fs
            LEFT JOIN pages p ON fs.page_id = p.id
            LEFT JOIN page_shares ps ON fs.share_id = ps.id
            ORDER BY fs.submitted_at DESC
            LIMIT 5";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($submissions) > 0) {
        echo "<table border='1' cellpadding='5' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th>ID</th><th>Page</th><th>Form</th><th>Browser</th><th>Device</th><th>Source</th><th>Status</th><th>Submitted</th>";
        echo "</tr>";
        
        foreach ($submissions as $sub) {
            echo "<tr>";
            echo "<td>{$sub['id']}</td>";
            echo "<td>{$sub['page_title']}<br><small>{$sub['original_filename']}</small></td>";
            echo "<td>{$sub['form_name']}</td>";
            echo "<td>{$sub['browser_name']} {$sub['browser_version']}<br><small>{$sub['os_name']}</small></td>";
            echo "<td>{$sub['device_type']}</td>";
            echo "<td>{$sub['submission_source']}</td>";
            echo "<td>{$sub['status']}</td>";
            echo "<td>{$sub['submitted_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No submissions found yet.</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>System Status Summary</h2>";
echo "<div style='background: #e7f3ff; color: #004085; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Enhanced Features Ready:</h3>";
echo "<ul>";
echo "<li><strong>Enhanced Form Data Collection:</strong> Captures browser, device, session, and visitor analytics</li>";
echo "<li><strong>ZIP File Processing:</strong> Automatically detects index pages and extracts complete websites</li>";
echo "<li><strong>Online Deployment Support:</strong> Configurable for production environments</li>";
echo "<li><strong>Advanced Analytics:</strong> Detailed visitor tracking and form submission analysis</li>";
echo "<li><strong>Improved Database Schema:</strong> Enhanced tables for comprehensive data storage</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Test Form Submissions:</strong> <a href='enhanced_submit_form.php'>Enhanced Submit Handler</a></li>";
echo "<li><strong>Upload ZIP Files:</strong> Test automatic index page detection</li>";
echo "<li><strong>Create Shares:</strong> Test enhanced URL generation</li>";
echo "<li><strong>View Analytics:</strong> Check enhanced submission data in database tab</li>";
echo "<li><strong>Deploy Online:</strong> Configure for production environment</li>";
echo "</ol>";

echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "<li><a href='test_form_submission.php'>Original Form Test</a></li>";
echo "<li><a href='enhanced_submit_form.php'>Enhanced Submit Handler</a></li>";
echo "<li><a href='includes/deployment_config.php'>Deployment Config</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
h1, h2, h3, h4 { color: #333; }
</style>
