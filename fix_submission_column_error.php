<?php
/**
 * Fix Form Submission Column Error
 * Diagnose and fix the "submission_data" column not found error
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Fix Form Submission Column Error</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>1. Diagnose Database Schema</h2>";
    
    // Check if form_submissions table exists
    $sql = "SHOW TABLES LIKE 'form_submissions'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        echo "<p style='color: red;'>❌ form_submissions table does not exist!</p>";
        echo "<h3>Creating form_submissions table...</h3>";
        
        // Create the table with the correct schema
        $sql = "CREATE TABLE form_submissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            form_id INT NULL,
            share_id INT NULL,
            form_name VARCHAR(255) NULL,
            submission_data JSON NOT NULL,
            visitor_session VARCHAR(64) NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            referrer VARCHAR(500) NULL,
            country_code VARCHAR(2) NULL,
            city VARCHAR(100) NULL,
            browser_name VARCHAR(50) NULL,
            browser_version VARCHAR(20) NULL,
            os_name VARCHAR(50) NULL,
            device_type ENUM('desktop', 'mobile', 'tablet') DEFAULT 'desktop',
            submission_source ENUM('direct', 'shared', 'embedded') DEFAULT 'shared',
            status ENUM('pending', 'processed', 'archived', 'spam', 'reviewed') DEFAULT 'pending',
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            tags JSON NULL,
            notes TEXT NULL,
            processed_at TIMESTAMP NULL,
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
            FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE SET NULL,
            FOREIGN KEY (share_id) REFERENCES page_shares(id) ON DELETE SET NULL,
            INDEX idx_page_id (page_id),
            INDEX idx_form_id (form_id),
            INDEX idx_share_id (share_id),
            INDEX idx_status (status),
            INDEX idx_submitted_at (submitted_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        
        echo "<p style='color: green;'>✅ form_submissions table created successfully!</p>";
    } else {
        echo "<p style='color: green;'>✅ form_submissions table exists</p>";
    }
    
    // Check current table structure
    echo "<h3>Current Table Structure</h3>";
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f2f2f2;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $columnNames = [];
    foreach ($columns as $column) {
        $columnNames[] = $column['Field'];
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>2. Check for Missing Columns</h2>";
    
    // Required columns for the system to work
    $requiredColumns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'page_id' => 'INT NOT NULL',
        'form_id' => 'INT NULL',
        'share_id' => 'INT NULL',
        'submission_data' => 'JSON NOT NULL',
        'ip_address' => 'VARCHAR(45) NULL',
        'user_agent' => 'TEXT NULL',
        'referrer' => 'VARCHAR(500) NULL',
        'submitted_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
    ];
    
    $missingColumns = [];
    $alterStatements = [];
    
    foreach ($requiredColumns as $columnName => $columnDef) {
        if (!in_array($columnName, $columnNames)) {
            $missingColumns[] = $columnName;
            
            // Generate ALTER statement
            if ($columnName === 'submission_data') {
                $alterStatements[] = "ADD COLUMN submission_data JSON NOT NULL";
            } elseif ($columnName === 'submitted_at') {
                $alterStatements[] = "ADD COLUMN submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
            } else {
                $alterStatements[] = "ADD COLUMN $columnName $columnDef";
            }
        }
    }
    
    if (empty($missingColumns)) {
        echo "<p style='color: green;'>✅ All required columns are present</p>";
    } else {
        echo "<p style='color: red;'>❌ Missing columns: " . implode(', ', $missingColumns) . "</p>";
        
        echo "<h3>Adding Missing Columns</h3>";
        
        foreach ($alterStatements as $alterStatement) {
            try {
                $sql = "ALTER TABLE form_submissions $alterStatement";
                echo "<p>Executing: <code>$sql</code></p>";
                $stmt = $db->prepare($sql);
                $stmt->execute();
                echo "<p style='color: green;'>✅ Column added successfully</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Error adding column: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>3. Check for Legacy Column Names</h2>";
    
    // Check if there are legacy column names that need to be handled
    $legacyColumns = ['form_data', 'created_at'];
    $foundLegacy = [];
    
    foreach ($legacyColumns as $legacyColumn) {
        if (in_array($legacyColumn, $columnNames)) {
            $foundLegacy[] = $legacyColumn;
        }
    }
    
    if (!empty($foundLegacy)) {
        echo "<p style='color: orange;'>⚠️ Found legacy columns: " . implode(', ', $foundLegacy) . "</p>";
        
        // If form_data exists but submission_data doesn't, rename it
        if (in_array('form_data', $foundLegacy) && !in_array('submission_data', $columnNames)) {
            echo "<h3>Renaming form_data to submission_data</h3>";
            try {
                $sql = "ALTER TABLE form_submissions CHANGE form_data submission_data JSON NOT NULL";
                $stmt = $db->prepare($sql);
                $stmt->execute();
                echo "<p style='color: green;'>✅ Renamed form_data to submission_data</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Error renaming column: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>4. Update Form Submission Handlers</h2>";
    
    // Check which column name is actually being used
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $finalColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    
    $dataColumn = 'submission_data';
    if (in_array('form_data', $finalColumns) && !in_array('submission_data', $finalColumns)) {
        $dataColumn = 'form_data';
    }
    
    echo "<p><strong>Data column to use:</strong> <code>$dataColumn</code></p>";
    
    // Update submit_form.php to use the correct column name
    $submitFormPath = 'submit_form.php';
    if (file_exists($submitFormPath)) {
        $content = file_get_contents($submitFormPath);
        
        if ($dataColumn === 'form_data') {
            $content = str_replace('submission_data', 'form_data', $content);
            echo "<p>✅ Updated submit_form.php to use 'form_data' column</p>";
        } else {
            $content = str_replace('form_data', 'submission_data', $content);
            echo "<p>✅ Updated submit_form.php to use 'submission_data' column</p>";
        }
        
        file_put_contents($submitFormPath, $content);
    }
    
    // Update enhanced_submit_form.php to use the correct column name
    $enhancedSubmitFormPath = 'enhanced_submit_form.php';
    if (file_exists($enhancedSubmitFormPath)) {
        $content = file_get_contents($enhancedSubmitFormPath);
        
        if ($dataColumn === 'form_data') {
            $content = str_replace('submission_data', 'form_data', $content);
            echo "<p>✅ Updated enhanced_submit_form.php to use 'form_data' column</p>";
        } else {
            $content = str_replace('form_data', 'submission_data', $content);
            echo "<p>✅ Updated enhanced_submit_form.php to use 'submission_data' column</p>";
        }
        
        file_put_contents($enhancedSubmitFormPath, $content);
    }
    
    echo "<h2>5. Test Form Submission</h2>";
    
    // Create a test submission to verify everything works
    $testData = [
        'test_field' => 'test_value',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    try {
        $sql = "INSERT INTO form_submissions (page_id, form_name, $dataColumn, ip_address, submitted_at) 
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            1, // Test page ID
            'test_form',
            json_encode($testData),
            '127.0.0.1'
        ]);
        
        $testId = $db->lastInsertId();
        echo "<p style='color: green;'>✅ Test submission created successfully (ID: $testId)</p>";
        
        // Retrieve the test submission
        $sql = "SELECT * FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testId]);
        $testSubmission = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testSubmission) {
            echo "<p style='color: green;'>✅ Test submission retrieved successfully</p>";
            echo "<p><strong>Stored data:</strong> " . htmlspecialchars($testSubmission[$dataColumn]) . "</p>";
            
            // Clean up test submission
            $sql = "DELETE FROM form_submissions WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$testId]);
            echo "<p>✅ Test submission cleaned up</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Test submission failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. Summary</h2>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
    echo "<h3>✅ Database Schema Fixed!</h3>";
    echo "<ul>";
    echo "<li>✅ form_submissions table structure verified</li>";
    echo "<li>✅ Missing columns added</li>";
    echo "<li>✅ Legacy column names handled</li>";
    echo "<li>✅ Form submission handlers updated</li>";
    echo "<li>✅ Test submission successful</li>";
    echo "</ul>";
    echo "<p><strong>The 'submission_data' column error should now be resolved!</strong></p>";
    echo "</div>";
    
    echo "<h2>Quick Test Links</h2>";
    echo "<div style='display: flex; gap: 15px; flex-wrap: wrap; margin: 20px 0;'>";
    echo "<a href='test_data_collection_system.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Test Data Collection</a>";
    echo "<a href='enhanced_data_collection.php' target='_blank' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Data Collection Manager</a>";
    echo "<a href='index.html' target='_blank' style='background: #6f42c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Main Application</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}

?>

<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: 'Courier New', monospace; }
</style>
