<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Data Fix</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
        .form-container { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .form-container h3 { margin-top: 0; color: #495057; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a8b; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>Test Form Data Fix</h1>
    
    <div class="result success">
        <h3>✅ Issue Fixed!</h3>
        <p>The "No form data received" error has been resolved. The form submission handler now:</p>
        <ul>
            <li>Allows forms with only hidden fields</li>
            <li>Processes tracking and analytics forms</li>
            <li>Validates essential data (page_id/share_token) instead of user fields</li>
            <li>Provides better error messages for actual issues</li>
        </ul>
    </div>

    <h2>Test Forms</h2>

    <div class="form-container">
        <h3>1. Normal Contact Form (Should Work)</h3>
        <form id="normalForm">
            <input type="hidden" name="_page_id" value="1">
            <input type="hidden" name="_form_name" value="contact_form">
            
            <div class="form-group">
                <label for="name1">Name:</label>
                <input type="text" id="name1" name="name" value="John Doe">
            </div>
            
            <div class="form-group">
                <label for="email1">Email:</label>
                <input type="email" id="email1" name="email" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="message1">Message:</label>
                <textarea id="message1" name="message" rows="3">This is a test message.</textarea>
            </div>
            
            <button type="submit">Submit Normal Form</button>
        </form>
        <div id="result1" class="result" style="display: none;"></div>
    </div>

    <div class="form-container">
        <h3>2. Hidden Fields Only Form (Previously Failed, Now Works)</h3>
        <form id="hiddenForm">
            <input type="hidden" name="_page_id" value="1">
            <input type="hidden" name="_form_name" value="tracking_form">
            <input type="hidden" name="tracking_id" value="12345">
            <input type="hidden" name="source" value="test_page">
            
            <p>This form only has hidden fields (used for tracking/analytics).</p>
            <button type="submit">Submit Hidden Form</button>
        </form>
        <div id="result2" class="result" style="display: none;"></div>
    </div>

    <div class="form-container">
        <h3>3. Minimal Form (Edge Case)</h3>
        <form id="minimalForm">
            <input type="hidden" name="_page_id" value="1">
            <input type="hidden" name="_form_name" value="minimal_form">
            
            <div class="form-group">
                <label for="action">Action:</label>
                <input type="text" id="action" name="action" value="test_action">
            </div>
            
            <button type="submit">Submit Minimal Form</button>
        </form>
        <div id="result3" class="result" style="display: none;"></div>
    </div>

    <div class="form-container">
        <h3>4. Newsletter Signup (Single Field)</h3>
        <form id="newsletterForm">
            <input type="hidden" name="_page_id" value="1">
            <input type="hidden" name="_form_name" value="newsletter_form">
            <input type="hidden" name="_redirect_url" value="thank-you.html">
            
            <div class="form-group">
                <label for="newsletter_email">Email Address:</label>
                <input type="email" id="newsletter_email" name="email" value="<EMAIL>">
            </div>
            
            <button type="submit">Subscribe to Newsletter</button>
        </form>
        <div id="result4" class="result" style="display: none;"></div>
    </div>

    <h2>Test Results Summary</h2>
    <div id="summary" class="result" style="display: none;"></div>

    <script>
        let testResults = [];
        
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            
            forms.forEach(function(form, index) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(form);
                    const resultDiv = document.getElementById('result' + (index + 1));
                    const formName = formData.get('_form_name') || 'unknown';
                    
                    // Show testing message
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'result warning';
                    resultDiv.innerHTML = '<strong>Testing...</strong> Submitting ' + formName;
                    
                    fetch('enhanced_submit_form.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            resultDiv.className = 'result success';
                            resultDiv.innerHTML = '<strong>✅ Success!</strong> ' + data.message;
                            if (data.redirect_url) {
                                resultDiv.innerHTML += '<br><strong>Redirect:</strong> ' + data.redirect_url + ' (disabled in test)';
                            }
                            testResults.push({form: formName, status: 'success', message: data.message});
                        } else {
                            resultDiv.className = 'result error';
                            resultDiv.innerHTML = '<strong>❌ Error:</strong> ' + data.message;
                            testResults.push({form: formName, status: 'error', message: data.message});
                        }
                        updateSummary();
                    })
                    .catch(error => {
                        console.error('Test error:', error);
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '<strong>❌ Network Error:</strong> ' + error.message;
                        testResults.push({form: formName, status: 'error', message: error.message});
                        updateSummary();
                    });
                });
            });
        });
        
        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            summaryDiv.style.display = 'block';
            
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            if (errorCount === 0) {
                summaryDiv.className = 'result success';
                summaryDiv.innerHTML = '<h3>🎉 All Tests Passed!</h3><p>All ' + successCount + ' forms submitted successfully. The "No form data received" error has been fixed!</p>';
            } else {
                summaryDiv.className = 'result warning';
                summaryDiv.innerHTML = '<h3>⚠️ Mixed Results</h3><p>Success: ' + successCount + ', Errors: ' + errorCount + '</p>';
                
                const errorForms = testResults.filter(r => r.status === 'error');
                if (errorForms.length > 0) {
                    summaryDiv.innerHTML += '<p><strong>Forms with errors:</strong></p><ul>';
                    errorForms.forEach(form => {
                        summaryDiv.innerHTML += '<li>' + form.form + ': ' + form.message + '</li>';
                    });
                    summaryDiv.innerHTML += '</ul>';
                }
            }
        }
    </script>
</body>
</html>
