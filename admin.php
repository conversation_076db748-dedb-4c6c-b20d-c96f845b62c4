<?php
/**
 * Protected Admin Interface
 * Requires authentication to access the main application
 */

require_once 'includes/auth_manager.php';

$auth = new AuthManager();

// Require authentication
$auth->requireAuth('login.php');

// Get current user for display
$currentUser = $auth->getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#007cba">
    <title>Webpage Manager - Admin Dashboard</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/mobile-enhancements.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .protected-notice {
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .protected-notice i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="header-main">
                    <h1><i class="fas fa-shield-alt"></i> Webpage Manager - Admin</h1>
                    <p>Protected admin interface for managing webpages and users</p>
                </div>
                <div class="header-auth">
                    <div class="auth-user-info">
                        <div class="user-avatar"><?php echo strtoupper(substr($currentUser['full_name'], 0, 1)); ?></div>
                        <div class="user-details">
                            <div class="user-name"><?php echo htmlspecialchars($currentUser['full_name']); ?></div>
                            <div class="user-role"><?php echo htmlspecialchars($currentUser['role']); ?></div>
                        </div>
                    </div>
                    <div class="auth-actions">
                        <a href="logout.php" class="auth-btn">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <div class="protected-notice">
            <i class="fas fa-lock"></i>
            You are accessing the protected admin interface. All actions are logged and monitored.
        </div>

        <nav class="nav-tabs" id="nav-tabs">
            <button class="tab-btn active" data-tab="import">
                <i class="fas fa-upload"></i> Import Pages
            </button>
            <button class="tab-btn" data-tab="manage">
                <i class="fas fa-list"></i> Manage Pages
            </button>
            <button class="tab-btn" data-tab="forms">
                <i class="fas fa-wpforms"></i> Forms
            </button>
            <button class="tab-btn" data-tab="database">
                <i class="fas fa-database"></i> Database
            </button>
            <button class="tab-btn" data-tab="sharing">
                <i class="fas fa-share-alt"></i> Sharing
            </button>
            <?php if ($currentUser['role'] === 'admin'): ?>
            <button class="tab-btn" data-tab="users">
                <i class="fas fa-users"></i> Users
            </button>
            <?php endif; ?>
        </nav>

        <!-- Include the main application content -->
        <?php 
        // Read the main index.html content and extract the tab content sections
        $indexContent = file_get_contents('index.html');
        
        // Extract content between the nav-tabs closing and the modals
        $startPos = strpos($indexContent, '</nav>');
        $endPos = strpos($indexContent, '<!-- Modal for Page Details -->');
        
        if ($startPos !== false && $endPos !== false) {
            $tabContent = substr($indexContent, $startPos + 6, $endPos - $startPos - 6);
            echo $tabContent;
        }
        ?>
    </div>

    <!-- Include all modals from index.html -->
    <?php 
    // Extract modal content
    $modalStartPos = strpos($indexContent, '<!-- Modal for Page Details -->');
    $modalEndPos = strpos($indexContent, '<script src="assets/js/app.js"></script>');
    
    if ($modalStartPos !== false && $modalEndPos !== false) {
        $modalContent = substr($indexContent, $modalStartPos, $modalEndPos - $modalStartPos);
        echo $modalContent;
    }
    ?>

    <script>
        // Set authentication state for JavaScript
        window.authUser = <?php echo json_encode($currentUser); ?>;
        window.isAuthenticated = true;
    </script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/mobile-enhancements.js"></script>
    <script>
        // Initialize the app with authentication context
        document.addEventListener('DOMContentLoaded', function() {
            // Override the checkAuthentication method since we're already authenticated
            if (window.app) {
                app.currentUser = window.authUser;
                app.updateAuthUI = function() {
                    // Auth UI is already rendered server-side
                };
                
                // Show admin elements if user is admin
                if (window.authUser.role === 'admin') {
                    document.querySelectorAll('.admin-only').forEach(el => {
                        el.classList.add('show');
                    });
                }
            }
        });
    </script>
</body>
</html>
