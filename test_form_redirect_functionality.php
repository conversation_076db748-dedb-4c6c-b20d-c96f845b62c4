<?php
/**
 * Test Form Redirect Functionality
 * Comprehensive testing for form submission redirects
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Test Form Redirect Functionality</h1>";

echo "<h2>1. Redirect Implementation Overview</h2>";

echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
echo "<h3>✅ How Redirects Work</h3>";
echo "<p>The form redirect system now supports multiple ways to specify where users should be redirected after form submission:</p>";

echo "<h4>Redirect Priority Order:</h4>";
echo "<ol>";
echo "<li><strong>Form-specific redirect:</strong> <code>_redirect_url</code> hidden field</li>";
echo "<li><strong>Share redirect:</strong> Redirect URL set when creating/editing a share</li>";
echo "<li><strong>Original form action:</strong> The original action attribute of the form</li>";
echo "</ol>";

echo "<h4>Implementation Details:</h4>";
echo "<ul>";
echo "<li><strong>Backend Processing:</strong> Form handlers extract and validate redirect URLs</li>";
echo "<li><strong>Response Handling:</strong> Redirect URL is included in JSON response</li>";
echo "<li><strong>Frontend Execution:</strong> JavaScript handles the redirect after success message</li>";
echo "<li><strong>User Experience:</strong> 1-second delay to show success message before redirect</li>";
echo "</ul>";
echo "</div>";

echo "<h2>2. Test Scenarios</h2>";

echo "<div style='background: #e7f3ff; color: #004085; padding: 20px; border-radius: 8px;'>";
echo "<h3>🧪 Available Test Cases</h3>";

echo "<h4>Scenario 1: Form with data-redirect attribute</h4>";
echo "<form id='testForm1' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<input type='hidden' name='_page_id' value='1'>";
echo "<input type='hidden' name='_form_name' value='test_redirect_form'>";
echo "<input type='hidden' name='_redirect_url' value='https://www.google.com'>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Name: <input type='text' name='name' value='Test User' style='margin-left: 10px; padding: 5px;'></label>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Email: <input type='email' name='email' value='<EMAIL>' style='margin-left: 10px; padding: 5px;'></label>";
echo "</div>";
echo "<button type='submit' style='background: #007cba; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Submit (Redirect to Google)</button>";
echo "</form>";

echo "<h4>Scenario 2: Form with original action URL</h4>";
echo "<form id='testForm2' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<input type='hidden' name='_page_id' value='1'>";
echo "<input type='hidden' name='_form_name' value='test_action_form'>";
echo "<input type='hidden' name='_original_action' value='https://httpbin.org/post'>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Message: <textarea name='message' style='margin-left: 10px; padding: 5px;'>Test message</textarea></label>";
echo "</div>";
echo "<button type='submit' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Submit (Redirect to HTTPBin)</button>";
echo "</form>";

echo "<h4>Scenario 3: Form without redirect (no redirect)</h4>";
echo "<form id='testForm3' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<input type='hidden' name='_page_id' value='1'>";
echo "<input type='hidden' name='_form_name' value='test_no_redirect_form'>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Feedback: <input type='text' name='feedback' value='No redirect test' style='margin-left: 10px; padding: 5px;'></label>";
echo "</div>";
echo "<button type='submit' style='background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Submit (No Redirect)</button>";
echo "</form>";
echo "</div>";

echo "<h2>3. Technical Implementation</h2>";

echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px;'>";
echo "<h3>🔧 Backend Processing</h3>";

echo "<h4>Enhanced Form Handler (enhanced_submit_form.php):</h4>";
echo "<pre style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('// Extract redirect URL before removing system fields
$redirectUrl = $_POST["_redirect_url"] ?? null;
$originalAction = $_POST["_original_action"] ?? null;

// Determine final redirect URL
$finalRedirectUrl = null;

// Priority order: form-specific redirect > share redirect > original form action
if (!empty($redirectUrl)) {
    $finalRedirectUrl = $redirectUrl;
} elseif (!empty($shareInfo["redirect_url"])) {
    $finalRedirectUrl = $shareInfo["redirect_url"];
} elseif (!empty($originalAction) && filter_var($originalAction, FILTER_VALIDATE_URL)) {
    $finalRedirectUrl = $originalAction;
}

echo json_encode([
    "success" => true,
    "message" => "Form submitted successfully",
    "submission_id" => $submissionId,
    "redirect_url" => $finalRedirectUrl
]);');
echo "</pre>";

echo "<h4>Frontend JavaScript Handling:</h4>";
echo "<pre style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('.then(data => {
    if (data.success) {
        // Show success message
        alert(data.message || "Form submitted successfully!");
        
        // Reset form
        form.reset();
        
        // Handle redirect if specified
        if (data.redirect_url) {
            // Small delay to ensure user sees the success message
            setTimeout(function() {
                window.location.href = data.redirect_url;
            }, 1000);
        }
    } else {
        alert("Error: " + data.message);
    }
})');
echo "</pre>";
echo "</div>";

echo "<h2>4. Share-Level Redirects</h2>";

echo "<div style='background: #d1ecf1; color: #0c5460; padding: 20px; border-radius: 8px;'>";
echo "<h3>🔗 Share Configuration</h3>";
echo "<p>When creating or editing shares, you can specify a redirect URL that applies to all forms on that shared page:</p>";

echo "<h4>In the Share Creation Modal:</h4>";
echo "<ul>";
echo "<li><strong>Redirect URL Field:</strong> Enter the URL where users should be redirected</li>";
echo "<li><strong>Validation:</strong> URL format is validated before saving</li>";
echo "<li><strong>Priority:</strong> Form-specific redirects override share redirects</li>";
echo "</ul>";

echo "<h4>Database Storage:</h4>";
echo "<ul>";
echo "<li><strong>Table:</strong> <code>page_shares</code></li>";
echo "<li><strong>Column:</strong> <code>redirect_url</code></li>";
echo "<li><strong>Validation:</strong> URLs are validated with <code>filter_var()</code></li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. Form-Level Redirects</h2>";

echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
echo "<h3>📝 Form Attributes</h3>";
echo "<p>Individual forms can specify their own redirect behavior using HTML attributes:</p>";

echo "<h4>Supported Attributes:</h4>";
echo "<ul>";
echo "<li><strong>data-redirect:</strong> <code>&lt;form data-redirect=\"https://example.com/thanks\"&gt;</code></li>";
echo "<li><strong>action:</strong> <code>&lt;form action=\"https://example.com/process\"&gt;</code></li>";
echo "</ul>";

echo "<h4>Hidden Field Override:</h4>";
echo "<ul>";
echo "<li><strong>_redirect_url:</strong> Can be added dynamically via JavaScript</li>";
echo "<li><strong>Highest Priority:</strong> Overrides both share and form attribute redirects</li>";
echo "</ul>";
echo "</div>";

echo "<h2>6. Test Results</h2>";

echo "<div id='testResults' style='margin: 20px 0;'></div>";

echo "<h2>7. Expected Behavior</h2>";

echo "<div style='background: #e2e3e5; color: #383d41; padding: 20px; border-radius: 8px;'>";
echo "<h3>✅ What Should Happen</h3>";

echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f2f2f2;'>";
echo "<th>Test Form</th><th>Redirect Source</th><th>Expected Result</th>";
echo "</tr>";
echo "<tr>";
echo "<td><strong>Form 1</strong></td>";
echo "<td>_redirect_url hidden field</td>";
echo "<td>✅ Redirects to Google after 1 second</td>";
echo "</tr>";
echo "<tr>";
echo "<td><strong>Form 2</strong></td>";
echo "<td>_original_action hidden field</td>";
echo "<td>✅ Redirects to HTTPBin after 1 second</td>";
echo "</tr>";
echo "<tr>";
echo "<td><strong>Form 3</strong></td>";
echo "<td>No redirect specified</td>";
echo "<td>✅ Shows success message, no redirect</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

echo "<h2>Quick Action Links</h2>";
echo "<div style='display: flex; gap: 15px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='index.html' target='_blank' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Main Application</a>";
echo "<a href='enhanced_data_collection.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Data Collection Manager</a>";
echo "<a href='test_data_collection_system.php' target='_blank' style='background: #6f42c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Test Data Collection</a>";
echo "</div>";

echo "<h2>Summary</h2>";
echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🎉 Form Redirect Functionality Ready!</h3>";
echo "<ul>";
echo "<li>✅ Multiple redirect sources supported (form, share, original action)</li>";
echo "<li>✅ Priority system ensures correct redirect behavior</li>";
echo "<li>✅ URL validation prevents invalid redirects</li>";
echo "<li>✅ User-friendly experience with success message before redirect</li>";
echo "<li>✅ Works with both shared and direct page views</li>";
echo "<li>✅ Graceful handling when no redirect is specified</li>";
echo "</ul>";
echo "<p><strong>Forms will now redirect users to specified pages after successful submission!</strong></p>";
echo "</div>";

?>

<script>
// Add event listeners to test forms
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[id^="testForm"]');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const resultsDiv = document.getElementById('testResults');
            const formId = form.id;
            
            // Show testing message
            resultsDiv.innerHTML += '<div style="background: #e7f3ff; color: #004085; padding: 10px; margin: 5px 0; border-radius: 5px;"><strong>' + formId + ':</strong> Testing form submission...</div>';
            
            fetch('enhanced_submit_form.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = '<div style="background: #d4edda; color: #155724; padding: 10px; margin: 5px 0; border-radius: 5px;">';
                    message += '<strong>' + formId + ' ✅ Success:</strong> ' + data.message;
                    
                    if (data.redirect_url) {
                        message += '<br><strong>Redirect URL:</strong> ' + data.redirect_url;
                        message += '<br><strong>Action:</strong> Will redirect in 1 second...';
                        
                        // Simulate the redirect (but don't actually redirect in test)
                        setTimeout(function() {
                            resultsDiv.innerHTML += '<div style="background: #fff3cd; color: #856404; padding: 10px; margin: 5px 0; border-radius: 5px;"><strong>' + formId + ' 🔄 Redirect:</strong> Would redirect to ' + data.redirect_url + ' (disabled in test)</div>';
                        }, 1000);
                    } else {
                        message += '<br><strong>Redirect:</strong> None specified (staying on page)';
                    }
                    
                    message += '</div>';
                    resultsDiv.innerHTML += message;
                } else {
                    resultsDiv.innerHTML += '<div style="background: #f8d7da; color: #721c24; padding: 10px; margin: 5px 0; border-radius: 5px;"><strong>' + formId + ' ❌ Error:</strong> ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Test error:', error);
                resultsDiv.innerHTML += '<div style="background: #f8d7da; color: #721c24; padding: 10px; margin: 5px 0; border-radius: 5px;"><strong>' + formId + ' ❌ Error:</strong> ' + error.message + '</div>';
            });
        });
    });
});
</script>

<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: 'Courier New', monospace; }
input, textarea { box-sizing: border-box; }
</style>
