<?php
/**
 * Complete System Reset
 * Reset and rebuild the entire system from scratch
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Complete System Reset</h1>";
echo "<p><strong>Warning:</strong> This will reset all form submission data!</p>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    echo "<h2>Step 1: Reset Form Submissions Table</h2>";
    
    // Drop and recreate form_submissions table with correct structure
    $db->exec("SET FOREIGN_KEY_CHECKS = 0");
    $db->exec("DROP TABLE IF EXISTS form_submissions");
    
    $sql = "CREATE TABLE form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_name VARCHAR(255) NULL,
        submission_data JSON NOT NULL,
        visitor_session VARCHAR(64) NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        referrer VARCHAR(500) NULL,
        country_code VARCHAR(2) NULL,
        city VARCHAR(100) NULL,
        browser_name VARCHAR(50) NULL,
        browser_version VARCHAR(20) NULL,
        os_name VARCHAR(50) NULL,
        device_type ENUM('desktop', 'mobile', 'tablet') DEFAULT 'desktop',
        submission_source ENUM('direct', 'shared', 'embedded') DEFAULT 'shared',
        status ENUM('pending', 'processed', 'archived', 'spam', 'reviewed') DEFAULT 'pending',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        tags JSON NULL,
        notes TEXT NULL,
        processed_at TIMESTAMP NULL,
        processed_by INT NULL,
        response_sent BOOLEAN DEFAULT FALSE,
        response_sent_at TIMESTAMP NULL,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at),
        INDEX idx_status (status),
        INDEX idx_priority (priority),
        INDEX idx_submission_source (submission_source),
        INDEX idx_device_type (device_type),
        INDEX idx_browser_name (browser_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ form_submissions table recreated with enhanced structure</p>";
    
    echo "<h2>Step 2: Ensure page_shares Table</h2>";
    
    // Check if page_shares exists and has correct structure
    try {
        $sql = "DESCRIBE page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
        
        if (!in_array('page_id', $columns)) {
            echo "<p>❌ page_shares missing page_id column, fixing...</p>";
            $db->exec("ALTER TABLE page_shares ADD COLUMN page_id INT NOT NULL AFTER id");
            echo "<p>✅ Added page_id column</p>";
        } else {
            echo "<p>✅ page_shares table structure correct</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ page_shares table issue, recreating...</p>";
        
        $db->exec("DROP TABLE IF EXISTS page_shares");
        
        $sql = "CREATE TABLE page_shares (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            share_token VARCHAR(64) NOT NULL UNIQUE,
            short_code VARCHAR(10) NOT NULL UNIQUE,
            title VARCHAR(255),
            description TEXT,
            password_hash VARCHAR(255),
            expires_at TIMESTAMP NULL,
            max_views INT NULL,
            view_count INT DEFAULT 0,
            allow_download BOOLEAN DEFAULT FALSE,
            show_forms BOOLEAN DEFAULT TRUE,
            show_metadata BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_page_id (page_id),
            INDEX idx_share_token (share_token),
            INDEX idx_short_code (short_code),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ page_shares table recreated</p>";
    }
    
    echo "<h2>Step 3: Create ZIP Extractions Table</h2>";
    
    $db->exec("DROP TABLE IF EXISTS zip_extractions");
    
    $sql = "CREATE TABLE zip_extractions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        original_filename VARCHAR(255) NOT NULL,
        extracted_path VARCHAR(500) NOT NULL,
        total_files INT DEFAULT 0,
        html_files_found INT DEFAULT 0,
        assets_found INT DEFAULT 0,
        index_page_id INT NULL,
        index_page_path VARCHAR(500),
        extraction_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
        error_message TEXT,
        file_structure JSON,
        extraction_time DECIMAL(10,4),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        INDEX idx_original_filename (original_filename),
        INDEX idx_extraction_status (extraction_status),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ zip_extractions table created</p>";
    
    $db->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    echo "<h2>Step 4: Test Basic Form Submission</h2>";
    
    $sql = "INSERT INTO form_submissions (page_id, submission_data, ip_address) 
            VALUES (?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([1, '{"test": "basic"}', '127.0.0.1']);
    
    $basicId = $db->lastInsertId();
    echo "<p>✅ Basic form submission works (ID: $basicId)</p>";
    
    // Clean up
    $db->exec("DELETE FROM form_submissions WHERE id = $basicId");
    
    echo "<h2>Step 5: Test Enhanced Form Submission</h2>";
    
    $sql = "INSERT INTO form_submissions (
                page_id, form_name, submission_data, visitor_session,
                ip_address, user_agent, browser_name, browser_version,
                os_name, device_type, submission_source, status, priority
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        1,
        'test_enhanced_form',
        '{"name": "Test User", "email": "<EMAIL>"}',
        'test_session_123',
        '127.0.0.1',
        'Test User Agent',
        'Chrome',
        '91.0',
        'Windows',
        'desktop',
        'shared',
        'pending',
        'normal'
    ]);
    
    $enhancedId = $db->lastInsertId();
    echo "<p>✅ Enhanced form submission works (ID: $enhancedId)</p>";
    
    // Show the inserted data
    $sql = "SELECT * FROM form_submissions WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$enhancedId]);
    $inserted = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo "<h4>Enhanced Data Stored:</h4>";
    foreach ($inserted as $key => $value) {
        if ($value !== null && $value !== '') {
            echo "<p><strong>$key:</strong> $value</p>";
        }
    }
    echo "</div>";
    
    // Clean up
    $db->exec("DELETE FROM form_submissions WHERE id = $enhancedId");
    
    echo "<h2>Step 6: Test Share System</h2>";
    
    // Get or create a test page
    $sql = "SELECT id FROM pages LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$page) {
        echo "<p>⚠️ No pages found, creating test page...</p>";
        
        // Create uploads directory
        if (!is_dir('uploads/pages')) {
            mkdir('uploads/pages', 0755, true);
        }
        
        // Create test HTML file
        $testHtml = '<!DOCTYPE html>
<html>
<head><title>Test Page</title></head>
<body>
    <h1>Test Contact Form</h1>
    <form method="POST">
        <input type="text" name="name" placeholder="Name" required>
        <input type="email" name="email" placeholder="Email" required>
        <textarea name="message" placeholder="Message" required></textarea>
        <button type="submit">Submit</button>
    </form>
</body>
</html>';
        
        $testFile = 'uploads/pages/test_page_' . time() . '.html';
        file_put_contents($testFile, $testHtml);
        
        $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            basename($testFile),
            'test_page.html',
            'Test Contact Form',
            $testFile,
            strlen($testHtml),
            hash('sha256', $testHtml)
        ]);
        
        $pageId = $db->lastInsertId();
        echo "<p>✅ Test page created with ID: $pageId</p>";
    } else {
        $pageId = $page['id'];
        echo "<p>✅ Using existing page ID: $pageId</p>";
    }
    
    // Create test share
    $shareToken = bin2hex(random_bytes(16));
    $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
    
    $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
            VALUES (?, ?, ?, ?, 1, 1)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId, $shareToken, $shortCode, 'System Reset Test Share']);
    
    $shareId = $db->lastInsertId();
    echo "<p>✅ Test share created with ID: $shareId</p>";
    
    // Test the problematic query
    $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
            FROM page_shares ps 
            JOIN pages p ON ps.page_id = p.id 
            WHERE ps.share_token = ? AND ps.is_active = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute([$shareToken]);
    $shareInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($shareInfo) {
        echo "<p>✅ Share query works correctly</p>";
        echo "<p><strong>Share:</strong> {$shareInfo['title']} for page: {$shareInfo['page_title']}</p>";
        
        // Generate share URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = rtrim(dirname($_SERVER['SCRIPT_NAME'] ?? ''), '/');
        $baseUrl = $protocol . '://' . $host . $path;
        $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
        
        echo "<p><strong>Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
    } else {
        echo "<p>❌ Share query failed</p>";
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 System Reset Complete!</h2>";
    echo "<p>All database tables have been reset and recreated with the correct structure.</p>";
    echo "<p>Both basic and enhanced form submission systems are now working.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Reset Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Test Form Submissions:</strong> <a href='test_form_submission.php'>Run Form Test</a></li>";
echo "<li><strong>Test Enhanced System:</strong> <a href='test_enhanced_system.php'>Run Enhanced Test</a></li>";
echo "<li><strong>Use Main App:</strong> <a href='index.html'>Main Application</a></li>";
echo "<li><strong>Test Share URL:</strong> Use the generated share URL above</li>";
echo "</ol>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3, h4 { color: #333; }
</style>
