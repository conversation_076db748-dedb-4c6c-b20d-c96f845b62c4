<?php
/**
 * Debug Form Collection Issues
 * Find out why form data from shared pages isn't being collected
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Debug Form Collection Issues</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>1. Check form_submissions Table Structure</h2>";
    
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $columnNames = [];
    foreach ($columns as $col) {
        $columnNames[] = $col['Field'];
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>2. Check submit_form.php Column Usage</h2>";
    
    // Check what columns submit_form.php is trying to use
    $submitFormContent = file_get_contents('submit_form.php');
    
    // Find the INSERT statement
    if (preg_match('/INSERT INTO form_submissions \((.*?)\)/s', $submitFormContent, $matches)) {
        $insertColumns = array_map('trim', explode(',', $matches[1]));
        echo "<p><strong>Columns submit_form.php tries to insert:</strong></p>";
        echo "<ul>";
        foreach ($insertColumns as $col) {
            $col = trim($col);
            $exists = in_array($col, $columnNames);
            $status = $exists ? '✅' : '❌';
            echo "<li>$status $col</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>3. Test Form Submission Process</h2>";
    
    // Create a test share
    $sql = "SELECT id FROM pages LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$page) {
        echo "<p>❌ No pages found. Creating test page...</p>";
        
        if (!is_dir('uploads/pages')) {
            mkdir('uploads/pages', 0755, true);
        }
        
        $testHtml = '<!DOCTYPE html>
<html>
<head><title>Test Form Page</title></head>
<body>
    <h1>Test Contact Form</h1>
    <form method="POST" name="contact_form">
        <div>
            <label>Name:</label>
            <input type="text" name="name" required>
        </div>
        <div>
            <label>Email:</label>
            <input type="email" name="email" required>
        </div>
        <div>
            <label>Message:</label>
            <textarea name="message" required></textarea>
        </div>
        <button type="submit">Submit</button>
    </form>
</body>
</html>';
        
        $testFile = 'uploads/pages/test_form_page_' . time() . '.html';
        file_put_contents($testFile, $testHtml);
        
        $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            basename($testFile),
            'test_form_page.html',
            'Test Form Page',
            $testFile,
            strlen($testHtml),
            hash('sha256', $testHtml)
        ]);
        
        $pageId = $db->lastInsertId();
        echo "<p>✅ Test page created with ID: $pageId</p>";
    } else {
        $pageId = $page['id'];
        echo "<p>✅ Using existing page ID: $pageId</p>";
    }
    
    // Create test share
    $shareToken = bin2hex(random_bytes(16));
    $shortCode = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
    
    $sql = "INSERT INTO page_shares (page_id, share_token, short_code, title, show_forms, is_active) 
            VALUES (?, ?, ?, ?, 1, 1)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId, $shareToken, $shortCode, 'Debug Test Share']);
    
    $shareId = $db->lastInsertId();
    echo "<p>✅ Test share created with ID: $shareId</p>";
    
    // Generate share URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = rtrim(dirname($_SERVER['SCRIPT_NAME'] ?? ''), '/');
    $baseUrl = $protocol . '://' . $host . $path;
    $shareUrl = $baseUrl . '/view.php?token=' . $shareToken;
    
    echo "<p><strong>Test Share URL:</strong> <a href='$shareUrl' target='_blank'>$shareUrl</a></p>";
    
    echo "<h2>4. Test Form Submission Handler</h2>";
    
    // Simulate form submission
    $_POST = [
        '_share_token' => $shareToken,
        '_page_id' => $pageId,
        '_form_name' => 'contact_form',
        'name' => 'Debug Test User',
        'email' => '<EMAIL>',
        'message' => 'This is a debug test submission'
    ];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    echo "<p>Simulating form submission...</p>";
    
    try {
        // Capture output from submit_form.php
        ob_start();
        include 'submit_form.php';
        $output = ob_get_clean();
        
        echo "<p>✅ submit_form.php executed without errors</p>";
        echo "<p><strong>Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        
        // Check if submission was actually stored
        $sql = "SELECT * FROM form_submissions ORDER BY id DESC LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $lastSubmission = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($lastSubmission) {
            echo "<p>✅ Form submission found in database</p>";
            echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
            echo "<h4>Latest Submission Data:</h4>";
            foreach ($lastSubmission as $key => $value) {
                if ($value !== null && $value !== '') {
                    echo "<p><strong>$key:</strong> $value</p>";
                }
            }
            echo "</div>";
        } else {
            echo "<p>❌ No form submission found in database</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error in submit_form.php: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Check Database Tables</h2>";
    
    // Check if required tables exist
    $requiredTables = ['pages', 'page_shares', 'form_submissions', 'share_access_log'];
    
    foreach ($requiredTables as $table) {
        try {
            $sql = "SELECT COUNT(*) as count FROM $table";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>✅ Table '$table' exists with {$result['count']} records</p>";
        } catch (Exception $e) {
            echo "<p>❌ Table '$table' error: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>6. Check Activity Log Table</h2>";
    
    try {
        $sql = "SELECT COUNT(*) as count FROM activity_log";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ activity_log table exists with {$result['count']} records</p>";
    } catch (Exception $e) {
        echo "<p>❌ activity_log table missing or error: " . $e->getMessage() . "</p>";
        echo "<p>This might cause submit_form.php to fail</p>";
    }
    
    echo "<h2>7. Check View.php Form Enhancement</h2>";
    
    // Test if view.php properly enhances forms
    echo "<p>Testing view.php form enhancement...</p>";
    
    try {
        // Simulate viewing a shared page
        $_GET = ['token' => $shareToken];
        
        ob_start();
        include 'view.php';
        $viewOutput = ob_get_clean();
        
        // Check if the output contains form enhancements
        if (strpos($viewOutput, 'submit_form.php') !== false) {
            echo "<p>✅ view.php properly sets form action to submit_form.php</p>";
        } else {
            echo "<p>❌ view.php does not enhance forms properly</p>";
        }
        
        if (strpos($viewOutput, '_share_token') !== false) {
            echo "<p>✅ view.php adds share token to forms</p>";
        } else {
            echo "<p>❌ view.php does not add share token to forms</p>";
        }
        
        if (strpos($viewOutput, 'fetch(\'submit_form.php\'') !== false) {
            echo "<p>✅ view.php adds AJAX form submission</p>";
        } else {
            echo "<p>❌ view.php does not add AJAX form submission</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error testing view.php: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>8. Summary of Issues Found</h2>";
    
    $issues = [];
    
    // Check for missing columns in form_submissions
    $requiredColumns = ['page_id', 'form_id', 'share_id', 'form_data', 'ip_address', 'user_agent', 'referrer', 'submitted_at'];
    $missingColumns = array_diff($requiredColumns, $columnNames);
    
    if (!empty($missingColumns)) {
        $issues[] = "Missing columns in form_submissions: " . implode(', ', $missingColumns);
    }
    
    // Check for activity_log table
    try {
        $sql = "SELECT 1 FROM activity_log LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
    } catch (Exception $e) {
        $issues[] = "activity_log table missing - this will cause submit_form.php to fail";
    }
    
    if (empty($issues)) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ No Critical Issues Found</h4>";
        echo "<p>The form collection system appears to be properly configured.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Issues Found:</h4>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Critical error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li><a href='fix_form_collection.php'>Fix Form Collection Issues</a> (if this file exists)</li>";
echo "<li><a href='test_form_submission.php'>Test Form Submission System</a></li>";
echo "<li>Try submitting a form on the test share URL above</li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
