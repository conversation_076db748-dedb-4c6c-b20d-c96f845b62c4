# Webpage Manager Database Documentation v2.0

This document provides comprehensive information about the enhanced database structure for Webpage Manager v2.0.

## Overview

The Webpage Manager now uses a robust database structure with 14 tables designed to handle all aspects of webpage import, form analysis, editing, and management. The database supports advanced features like project organization, version control, activity logging, and form submission storage.

## Database Schema

### Core Tables

#### 1. `app_settings`
Stores application configuration and settings.

**Columns:**
- `id` - Primary key
- `setting_key` - Unique setting identifier
- `setting_value` - Setting value (TEXT)
- `setting_type` - Data type (string, integer, boolean, json)
- `description` - Human-readable description
- `is_system` - Whether it's a system setting
- `created_at`, `updated_at` - Timestamps

**Purpose:** Centralized configuration management

#### 2. `users`
User management for multi-user support.

**Columns:**
- `id` - Primary key
- `username` - Unique username
- `email` - Unique email address
- `password_hash` - Encrypted password
- `full_name` - User's full name
- `role` - User role (admin, editor, viewer)
- `is_active` - Account status
- `last_login` - Last login timestamp
- `created_at`, `updated_at` - Timestamps

**Purpose:** User authentication and authorization

#### 3. `projects`
Project organization for grouping pages.

**Columns:**
- `id` - Primary key
- `name` - Project name
- `description` - Project description
- `slug` - URL-friendly identifier
- `color` - Project color code
- `is_active` - Project status
- `created_by` - User who created the project
- `created_at`, `updated_at` - Timestamps

**Purpose:** Organize pages into logical groups

#### 4. `pages` (Enhanced)
Stores imported HTML page information.

**New Columns Added:**
- `project_id` - Link to project
- `description` - Page description
- `status` - Page status (active, archived, deleted)
- `version` - Current version number
- `meta_keywords` - SEO keywords
- `meta_description` - SEO description
- `language` - Page language
- `charset` - Character encoding

**Purpose:** Complete page metadata and organization

#### 5. `forms` (Enhanced)
Stores detected form structures.

**New Columns Added:**
- `form_target` - Form target attribute
- `form_autocomplete` - Autocomplete setting
- `form_novalidate` - Validation bypass flag
- `form_description` - Form description
- `is_active` - Form status
- `validation_rules` - JSON validation rules
- `custom_attributes` - JSON custom attributes
- `updated_at` - Update timestamp

**Purpose:** Complete form metadata and configuration

#### 6. `form_fields` (Enhanced)
Stores detailed field information.

**New Columns Added:**
- `field_label` - Field label text
- `field_validation_rules` - JSON validation rules
- `field_custom_attributes` - JSON custom attributes
- `field_order` - Field display order
- `is_active` - Field status
- `updated_at` - Update timestamp

**Purpose:** Complete field metadata and validation

### Supporting Tables

#### 7. `associated_files`
Tracks uploaded assets (CSS, JS, images, fonts).

**Purpose:** Manage file dependencies and references

#### 8. `generated_tables`
Stores generated database table structures.

**New Columns Added:**
- `is_created` - Whether table was actually created
- `updated_at` - Update timestamp

**Purpose:** Track database table generation from forms

#### 9. `analysis_log` (Enhanced)
Logs analysis results and system events.

**New Columns Added:**
- `form_id` - Link to specific form
- `execution_time` - Processing time
- `memory_usage` - Memory consumption

**Purpose:** Detailed system monitoring and debugging

### New Advanced Tables

#### 10. `form_templates`
Reusable form structures and templates.

**Columns:**
- `id` - Primary key
- `name` - Template name
- `description` - Template description
- `category` - Template category
- `template_data` - JSON template structure
- `is_system` - System template flag
- `is_active` - Template status
- `usage_count` - Usage statistics
- `created_at`, `updated_at` - Timestamps

**Purpose:** Quick form creation from predefined templates

#### 11. `form_submissions`
Stores actual form submission data.

**Columns:**
- `id` - Primary key
- `form_id` - Link to form
- `submission_data` - JSON submission data
- `ip_address` - Submitter IP
- `user_agent` - Browser information
- `referrer` - Referring page
- `status` - Submission status
- `processed_at` - Processing timestamp
- `created_at` - Submission timestamp

**Purpose:** Store and manage form submissions

#### 12. `page_versions`
Version control for page modifications.

**Columns:**
- `id` - Primary key
- `page_id` - Link to page
- `version_number` - Version number
- `file_path` - Version file path
- `file_hash` - File integrity hash
- `changes_summary` - Change description
- `created_at` - Version timestamp

**Purpose:** Track page modifications and enable rollback

#### 13. `activity_log`
Comprehensive activity tracking.

**Columns:**
- `id` - Primary key
- `user_id` - User who performed action
- `action` - Action performed
- `entity_type` - Type of entity affected
- `entity_id` - ID of affected entity
- `old_values` - JSON previous values
- `new_values` - JSON new values
- `ip_address` - User IP address
- `user_agent` - Browser information
- `created_at` - Action timestamp

**Purpose:** Audit trail and activity monitoring

## Database Views

### `form_analysis_view`
Provides comprehensive form analysis data.

**Columns:**
- Page and form information
- Field counts and types
- Creation timestamps

### `table_generation_status`
Shows database table generation status.

**Columns:**
- Form information
- Generated table details
- Creation status

## Key Features

### 1. **Data Integrity**
- Foreign key constraints ensure referential integrity
- Proper indexing for performance
- UTF8MB4 encoding for full Unicode support

### 2. **Scalability**
- Optimized table structure
- Efficient indexing strategy
- JSON columns for flexible data storage

### 3. **Security**
- User role-based access control
- Activity logging for audit trails
- Secure password hashing support

### 4. **Flexibility**
- JSON columns for extensible data
- Template system for reusability
- Project organization for scalability

### 5. **Maintenance**
- Automated cleanup procedures
- Database optimization tools
- Backup and restore capabilities

## Configuration Settings

The `app_settings` table includes these key settings:

- `max_file_size` - Maximum upload size
- `allowed_file_types` - Permitted file extensions
- `auto_analyze_forms` - Automatic form analysis
- `keep_file_backups` - Backup file retention
- `enable_form_submissions` - Form submission storage
- `cleanup_old_logs` - Automatic log cleanup
- `log_retention_days` - Log retention period

## Usage Examples

### Adding a New Project
```sql
INSERT INTO projects (name, description, slug, created_by) 
VALUES ('E-commerce Site', 'Online store forms', 'ecommerce', 1);
```

### Creating a Form Template
```sql
INSERT INTO form_templates (name, category, template_data) 
VALUES ('Contact Form', 'contact', '{"fields": [...]}');
```

### Tracking Activity
```sql
INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values) 
VALUES (1, 'form_created', 'form', 123, '{"name": "Contact Form"}');
```

## Maintenance Procedures

### Regular Cleanup
```sql
-- Clean old logs (automated via settings)
DELETE FROM analysis_log WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
DELETE FROM activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### Database Optimization
```sql
-- Optimize all tables
OPTIMIZE TABLE pages, forms, form_fields, analysis_log, activity_log;
```

### Backup Creation
Use the built-in database manager API:
```
GET /includes/database_manager.php?action=backup
```

## Migration from v1.0

The enhanced database structure is backward compatible with v1.0 data. The setup script automatically:

1. Creates new tables
2. Adds new columns to existing tables
3. Migrates existing data
4. Sets up default configurations

## Performance Considerations

- **Indexing:** All foreign keys and frequently queried columns are indexed
- **JSON Storage:** Used for flexible, schema-less data
- **Partitioning:** Consider partitioning large log tables by date
- **Archiving:** Implement data archiving for old submissions and logs

## Security Notes

- All user inputs are properly sanitized
- Password hashing uses secure algorithms
- Activity logging tracks all modifications
- Role-based access control limits user actions
- File upload restrictions prevent malicious uploads

This enhanced database structure provides a solid foundation for the Webpage Manager's advanced features while maintaining performance and security.
