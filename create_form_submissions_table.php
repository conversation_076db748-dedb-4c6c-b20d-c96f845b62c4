<?php
/**
 * Create form_submissions table for storing form data
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // Create form_submissions table
    $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_data JSON NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at),
        FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
        FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE SET NULL,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (share_id) REFERENCES page_shares(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "Form submissions table created successfully.\n";

    // Check if share_access_log table exists, create if not
    $sql = "CREATE TABLE IF NOT EXISTS share_access_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        share_id INT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        access_type ENUM('view', 'form_submission', 'download') DEFAULT 'view',
        accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_share_id (share_id),
        INDEX idx_accessed_at (accessed_at),
        INDEX idx_access_type (access_type),
        FOREIGN KEY (share_id) REFERENCES page_shares(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "Share access log table created successfully.\n";

    // Check if page_shares table exists, create if not
    $sql = "CREATE TABLE IF NOT EXISTS page_shares (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        share_token VARCHAR(64) UNIQUE NOT NULL,
        short_code VARCHAR(10) UNIQUE NOT NULL,
        title VARCHAR(255),
        description TEXT,
        password_hash VARCHAR(255),
        expires_at TIMESTAMP NULL,
        max_views INT NULL,
        view_count INT DEFAULT 0,
        allow_download BOOLEAN DEFAULT FALSE,
        show_forms BOOLEAN DEFAULT TRUE,
        show_metadata BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_page_id (page_id),
        INDEX idx_share_token (share_token),
        INDEX idx_short_code (short_code),
        INDEX idx_created_at (created_at),
        INDEX idx_is_active (is_active),
        FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "Page shares table created successfully.\n";

    echo "Database migration completed successfully.\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
