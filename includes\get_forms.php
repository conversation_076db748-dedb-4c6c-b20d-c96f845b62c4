<?php
/**
 * Get Forms for a specific page
 * Returns forms and their fields for editing
 */

require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $pageId = $_GET['page_id'] ?? null;
    
    if (!$pageId) {
        throw new Exception('Page ID is required');
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Get forms for the page
    $sql = "SELECT * FROM forms WHERE page_id = ? AND is_active = 1 ORDER BY form_index ASC";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get fields for each form
    foreach ($forms as &$form) {
        $sql = "SELECT * FROM form_fields WHERE form_id = ? AND is_active = 1 ORDER BY field_order ASC, id ASC";
        $stmt = $db->prepare($sql);
        $stmt->execute([$form['id']]);
        $form['fields'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo json_encode([
        'success' => true,
        'forms' => $forms,
        'page_id' => $pageId,
        'total_forms' => count($forms),
        'total_fields' => array_sum(array_map(function($form) { return count($form['fields']); }, $forms))
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
