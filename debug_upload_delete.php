<?php
/**
 * Debug Upload and Delete Issues
 * Test upload and delete functionality to identify response issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Upload and Delete Issues</h1>";

echo "<h2>1. Test Upload Response</h2>";

// Test the enhanced upload endpoint directly
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_upload'])) {
    echo "<h3>Upload Test Results:</h3>";
    
    // Capture any output before JSON
    ob_start();
    
    // Simulate the upload request
    $_FILES['files'] = $_FILES['test_upload'];
    $_POST['upload_type'] = 'individual';
    $_POST['project_id'] = 1;
    
    try {
        // Include the enhanced upload handler
        require_once 'includes/enhanced_upload.php';
        
        // This should not output anything before the JSON
        $handler = new EnhancedUploadHandler();
        $result = $handler->handleUpload();
        
        // Check for any unexpected output
        $output = ob_get_clean();
        
        echo "<h4>Raw Response Analysis:</h4>";
        echo "<p><strong>Unexpected Output Before JSON:</strong> " . (empty($output) ? "None (Good!)" : "<span style='color: red;'>Found: " . htmlspecialchars($output) . "</span>") . "</p>";
        
        echo "<h4>JSON Response:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT));
        echo "</pre>";
        
        echo "<h4>Response Analysis:</h4>";
        echo "<ul>";
        echo "<li><strong>Success Flag:</strong> " . ($result['success'] ? "✅ True" : "❌ False") . "</li>";
        echo "<li><strong>Has Results:</strong> " . (isset($result['results']) ? "✅ Yes (" . count($result['results']) . " items)" : "❌ No") . "</li>";
        echo "<li><strong>Total Files:</strong> " . ($result['total_files'] ?? 'Not set') . "</li>";
        echo "<li><strong>Successful Uploads:</strong> " . ($result['successful_uploads'] ?? 'Not set') . "</li>";
        echo "</ul>";
        
        if (isset($result['results'])) {
            echo "<h4>Individual File Results:</h4>";
            foreach ($result['results'] as $i => $fileResult) {
                echo "<div style='background: " . ($fileResult['success'] ? '#d4edda' : '#f8d7da') . "; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
                echo "<strong>File " . ($i + 1) . ":</strong> " . htmlspecialchars($fileResult['name'] ?? 'Unknown') . "<br>";
                echo "<strong>Success:</strong> " . ($fileResult['success'] ? "✅ Yes" : "❌ No") . "<br>";
                echo "<strong>Message:</strong> " . htmlspecialchars($fileResult['message'] ?? 'No message') . "<br>";
                if (isset($fileResult['page_id'])) {
                    echo "<strong>Page ID:</strong> " . $fileResult['page_id'] . "<br>";
                }
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        $output = ob_get_clean();
        echo "<p style='color: red;'><strong>Exception:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>Output before exception:</strong> " . htmlspecialchars($output) . "</p>";
    }
}

echo "<h2>2. Test Delete Response</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_delete'])) {
    echo "<h3>Delete Test Results:</h3>";
    
    $pageId = $_POST['page_id'] ?? 0;
    
    if ($pageId) {
        ob_start();
        
        try {
            // Test delete functionality
            require_once 'includes/page_manager.php';
            
            $manager = new PageManager();
            $result = $manager->deletePage($pageId, true);
            
            $output = ob_get_clean();
            
            echo "<h4>Raw Response Analysis:</h4>";
            echo "<p><strong>Unexpected Output Before JSON:</strong> " . (empty($output) ? "None (Good!)" : "<span style='color: red;'>Found: " . htmlspecialchars($output) . "</span>") . "</p>";
            
            echo "<h4>Delete Response:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT));
            echo "</pre>";
            
            echo "<h4>Response Analysis:</h4>";
            echo "<ul>";
            echo "<li><strong>Success Flag:</strong> " . ($result['success'] ? "✅ True" : "❌ False") . "</li>";
            echo "<li><strong>Message:</strong> " . htmlspecialchars($result['message'] ?? 'No message') . "</li>";
            echo "<li><strong>Deleted Files:</strong> " . ($result['deleted_files'] ?? 'Not set') . "</li>";
            echo "<li><strong>Affected Shares:</strong> " . ($result['affected_shares'] ?? 'Not set') . "</li>";
            echo "</ul>";
            
        } catch (Exception $e) {
            $output = ob_get_clean();
            echo "<p style='color: red;'><strong>Exception:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p><strong>Output before exception:</strong> " . htmlspecialchars($output) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>No page ID provided for delete test.</p>";
    }
}

echo "<h2>3. Common Issues Analysis</h2>";

echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px;'>";
echo "<h3>🔍 Potential Causes of False 'Failed' Messages</h3>";
echo "<ul>";
echo "<li><strong>PHP Warnings/Notices:</strong> Any PHP output before JSON corrupts the response</li>";
echo "<li><strong>Incorrect Content-Type:</strong> Missing or wrong Content-Type header</li>";
echo "<li><strong>JavaScript Parsing:</strong> JSON.parse() failing due to malformed response</li>";
echo "<li><strong>Response Structure:</strong> JavaScript expecting different response format</li>";
echo "<li><strong>Error Handling:</strong> Catch blocks triggering incorrectly</li>";
echo "<li><strong>Network Issues:</strong> HTTP status codes causing fetch() to throw</li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. Test Forms</h2>";

echo "<h3>Upload Test</h3>";
echo "<form method='POST' enctype='multipart/form-data' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 10px 0;'>";
echo "<p>Upload a test file to see the exact response:</p>";
echo "<input type='file' name='test_upload' accept='.html,.htm,.txt' required>";
echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-left: 10px;'>Test Upload</button>";
echo "</form>";

echo "<h3>Delete Test</h3>";
echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 10px 0;'>";
echo "<p>Test delete functionality (enter a page ID):</p>";
echo "<input type='number' name='page_id' placeholder='Page ID' required>";
echo "<input type='hidden' name='test_delete' value='1'>";
echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-left: 10px;'>Test Delete</button>";
echo "</form>";

echo "<h2>5. Check Current Pages</h2>";

try {
    require_once 'includes/page_manager.php';
    $manager = new PageManager();
    $result = $manager->getPages();
    
    if ($result['success'] && !empty($result['pages'])) {
        echo "<h3>Available Pages for Testing:</h3>";
        echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th>ID</th><th>Title</th><th>Filename</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($result['pages'] as $page) {
            echo "<tr>";
            echo "<td>" . $page['id'] . "</td>";
            echo "<td>" . htmlspecialchars($page['title'] ?? 'No title') . "</td>";
            echo "<td>" . htmlspecialchars($page['original_filename'] ?? $page['filename'] ?? 'No filename') . "</td>";
            echo "<td>" . htmlspecialchars($page['status'] ?? 'unknown') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No pages found or error loading pages.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error loading pages: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>6. JavaScript Response Handling Check</h2>";

echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
echo "<h4>Current JavaScript Upload Flow:</h4>";
echo "<pre style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('// In uploadFiles method:
const response = await fetch("includes/enhanced_upload.php", {
    method: "POST",
    body: formData
});

if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
}

const responseText = await response.text();
let result = JSON.parse(responseText);

// Show results
this.displayEnhancedUploadResults(result);

// Check success
if (result.success) {
    this.loadPages(); // ✅ This should happen
} else {
    // ❌ This might be the issue
}');
echo "</pre>";
echo "</div>";

echo "<h2>7. Recommendations</h2>";

echo "<div style='background: #d1ecf1; color: #0c5460; padding: 20px; border-radius: 8px;'>";
echo "<h3>🔧 Fixes to Implement</h3>";
echo "<ol>";
echo "<li><strong>Add Content-Type Headers:</strong> Ensure PHP files output proper JSON headers</li>";
echo "<li><strong>Remove Debug Output:</strong> Eliminate any echo/print statements before JSON</li>";
echo "<li><strong>Improve Error Handling:</strong> Better JavaScript error detection</li>";
echo "<li><strong>Add Response Validation:</strong> Check response structure before processing</li>";
echo "<li><strong>Enhanced Logging:</strong> Add console.log for debugging response issues</li>";
echo "</ol>";
echo "</div>";

?>

<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
