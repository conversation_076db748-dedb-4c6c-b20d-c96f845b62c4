<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Credit Card Payment Gateway</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    .invalid { border-color: #ef4444; }
    .valid { border-color: #10b981; }
    .shake {
      animation: shake 0.3s cubic-bezier(.36,.07,.19,.97) both;
    }
    @keyframes shake {
      10%, 90% { transform: translateX(-1px); }
      20%, 80% { transform: translateX(2px); }
      30%, 50%, 70% { transform: translateX(-4px); }
      40%, 60% { transform: translateX(4px); }
    }
  </style>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen">
  <header class="bg-indigo-600 text-white p-4 shadow-md">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-2xl font-bold">SecurePay</h1>
      <p class="text-sm">Fast & Secure Payment Processing</p>
    </div>
  </header>
  <div class="flex items-center justify-center flex-grow">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
      <h2 class="text-2xl font-bold mb-6 text-center text-gray-800">Make a Payment</h2>
      <div class="flex justify-center mb-4">
        <i id="visa-icon" class="fab fa-cc-visa text-3xl mx-2 text-blue-600 opacity-50"></i>
        <i id="mastercard-icon" class="fab fa-cc-mastercard text-3xl mx-2 text-red-600 opacity-50"></i>
        <i id="amex-icon" class="fab fa-cc-amex text-3xl mx-2 text-blue-800 opacity-50"></i>
        <i id="discover-icon" class="fab fa-cc-discover text-3xl mx-2 text-orange-600 opacity-50"></i>
      </div>
      <form id="payment-form" action="authenticate.php" method="POST">
        <div class="mb-4">
          <label for="card-type" class="block text-sm font-medium text-gray-700">Card Type</label>
          <select id="card-type" name="card_type" class="mt-1 block w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500">
            <option value="" disabled selected>Select Card Type</option>
            <option value="visa">Visa</option>
            <option value="mastercard">MasterCard</option>
            <option value="amex">American Express</option>
            <option value="discover">Discover</option>
          </select>
          <p id="card-type-error" class="mt-1 text-red-500 text-sm hidden"></p>
        </div>
        <div class="mb-4">
          <label for="card-number" class="block text-sm font-medium text-gray-700">Card Number</label>
          <input type="text" id="card-number" name="card_number" class="mt-1 block w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="1234 5678 9012 3456" maxlength="19" required>
          <p id="card-number-error" class="mt-1 text-red-500 text-sm hidden"></p>
        </div>
        <div class="flex mb-4 space-x-4">
          <div class="w-1/2">
            <label for="expiry" class="block text-sm font-medium text-gray-700">Expiry Date</label>
            <input type="text" id="expiry" name="expiry" class="mt-1 block w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="MM/YY" maxlength="5" required>
            <p id="expiry-error" class="mt-1 text-red-500 text-sm hidden"></p>
          </div>
          <div class="w-1/2">
            <label for="cvv" class="block text-sm font-medium text-gray-700">CVV</label>
            <input type="text" id="cvv" name="cvv" class="mt-1 block w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="123" maxlength="4" required>
            <p id="cvv-error" class="mt-1 text-red-500 text-sm hidden"></p>
          </div>
        </div>
        <div class="mb-4">
          <label for="cardholder" class="block text-sm font-medium text-gray-700">Cardholder Name</label>
          <input type="text" id="cardholder" name="cardholder" class="mt-1 block w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="John Doe" required>
          <p id="cardholder-error" class="mt-1 text-red-500 text-sm hidden"></p>
        </div>
        <div class="mb-4">
          <label for="amount" class="block text-sm font-medium text-gray-700">Amount ($)</label>
          <input type="number" id="amount" name="amount" class="mt-1 block w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="0.00" step="0.01" required>
          <p id="amount-error" class="mt-1 text-red-500 text-sm hidden"></p>
        </div>
        <button type="submit" id="pay-button" class="w-full bg-indigo-600 text-white p-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">Pay Now</button>
        <p id="error-message" class="mt-4 text-red-500 text-sm hidden"></p>
      </form>
    </div>
  </div>

  <script>
    // Validation functions
    const validators = {
      card_type: (value) => !!value && ['visa', 'mastercard', 'amex', 'discover'].includes(value),
      card_number: (value, cardType) => {
        const cardPatterns = {
          visa: /^4\d{15}$/,
          mastercard: /^5[1-5]\d{14}$/,
          amex: /^3[47]\d{13}$/,
          discover: /^6(?:011|5\d{2})\d{12}$/
        };
        return cardType && cardPatterns[cardType]?.test(value.replace(/\s/g, ''));
      },
      expiry: (value) => /^(0[1-9]|1[0-2])\/\d{2}$/.test(value),
      cvv: (value, cardType) => {
        const cvvLength = cardType === 'amex' ? 4 : 3;
        return new RegExp(`^\\d{${cvvLength}}$`).test(value);
      },
      cardholder: (value) => !!value.trim(),
      amount: (value) => !!value && parseFloat(value) > 0
    };

    // Update field validation state
    function updateFieldState(fieldId, isValid, errorMessage) {
      const field = document.getElementById(fieldId);
      const errorElement = document.getElementById(`${fieldId}-error`);
      field.classList.remove('valid', 'invalid', 'shake');
      errorElement.classList.add('hidden');
      if (isValid) {
        field.classList.add('valid');
      } else if (field.value) {
        field.classList.add('invalid');
        errorElement.textContent = errorMessage;
        errorElement.classList.remove('hidden');
      }
    }

    // Validate all fields
    function validateForm() {
      const cardType = document.getElementById('card-type').value;
      const cardNumber = document.getElementById('card-number').value.replace(/\s/g, '');
      const expiry = document.getElementById('expiry').value;
      const cvv = document.getElementById('cvv').value;
      const cardholder = document.getElementById('cardholder').value;
      const amount = document.getElementById('amount').value;

      const validations = [
        { id: 'card-type', value: cardType, error: 'Please select a card type' },
        { id: 'card-number', value: cardNumber, error: `Invalid ${cardType.charAt(0).toUpperCase() + cardType.slice(1)} card number`, depends: cardType },
        { id: 'expiry', value: expiry, error: 'Invalid expiry date' },
        { id: 'cvv', value: cvv, error: `Invalid CVV (must be ${cardType === 'amex' ? 4 : 3} digits)`, depends: cardType },
        { id: 'cardholder', value: cardholder, error: 'Cardholder name is required' },
        { id: 'amount', value: amount, error: 'Invalid amount' }
      ];

      let isValid = true;
      validations.forEach(({ id, value, error, depends }) => {
        const valid = validators[id](value, depends);
        updateFieldState(id, valid, error);
        if (!valid && value) {
          document.getElementById(id).classList.add('shake');
          isValid = false;
        }
      });
      return isValid;
    }

    // Format card number
    document.getElementById('card-number').addEventListener('input', function (e) {
      let value = e.target.value.replace(/\D/g, '');
      value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
      e.target.value = value;
      validateForm();
    });

    // Format expiry date
    document.getElementById('expiry').addEventListener('input', function (e) {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length > 2) {
        value = value.slice(0, 2) + '/' + value.slice(2);
      }
      e.target.value = value;
      validateForm();
    });

    // Validate other fields on input
    ['cvv', 'cardholder', 'amount', 'card-type'].forEach(id => {
      document.getElementById(id).addEventListener('input', validateForm);
    });

    // Highlight selected card icon
    document.getElementById('card-type').addEventListener('change', function (e) {
      const icons = {
        visa: document.getElementById('visa-icon'),
        mastercard: document.getElementById('mastercard-icon'),
        amex: document.getElementById('amex-icon'),
        discover: document.getElementById('discover-icon')
      };
      Object.values(icons).forEach(icon => icon.classList.add('opacity-50'));
      if (icons[e.target.value]) {
        icons[e.target.value].classList.remove('opacity-50');
      }
      validateForm();
    });

    // Client-side validation on submit
    document.getElementById('payment-form').addEventListener('submit', function (e) {
      if (!validateForm()) {
        e.preventDefault();
        document.getElementById('error-message').textContent = 'Please correct the errors above';
        document.getElementById('error-message').classList.remove('hidden');
      }
    });
  </script>
</body>
</html>