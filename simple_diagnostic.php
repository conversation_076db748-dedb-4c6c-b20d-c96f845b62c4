<?php
/**
 * Simple Diagnostic
 * Basic check to see what's actually working
 */

// Show all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Simple Diagnostic</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>1. File Existence Check</h2>";

$files = [
    'config/database.php',
    'submit_form.php',
    'enhanced_submit_form.php',
    'includes/deployment_config.php',
    'includes/enhanced_zip_extractor.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ $file exists</p>";
    } else {
        echo "<p>❌ $file missing</p>";
    }
}

echo "<h2>2. Database Connection Test</h2>";

try {
    require_once 'config/database.php';
    echo "<p>✅ database.php loaded successfully</p>";
    
    $database = new Database();
    echo "<p>✅ Database class instantiated</p>";
    
    $db = $database->getConnection();
    if ($db) {
        echo "<p>✅ Database connection successful</p>";
        
        // Test basic query
        $sql = "SELECT 1 as test";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        echo "<p>✅ Basic query works: " . $result['test'] . "</p>";
        
    } else {
        echo "<p>❌ Database connection failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
}

echo "<h2>3. Table Existence Check</h2>";

if (isset($db) && $db) {
    $tables = ['pages', 'page_shares', 'form_submissions', 'forms', 'form_fields'];
    
    foreach ($tables as $table) {
        try {
            $sql = "SELECT COUNT(*) as count FROM $table";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch();
            echo "<p>✅ Table '$table' exists with {$result['count']} records</p>";
        } catch (Exception $e) {
            echo "<p>❌ Table '$table' error: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<h2>4. Form Submissions Table Structure</h2>";

if (isset($db) && $db) {
    try {
        $sql = "SHOW COLUMNS FROM form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>✅ form_submissions table has " . count($columns) . " columns</p>";
        echo "<table border='1' cellpadding='3'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p>❌ Cannot check form_submissions structure: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>5. Page Shares Table Check</h2>";

if (isset($db) && $db) {
    try {
        $sql = "SHOW COLUMNS FROM page_shares";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $columnNames = array_column($columns, 'Field');
        
        if (in_array('page_id', $columnNames)) {
            echo "<p>✅ page_shares has page_id column</p>";
        } else {
            echo "<p>❌ page_shares missing page_id column</p>";
        }
        
        // Test the join query
        $sql = "SELECT ps.id, ps.share_token, p.title 
                FROM page_shares ps 
                LEFT JOIN pages p ON ps.page_id = p.id 
                LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result) {
            echo "<p>✅ Join query works</p>";
        } else {
            echo "<p>⚠️ Join query returns no results (normal if no shares exist)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Page shares query error: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>6. Simple Form Submission Test</h2>";

if (isset($db) && $db) {
    try {
        // Try the simplest possible insertion
        $sql = "INSERT INTO form_submissions (page_id, submission_data, ip_address, submitted_at) 
                VALUES (1, '{\"test\": \"simple\"}', '127.0.0.1', NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        
        $insertId = $db->lastInsertId();
        echo "<p>✅ Simple form submission successful, ID: $insertId</p>";
        
        // Verify it was inserted
        $sql = "SELECT * FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$insertId]);
        $inserted = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($inserted) {
            echo "<p>✅ Record verified in database</p>";
            echo "<p><strong>Data:</strong> {$inserted['submission_data']}</p>";
        }
        
        // Clean up
        $sql = "DELETE FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$insertId]);
        echo "<p>✅ Test record cleaned up</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Simple form submission failed: " . $e->getMessage() . "</p>";
        echo "<p><strong>SQL State:</strong> " . $e->getCode() . "</p>";
    }
}

echo "<h2>7. Error Log Check</h2>";

$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    echo "<p><strong>Error log location:</strong> $errorLog</p>";
    $errors = file_get_contents($errorLog);
    $recentErrors = array_slice(explode("\n", $errors), -10);
    echo "<p><strong>Recent errors:</strong></p>";
    echo "<pre>" . implode("\n", $recentErrors) . "</pre>";
} else {
    echo "<p>No error log found or configured</p>";
}

echo "<h2>Summary</h2>";

if (isset($db) && $db) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Basic System Status: Working</h4>";
    echo "<p>Database connection and basic operations are functional.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Critical Issue: Database Connection Failed</h4>";
    echo "<p>Cannot connect to database. Check database configuration.</p>";
    echo "</div>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 5px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
