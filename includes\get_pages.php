<?php
require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get all pages with form counts
    $sql = "SELECT 
                p.id,
                p.filename,
                p.original_filename,
                p.title,
                p.file_size,
                p.created_at,
                p.updated_at,
                COUNT(f.id) as forms_count
            FROM pages p
            LEFT JOIN forms f ON p.id = f.page_id
            GROUP BY p.id
            ORDER BY p.created_at DESC";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($pages);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
