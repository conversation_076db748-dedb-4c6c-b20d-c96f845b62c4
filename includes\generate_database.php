<?php
require_once '../config/database.php';
require_once 'html_parser.php';

header('Content-Type: application/json');

class DatabaseGenerator {
    private $db;
    private $parser;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->parser = new HTMLParser();
    }

    public function generateDatabaseStructure() {
        try {
            // Get all forms with their fields
            $sql = "SELECT 
                        f.id as form_id,
                        f.form_name,
                        f.form_action,
                        p.filename,
                        p.title as page_title
                    FROM forms f
                    JOIN pages p ON f.page_id = p.id
                    ORDER BY f.id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $generatedTables = [];

            foreach ($forms as $form) {
                $tableName = $this->generateTableName($form);
                
                // Get fields for this form
                $sql = "SELECT * FROM form_fields WHERE form_id = ? ORDER BY id";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$form['form_id']]);
                $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (empty($fields)) {
                    continue; // Skip forms without fields
                }

                $tableSQL = $this->generateCreateTableSQL($tableName, $fields);
                
                // Check if table already exists in generated_tables
                $sql = "SELECT id FROM generated_tables WHERE form_id = ?";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$form['form_id']]);
                $existingTable = $stmt->fetch();

                if ($existingTable) {
                    // Update existing record
                    $sql = "UPDATE generated_tables SET table_name = ?, table_sql = ? WHERE form_id = ?";
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([$tableName, $tableSQL, $form['form_id']]);
                } else {
                    // Insert new record
                    $sql = "INSERT INTO generated_tables (table_name, form_id, table_sql) VALUES (?, ?, ?)";
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([$tableName, $form['form_id'], $tableSQL]);
                }

                $generatedTables[] = [
                    'form_id' => $form['form_id'],
                    'table_name' => $tableName,
                    'sql' => $tableSQL,
                    'form_info' => $form
                ];
            }

            return [
                'success' => true,
                'message' => 'Database structure generated successfully',
                'tables' => $generatedTables
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error generating database structure: ' . $e->getMessage()
            ];
        }
    }

    private function generateTableName($form) {
        // Start with form name or action
        $baseName = '';
        
        if (!empty($form['form_name'])) {
            $baseName = $form['form_name'];
        } elseif (!empty($form['form_action'])) {
            $baseName = basename($form['form_action'], '.php');
        } else {
            $baseName = 'form_' . $form['form_id'];
        }

        // Clean the name
        $tableName = preg_replace('/[^a-zA-Z0-9_]/', '_', $baseName);
        $tableName = preg_replace('/_+/', '_', $tableName);
        $tableName = trim($tableName, '_');
        $tableName = strtolower($tableName);

        // Ensure it starts with a letter
        if (!preg_match('/^[a-z]/', $tableName)) {
            $tableName = 'tbl_' . $tableName;
        }

        // Limit length
        if (strlen($tableName) > 60) {
            $tableName = substr($tableName, 0, 60);
        }

        return $tableName;
    }

    private function generateCreateTableSQL($tableName, $fields) {
        $sql = "CREATE TABLE IF NOT EXISTS `$tableName` (\n";
        $sql .= "    `id` INT AUTO_INCREMENT PRIMARY KEY,\n";

        $fieldDefinitions = [];
        $uniqueFields = [];

        foreach ($fields as $field) {
            if (empty($field['field_name'])) {
                continue; // Skip fields without names
            }

            $fieldName = $this->sanitizeFieldName($field['field_name']);
            $fieldType = $this->getFieldType($field);
            $constraints = $this->getFieldConstraints($field);

            $fieldDefinition = "    `$fieldName` $fieldType";
            if ($constraints) {
                $fieldDefinition .= " $constraints";
            }

            $fieldDefinitions[] = $fieldDefinition;

            // Track unique fields
            if ($field['field_type'] === 'email' || 
                strpos($field['field_name'], 'email') !== false ||
                strpos($field['field_name'], 'username') !== false) {
                $uniqueFields[] = $fieldName;
            }
        }

        $sql .= implode(",\n", $fieldDefinitions);

        // Add unique constraints
        foreach ($uniqueFields as $uniqueField) {
            $sql .= ",\n    UNIQUE KEY `uk_{$tableName}_{$uniqueField}` (`$uniqueField`)";
        }

        // Add timestamps
        $sql .= ",\n    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
        $sql .= ",\n    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";

        $sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        return $sql;
    }

    private function sanitizeFieldName($fieldName) {
        // Convert to snake_case and sanitize
        $fieldName = preg_replace('/[^a-zA-Z0-9_]/', '_', $fieldName);
        $fieldName = preg_replace('/_+/', '_', $fieldName);
        $fieldName = trim($fieldName, '_');
        $fieldName = strtolower($fieldName);

        // Ensure it starts with a letter
        if (!preg_match('/^[a-z]/', $fieldName)) {
            $fieldName = 'field_' . $fieldName;
        }

        // Handle reserved words
        $reservedWords = ['order', 'group', 'select', 'from', 'where', 'limit', 'index', 'key', 'table'];
        if (in_array($fieldName, $reservedWords)) {
            $fieldName = 'field_' . $fieldName;
        }

        return $fieldName;
    }

    private function getFieldType($field) {
        $type = strtolower($field['field_type']);
        $attributes = [];

        // Extract numeric attributes
        if ($field['field_maxlength']) $attributes['maxlength'] = $field['field_maxlength'];
        if ($field['field_min']) $attributes['min'] = $field['field_min'];
        if ($field['field_max']) $attributes['max'] = $field['field_max'];
        if ($field['field_step']) $attributes['step'] = $field['field_step'];

        switch ($type) {
            case 'email':
                return 'VARCHAR(255)';
            
            case 'url':
                return 'VARCHAR(500)';
            
            case 'tel':
            case 'phone':
                return 'VARCHAR(20)';
            
            case 'number':
            case 'range':
                if (isset($attributes['step']) && strpos($attributes['step'], '.') !== false) {
                    return 'DECIMAL(10,2)';
                }
                if (isset($attributes['max']) && $attributes['max'] > 2147483647) {
                    return 'BIGINT';
                }
                return 'INT';
            
            case 'date':
                return 'DATE';
            
            case 'datetime':
            case 'datetime-local':
                return 'DATETIME';
            
            case 'time':
                return 'TIME';
            
            case 'checkbox':
                return 'BOOLEAN';
            
            case 'radio':
                // For radio buttons, use VARCHAR to store the selected value
                return 'VARCHAR(100)';
            
            case 'select':
                // Analyze options to determine appropriate type
                if ($field['field_options']) {
                    $options = json_decode($field['field_options'], true);
                    if (is_array($options)) {
                        $maxLength = 0;
                        foreach ($options as $option) {
                            if (is_array($option) && isset($option['value'])) {
                                $maxLength = max($maxLength, strlen($option['value']));
                            }
                        }
                        return $maxLength > 255 ? 'TEXT' : 'VARCHAR(' . max(100, $maxLength + 50) . ')';
                    }
                }
                return 'VARCHAR(255)';
            
            case 'textarea':
                return 'TEXT';
            
            case 'password':
                return 'VARCHAR(255)'; // For hashed passwords
            
            case 'file':
                return 'VARCHAR(500)'; // File path
            
            case 'hidden':
            case 'text':
            case 'search':
            default:
                $maxLength = isset($attributes['maxlength']) ? (int)$attributes['maxlength'] : 255;
                if ($maxLength > 65535) {
                    return 'LONGTEXT';
                } elseif ($maxLength > 255) {
                    return 'TEXT';
                } else {
                    return "VARCHAR($maxLength)";
                }
        }
    }

    private function getFieldConstraints($field) {
        $constraints = [];

        // NOT NULL constraint
        if ($field['field_required']) {
            $constraints[] = 'NOT NULL';
        }

        // Default value
        if ($field['field_value'] && !$field['field_required']) {
            $defaultValue = $this->db->quote($field['field_value']);
            $constraints[] = "DEFAULT $defaultValue";
        }

        return implode(' ', $constraints);
    }

    public function createActualTables() {
        try {
            // Get all generated table structures
            $sql = "SELECT * FROM generated_tables ORDER BY id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $createdTables = [];
            $errors = [];

            foreach ($tables as $table) {
                try {
                    $this->db->exec($table['table_sql']);
                    $createdTables[] = $table['table_name'];
                } catch (PDOException $e) {
                    $errors[] = "Error creating table {$table['table_name']}: " . $e->getMessage();
                }
            }

            return [
                'success' => empty($errors),
                'created_tables' => $createdTables,
                'errors' => $errors
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error creating tables: ' . $e->getMessage()
            ];
        }
    }
}

// Handle the request
try {
    $generator = new DatabaseGenerator();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? 'generate';
        
        if ($action === 'create') {
            $result = $generator->createActualTables();
        } else {
            $result = $generator->generateDatabaseStructure();
        }
    } else {
        $result = $generator->generateDatabaseStructure();
    }
    
    echo json_encode($result);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
