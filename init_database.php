<?php
/**
 * Database Initialization Script
 * Ensures all required tables exist for sharing and form submissions
 */

require_once 'config/database.php';

echo "<h1>Database Initialization</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Create form_submissions table if it doesn't exist
    echo "<h2>Creating/Updating Tables</h2>";
    
    $sql = "CREATE TABLE IF NOT EXISTS form_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_id INT NOT NULL,
        form_id INT NULL,
        share_id INT NULL,
        form_data JSON NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        INDEX idx_page_id (page_id),
        INDEX idx_form_id (form_id),
        INDEX idx_share_id (share_id),
        INDEX idx_submitted_at (submitted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ form_submissions table created/updated</p>";
    
    // Ensure page_shares table has all required columns
    $sql = "SHOW COLUMNS FROM page_shares LIKE 'show_forms'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $showFormsExists = $stmt->fetch();
    
    if (!$showFormsExists) {
        $sql = "ALTER TABLE page_shares ADD COLUMN show_forms BOOLEAN DEFAULT TRUE";
        $db->exec($sql);
        echo "<p>✅ Added show_forms column to page_shares</p>";
    }
    
    // Ensure share_access_log table exists
    $sql = "CREATE TABLE IF NOT EXISTS share_access_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        share_id INT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referrer TEXT,
        access_type ENUM('view', 'form_submission', 'download') DEFAULT 'view',
        accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_share_id (share_id),
        INDEX idx_accessed_at (accessed_at),
        INDEX idx_access_type (access_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ share_access_log table created/updated</p>";
    
    // Check table status
    echo "<h2>Table Status</h2>";
    
    $tables = [
        'pages' => 'Pages storage',
        'page_shares' => 'Page sharing',
        'share_access_log' => 'Share access tracking',
        'form_submissions' => 'Form submission data',
        'forms' => 'Form definitions',
        'form_fields' => 'Form field definitions',
        'associated_files' => 'Page assets'
    ];
    
    foreach ($tables as $table => $description) {
        $sql = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $exists = $stmt->fetch();
        
        if ($exists) {
            // Get row count
            $sql = "SELECT COUNT(*) as count FROM $table";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $count = $result['count'];
            
            echo "<p>✅ $table ($description): $count rows</p>";
        } else {
            echo "<p>❌ $table ($description): Missing</p>";
        }
    }
    
    // Test sharing functionality
    echo "<h2>Testing Sharing System</h2>";
    
    // Check if we have any pages
    $sql = "SELECT COUNT(*) as count FROM pages";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $pageCount = $result['count'];
    
    if ($pageCount == 0) {
        echo "<p>⚠️ No pages found. Creating test page...</p>";
        
        // Create uploads directory
        if (!is_dir('uploads')) {
            mkdir('uploads', 0755, true);
            echo "<p>✅ Created uploads directory</p>";
        }
        
        // Create test HTML file
        $testHtml = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Form</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>Contact Us</h1>
    <form method="POST" action="">
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" required>
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" rows="5" required></textarea>
        </div>
        <button type="submit">Send Message</button>
    </form>
</body>
</html>';
        
        $testFile = 'uploads/test_contact_form.html';
        file_put_contents($testFile, $testHtml);
        
        // Insert into database
        $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            'test_contact_form.html',
            'test_contact_form.html',
            'Test Contact Form',
            $testFile,
            strlen($testHtml),
            md5($testHtml)
        ]);
        
        $pageId = $db->lastInsertId();
        echo "<p>✅ Created test page with ID: $pageId</p>";
        
        // Create a form record
        $sql = "INSERT INTO forms (page_id, form_name, form_method, form_action) 
                VALUES (?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$pageId, 'Contact Form', 'POST', '']);
        
        $formId = $db->lastInsertId();
        echo "<p>✅ Created form record with ID: $formId</p>";
        
        // Create field records
        $fields = [
            ['name', 'text', 'Name'],
            ['email', 'email', 'Email'],
            ['message', 'textarea', 'Message']
        ];
        
        foreach ($fields as $index => $field) {
            $sql = "INSERT INTO form_fields (form_id, field_name, field_type, field_label, field_order) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$formId, $field[0], $field[1], $field[2], $index]);
        }
        
        echo "<p>✅ Created " . count($fields) . " field records</p>";
    }
    
    echo "<h2>Quick Links</h2>";
    echo "<p><a href='test_sharing.php'>Test Sharing System</a></p>";
    echo "<p><a href='debug_view.php'>Debug View System</a></p>";
    echo "<p><a href='index.html'>Main Application</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
p { margin: 5px 0; }
</style>
