<?php
/**
 * Database Migration Script for Webpage Manager v2.0
 * Adds missing columns and tables to existing databases
 */

require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - Webpage Manager v2.0</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .migration-step { margin: 15px 0; padding: 10px; border-left: 4px solid #667eea; background: #f8f9ff; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Migration to v2.0</h1>
        <p>This script will update your existing database to support the new sharing and enhanced features.</p>

<?php

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Cannot connect to database");
    }
    
    echo "<div class='info'>Connected to database successfully.</div>";
    
    $migrations = [];
    
    // Check and add missing columns to pages table
    echo "<div class='migration-step'>";
    echo "<h3>Checking pages table...</h3>";
    
    $sql = "SHOW COLUMNS FROM pages";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = [
        'project_id' => 'INT DEFAULT NULL',
        'description' => 'TEXT',
        'status' => "ENUM('active', 'archived', 'deleted') DEFAULT 'active'",
        'version' => 'INT DEFAULT 1',
        'meta_keywords' => 'TEXT',
        'meta_description' => 'TEXT',
        'language' => "VARCHAR(10) DEFAULT 'en'",
        'charset' => "VARCHAR(50) DEFAULT 'UTF-8'",
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $columns)) {
            $sql = "ALTER TABLE pages ADD COLUMN $column $definition";
            $db->exec($sql);
            echo "<div class='success'>✓ Added column '$column' to pages table</div>";
            $migrations[] = "Added pages.$column";
        } else {
            echo "<div class='info'>Column '$column' already exists in pages table</div>";
        }
    }
    echo "</div>";
    
    // Check and add missing columns to forms table
    echo "<div class='migration-step'>";
    echo "<h3>Checking forms table...</h3>";
    
    $sql = "SHOW COLUMNS FROM forms";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = [
        'form_target' => 'VARCHAR(50)',
        'form_autocomplete' => "ENUM('on', 'off') DEFAULT 'on'",
        'form_novalidate' => 'BOOLEAN DEFAULT FALSE',
        'form_description' => 'TEXT',
        'is_active' => 'BOOLEAN DEFAULT TRUE',
        'validation_rules' => 'JSON',
        'custom_attributes' => 'JSON',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $columns)) {
            $sql = "ALTER TABLE forms ADD COLUMN $column $definition";
            $db->exec($sql);
            echo "<div class='success'>✓ Added column '$column' to forms table</div>";
            $migrations[] = "Added forms.$column";
        } else {
            echo "<div class='info'>Column '$column' already exists in forms table</div>";
        }
    }
    echo "</div>";
    
    // Check and add missing columns to form_fields table
    echo "<div class='migration-step'>";
    echo "<h3>Checking form_fields table...</h3>";
    
    $sql = "SHOW COLUMNS FROM form_fields";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = [
        'field_label' => 'VARCHAR(500)',
        'field_validation_rules' => 'JSON',
        'field_custom_attributes' => 'JSON',
        'field_order' => 'INT DEFAULT 0',
        'is_active' => 'BOOLEAN DEFAULT TRUE',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $columns)) {
            $sql = "ALTER TABLE form_fields ADD COLUMN $column $definition";
            $db->exec($sql);
            echo "<div class='success'>✓ Added column '$column' to form_fields table</div>";
            $migrations[] = "Added form_fields.$column";
        } else {
            echo "<div class='info'>Column '$column' already exists in form_fields table</div>";
        }
    }
    echo "</div>";
    
    // Check and create missing tables
    echo "<div class='migration-step'>";
    echo "<h3>Checking for missing tables...</h3>";
    
    $sql = "SHOW TABLES";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredTables = [
        'users' => "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(255) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(255),
            role ENUM('admin', 'editor', 'viewer') DEFAULT 'editor',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_role (role)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'projects' => "CREATE TABLE projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            slug VARCHAR(255) NOT NULL UNIQUE,
            color VARCHAR(7) DEFAULT '#667eea',
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_slug (slug),
            INDEX idx_created_by (created_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'page_shares' => "CREATE TABLE page_shares (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            share_token VARCHAR(32) NOT NULL UNIQUE,
            short_code VARCHAR(10) NOT NULL UNIQUE,
            title VARCHAR(255),
            description TEXT,
            password_hash VARCHAR(255) DEFAULT NULL,
            expires_at TIMESTAMP NULL,
            max_views INT DEFAULT NULL,
            view_count INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            allow_download BOOLEAN DEFAULT FALSE,
            show_forms BOOLEAN DEFAULT TRUE,
            show_metadata BOOLEAN DEFAULT FALSE,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
            INDEX idx_share_token (share_token),
            INDEX idx_short_code (short_code),
            INDEX idx_page_id (page_id),
            INDEX idx_expires_at (expires_at),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'share_access_log' => "CREATE TABLE share_access_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            share_id INT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            referrer VARCHAR(500),
            country VARCHAR(2),
            city VARCHAR(100),
            access_type ENUM('view', 'download', 'form_submit') DEFAULT 'view',
            accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (share_id) REFERENCES page_shares(id) ON DELETE CASCADE,
            INDEX idx_share_id (share_id),
            INDEX idx_access_type (access_type),
            INDEX idx_accessed_at (accessed_at),
            INDEX idx_ip_address (ip_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'app_settings' => "CREATE TABLE app_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
            description TEXT,
            is_system BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_setting_key (setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    foreach ($requiredTables as $tableName => $createSql) {
        if (!in_array($tableName, $tables)) {
            $db->exec($createSql);
            echo "<div class='success'>✓ Created table '$tableName'</div>";
            $migrations[] = "Created table $tableName";
        } else {
            echo "<div class='info'>Table '$tableName' already exists</div>";
        }
    }
    echo "</div>";
    
    // Insert default data if needed
    echo "<div class='migration-step'>";
    echo "<h3>Inserting default data...</h3>";
    
    // Insert default user if users table is empty
    $sql = "SELECT COUNT(*) FROM users";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $userCount = $stmt->fetchColumn();
    
    if ($userCount == 0) {
        $sql = "INSERT INTO users (id, username, email, password_hash, full_name, role) 
                VALUES (1, 'admin', 'admin@localhost', '', 'System Administrator', 'admin')";
        $db->exec($sql);
        echo "<div class='success'>✓ Created default admin user</div>";
        $migrations[] = "Created default user";
    }
    
    // Insert default project if projects table is empty
    $sql = "SELECT COUNT(*) FROM projects";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $projectCount = $stmt->fetchColumn();
    
    if ($projectCount == 0) {
        $sql = "INSERT INTO projects (id, name, description, slug, created_by) 
                VALUES (1, 'Default Project', 'Default project for imported pages', 'default', 1)";
        $db->exec($sql);
        echo "<div class='success'>✓ Created default project</div>";
        $migrations[] = "Created default project";
    }
    
    echo "</div>";
    
    // Summary
    echo "<div class='migration-step'>";
    echo "<h3>Migration Summary</h3>";
    
    if (empty($migrations)) {
        echo "<div class='info'>No migrations were needed. Your database is already up to date!</div>";
    } else {
        echo "<div class='success'>Migration completed successfully! Applied " . count($migrations) . " changes:</div>";
        echo "<ul>";
        foreach ($migrations as $migration) {
            echo "<li>$migration</li>";
        }
        echo "</ul>";
    }
    
    echo "<div class='info'>";
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li><a href='index.html'>Return to Webpage Manager</a></li>";
    echo "<li>Test the page viewing functionality</li>";
    echo "<li>Try creating a share in the Sharing tab</li>";
    echo "<li>Import some test pages to verify everything works</li>";
    echo "</ol>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>Migration failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<div class='info'>";
    echo "<h4>Troubleshooting:</h4>";
    echo "<ul>";
    echo "<li>Check your database connection settings in config/database.php</li>";
    echo "<li>Ensure your database user has ALTER and CREATE privileges</li>";
    echo "<li>Verify MySQL version is 5.7 or higher for JSON support</li>";
    echo "<li>Check PHP error logs for detailed error information</li>";
    echo "</ul>";
    echo "</div>";
}

?>

    </div>
</body>
</html>
