# Page Deletion System Guide

This guide explains the comprehensive page deletion and management system that allows you to safely delete uploaded pages with full control over associated files and data.

## Overview

The page deletion system provides multiple ways to manage your uploaded pages:

### 🗑️ **Deletion Options**
- **Individual Page Deletion**: Delete single pages with confirmation
- **Bulk Deletion**: Select and delete multiple pages at once
- **Archive/Restore**: Soft delete with ability to restore
- **File Management**: Choose whether to keep or delete associated files

### 🔒 **Safety Features**
- **Confirmation Dialogs**: Prevent accidental deletions
- **Backup System**: Automatic backup before deletion
- **File Options**: Choose to keep files on disk or remove completely
- **Cascade Protection**: Handles related data (forms, shares, assets)

## Deletion Methods

### 1. **Individual Page Deletion**

#### **From Page Card**
- Click the red trash icon (🗑️) on any page card
- Confirmation modal appears with deletion options
- Choose whether to delete associated files
- Confirm deletion

#### **What Gets Deleted**
- Page record from database
- All associated forms and form fields
- All sharing links and access logs
- Associated files (CSS, JS, images) - optional
- Form submissions and analysis data

### 2. **Bulk Page Deletion**

#### **Selection Process**
1. **Select Pages**: Check boxes next to pages you want to delete
2. **Bulk Actions**: Click "Bulk Actions" button when pages are selected
3. **Choose Action**: Select "Delete (Keep Files)" or "Delete (Remove Files)"
4. **Confirm**: Review and confirm the bulk deletion

#### **Bulk Selection Features**
- **Select All**: Toggle all pages on current view
- **Visual Feedback**: Selected count display
- **Mixed Actions**: Archive, restore, or delete selected pages

### 3. **Archive and Restore**

#### **Archive Pages**
- **Purpose**: Soft delete that can be reversed
- **Method**: Click archive icon (📦) or use bulk actions
- **Status**: Pages marked as "archived" but data preserved
- **Visibility**: Hidden from active pages view

#### **Restore Pages**
- **Filter**: Switch to "Archived Pages" view
- **Method**: Click restore icon (↶) on archived pages
- **Result**: Page returns to active status

## File Management Options

### 🗂️ **Delete Files vs Keep Files**

#### **Delete Files (Complete Removal)**
- ✅ Removes all traces from system
- ✅ Frees up disk space
- ✅ Clean removal for unwanted content
- ❌ Cannot be undone
- ❌ Shared links will break

#### **Keep Files (Database Only)**
- ✅ Files remain on disk
- ✅ Can manually restore if needed
- ✅ Shared links may still work
- ❌ Takes up disk space
- ❌ Orphaned files need cleanup

### 📁 **Associated Files Handled**
- **HTML Files**: Main page files
- **CSS Files**: Stylesheets and themes
- **JavaScript Files**: Interactive functionality
- **Images**: Photos, icons, graphics
- **Fonts**: Custom typography files
- **Documents**: PDFs and other attachments

## User Interface Features

### 🎛️ **Enhanced Management Controls**

#### **Filter Options**
- **Status Filter**: Active, Archived, All pages
- **Project Filter**: Filter by specific projects
- **Search**: Real-time search across page titles and filenames

#### **Selection Tools**
- **Individual Selection**: Checkbox on each page card
- **Select All**: Toggle all pages in current view
- **Clear Selection**: Deselect all pages
- **Selection Counter**: Shows number of selected pages

#### **Bulk Actions Panel**
- **Archive**: Move selected pages to archived status
- **Restore**: Restore archived pages to active status
- **Delete (Keep Files)**: Remove from database, keep files
- **Delete (Remove Files)**: Complete removal including files

### 📊 **Page Statistics**
- **Total Pages**: Count of all pages in system
- **Active Pages**: Currently active pages
- **Archived Pages**: Pages in archived status
- **Total Size**: Combined size of all page files

## Safety and Backup Features

### 🛡️ **Deletion Protection**

#### **Confirmation System**
- **Warning Dialog**: Clear explanation of consequences
- **File Options**: Checkbox to control file deletion
- **Impact Summary**: Shows what will be affected
- **Cancel Option**: Easy way to abort deletion

#### **Backup System**
```sql
deletion_backups table stores:
- Original page data (JSON)
- Associated files information
- Sharing data and access logs
- Deletion timestamp and user
- Restoration capability flag
```

### 🔄 **Recovery Options**

#### **Automatic Backups**
- **Before Deletion**: Complete page data backed up
- **Metadata Preserved**: All form and sharing information
- **File Status**: Tracks whether files still exist
- **Restoration Ready**: Can restore if files available

#### **Manual Recovery**
- **Database Restore**: Restore page records from backup
- **File Recovery**: If files were kept, full restoration possible
- **Partial Recovery**: Restore metadata even if files gone

## Advanced Features

### 🧹 **Cleanup Tools**

#### **Orphaned File Cleanup**
- **Detection**: Finds files without database records
- **Removal**: Safely removes orphaned files
- **Reporting**: Shows what was cleaned up
- **Logging**: Records cleanup activities

#### **Automatic Maintenance**
- **Scheduled Cleanup**: Remove old backups and logs
- **Storage Optimization**: Compress old backup data
- **Performance**: Keep database lean and fast

### 📈 **Analytics and Tracking**

#### **Deletion Analytics**
- **Activity Logging**: All deletions tracked
- **User Actions**: Who deleted what and when
- **Impact Analysis**: Files and data affected
- **Recovery Tracking**: Restoration attempts

#### **Usage Statistics**
- **Storage Usage**: Track disk space usage
- **Deletion Patterns**: Most deleted content types
- **Recovery Rates**: How often pages are restored
- **System Health**: Overall database performance

## Best Practices

### ✅ **Recommended Workflow**

1. **Review Before Deletion**
   - Check page content and associated files
   - Verify no important shares are active
   - Consider archiving instead of deleting

2. **Choose Appropriate Method**
   - **Archive**: For temporary removal
   - **Delete (Keep Files)**: For database cleanup
   - **Delete (Remove Files)**: For complete removal

3. **Bulk Operations**
   - Use filters to isolate pages for deletion
   - Review selection carefully before confirming
   - Consider impact on storage and shares

### ⚠️ **Important Considerations**

#### **Before Deleting**
- **Check Shares**: Active sharing links will break
- **Backup Important**: Export critical pages first
- **Team Coordination**: Notify team members if shared project
- **Storage Impact**: Consider disk space implications

#### **After Deleting**
- **Verify Results**: Check that intended pages were removed
- **Update Links**: Fix any broken internal links
- **Clean References**: Remove references in other pages
- **Monitor Performance**: Check system performance improvement

## Troubleshooting

### 🔧 **Common Issues**

#### **Deletion Fails**
- **File Permissions**: Check write permissions on upload directory
- **Database Locks**: Ensure no other processes are using the page
- **Foreign Key Constraints**: Related data may prevent deletion

#### **Files Not Deleted**
- **Permission Issues**: Server may not have delete permissions
- **File In Use**: File may be locked by another process
- **Path Issues**: File path may have changed

#### **Recovery Problems**
- **Files Missing**: Original files may have been moved or deleted
- **Database Corruption**: Backup data may be incomplete
- **Version Conflicts**: Page structure may have changed

### 🛠️ **Solutions**

#### **Manual Cleanup**
```bash
# Find orphaned files
find uploads/ -type f -name "*.html" | while read file; do
    # Check if file exists in database
    # Remove if orphaned
done
```

#### **Database Repair**
```sql
-- Find and remove orphaned records
DELETE af FROM associated_files af 
LEFT JOIN pages p ON af.page_id = p.id 
WHERE p.id IS NULL;
```

This comprehensive page deletion system ensures safe, controlled removal of uploaded pages while providing flexibility in file management and recovery options!
