<?php
/**
 * Enhanced Setup Script for Webpage Manager v2.0
 * Run this script to initialize the application with the new database structure
 */

require_once 'config/database.php';

// Set up error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering for better error handling
ob_start();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webpage Manager Setup v2.0</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        h2 { color: #667eea; margin-top: 30px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #667eea; background: #f8f9ff; }
        .progress { width: 100%; background: #e9ecef; border-radius: 5px; margin: 10px 0; }
        .progress-bar { height: 20px; background: #667eea; border-radius: 5px; transition: width 0.3s; }
        .btn { background: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #5a67d8; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .version { float: right; color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Webpage Manager Setup <span class="version">v2.0.0</span></h1>
        <p>This setup will initialize the enhanced Webpage Manager with the new database structure and features.</p>

<?php

$setupSteps = [
    'database_connection' => 'Testing Database Connection',
    'database_creation' => 'Creating Database',
    'table_creation' => 'Creating Tables',
    'initial_data' => 'Inserting Initial Data',
    'directory_setup' => 'Setting Up Directories',
    'permissions' => 'Checking Permissions',
    'configuration' => 'Finalizing Configuration'
];

$currentStep = 0;
$totalSteps = count($setupSteps);

function showProgress($current, $total) {
    $percentage = ($current / $total) * 100;
    echo "<div class='progress'><div class='progress-bar' style='width: {$percentage}%'></div></div>";
    echo "<p>Step {$current} of {$total} ({$percentage}%)</p>";
}

function showStep($title, $success = true, $message = '') {
    $class = $success ? 'success' : 'error';
    $icon = $success ? '✓' : '✗';
    echo "<div class='step'>";
    echo "<div class='{$class}'>{$icon} {$title}</div>";
    if ($message) echo "<p>{$message}</p>";
    echo "</div>";
}

try {
    echo "<h2>Database Setup</h2>";
    showProgress(++$currentStep, $totalSteps);

    $database = new Database();

    // Test database connection
    showStep("Testing Database Connection");
    $conn = $database->getConnection();
    if (!$conn) {
        throw new Exception("Cannot connect to database. Please check your database configuration in config/database.php");
    }

    showProgress(++$currentStep, $totalSteps);

    // Create database and tables
    showStep("Creating Database and Tables");
    if ($database->initializeDatabase()) {
        showStep("Database and tables created successfully!", true);
    } else {
        throw new Exception("Failed to create database and tables.");
    }

    showProgress(++$currentStep, $totalSteps);

    // Check database version and structure
    echo "<h2>Database Information</h2>";
    $version = $database->getVersion();
    showStep("Database Version: {$version}", true);

    // Count tables
    $sql = "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'webpage_manager'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    showStep("Database Tables: {$result['table_count']} tables created", true);

    showProgress(++$currentStep, $totalSteps);

    // Check directory permissions
    echo "<h2>Directory Setup</h2>";

    $directories = [
        'uploads/' => 'Main upload directory',
        'uploads/pages/' => 'HTML pages directory',
        'uploads/assets/' => 'Assets directory (CSS, JS, images)',
        'uploads/backups/' => 'Backup files directory'
    ];

    foreach ($directories as $dir => $description) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                showStep("Created directory: {$dir}", true, $description);
            } else {
                showStep("Failed to create directory: {$dir}", false, $description);
            }
        } else {
            showStep("Directory exists: {$dir}", true, $description);
        }

        if (is_writable($dir)) {
            showStep("Directory is writable: {$dir}", true);
        } else {
            showStep("Directory may not be writable: {$dir}", false, "Please check permissions");
        }
    }

    showProgress(++$currentStep, $totalSteps);
    
    // Test database connection
    echo "<h2>Testing Database Connection...</h2>";
    
    $conn = $database->getConnection();
    if ($conn) {
        echo "<p style='color: green;'>✓ Database connection successful!</p>";
        
        // Test table creation
        $sql = "SELECT COUNT(*) as table_count FROM information_schema.tables 
                WHERE table_schema = 'webpage_manager'";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p style='color: green;'>✓ Found {$result['table_count']} tables in database</p>";
    } else {
        echo "<p style='color: red;'>✗ Database connection failed!</p>";
    }
    
    // Check PHP extensions
    echo "<h2>Checking PHP Extensions...</h2>";
    
    $required_extensions = ['pdo', 'pdo_mysql', 'dom', 'libxml', 'json'];
    
    foreach ($required_extensions as $ext) {
        if (extension_loaded($ext)) {
            echo "<p style='color: green;'>✓ Extension loaded: $ext</p>";
        } else {
            echo "<p style='color: red;'>✗ Extension missing: $ext</p>";
        }
    }
    
    // Check file upload settings
    echo "<h2>Checking File Upload Settings...</h2>";
    
    $upload_max = ini_get('upload_max_filesize');
    $post_max = ini_get('post_max_size');
    $max_files = ini_get('max_file_uploads');
    
    echo "<p>Upload max filesize: <strong>$upload_max</strong></p>";
    echo "<p>Post max size: <strong>$post_max</strong></p>";
    echo "<p>Max file uploads: <strong>$max_files</strong></p>";
    
    if (ini_get('file_uploads')) {
        echo "<p style='color: green;'>✓ File uploads are enabled</p>";
    } else {
        echo "<p style='color: red;'>✗ File uploads are disabled</p>";
    }
    
    echo "<h2>Setup Complete!</h2>";
    echo "<p style='color: green; font-size: 18px;'>✓ Webpage Manager is ready to use!</p>";
    echo "<p><a href='index.html' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Launch Application</a></p>";
    
    echo "<h3>Test Files Available:</h3>";
    echo "<ul>";
    echo "<li><a href='test_samples/sample_form.html' target='_blank'>Sample Contact Form</a> - Complex form with various input types</li>";
    echo "<li><a href='test_samples/simple_login.html' target='_blank'>Simple Login Form</a> - Basic login form with CSS</li>";
    echo "</ul>";
    
    showProgress($totalSteps, $totalSteps);

    echo "<h2>Setup Complete! 🎉</h2>";
    showStep("Webpage Manager v2.0 is ready to use!", true);

    echo "<div class='info'>";
    echo "<h3>🚀 Quick Start Guide:</h3>";
    echo "<ol>";
    echo "<li><strong>Access the Application:</strong> <a href='index.html' class='btn'>Launch Webpage Manager</a></li>";
    echo "<li><strong>Import Pages:</strong> Go to 'Import Pages' tab and upload HTML files</li>";
    echo "<li><strong>Edit Forms:</strong> Use 'Manage Pages' tab to view and edit form attributes</li>";
    echo "<li><strong>Generate Database:</strong> Use 'Database' tab to create tables from forms</li>";
    echo "</ol>";
    echo "</div>";

    echo "<div class='info'>";
    echo "<h3>📋 Test Files Available:</h3>";
    echo "<ul>";
    echo "<li><a href='test_samples/sample_form.html' target='_blank'>Sample Contact Form</a> - Complex form with various input types</li>";
    echo "<li><a href='test_samples/simple_login.html' target='_blank'>Simple Login Form</a> - Basic login form with CSS</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='info'>";
    echo "<h3>🔧 New Features in v2.0:</h3>";
    echo "<ul>";
    echo "<li>Enhanced database structure with 14 tables</li>";
    echo "<li>Project organization for better page management</li>";
    echo "<li>Form templates for quick form creation</li>";
    echo "<li>Version control for page modifications</li>";
    echo "<li>Activity logging for audit trails</li>";
    echo "<li>Form submission storage capabilities</li>";
    echo "<li>Advanced form editing with all HTML5 input types</li>";
    echo "<li>Improved analysis and reporting</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='info'>";
    echo "<h3>📚 Documentation:</h3>";
    echo "<ul>";
    echo "<li><a href='README.md' target='_blank'>README.md</a> - Complete documentation</li>";
    echo "<li><a href='FORM_EDITING_GUIDE.md' target='_blank'>Form Editing Guide</a> - How to edit forms</li>";
    echo "<li><a href='database_setup.sql' target='_blank'>Database Schema</a> - Complete database structure</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    showStep("Setup Failed", false, $e->getMessage());
    echo "<div class='error'>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Check your database credentials in <code>config/database.php</code></li>";
    echo "<li>Ensure MySQL service is running</li>";
    echo "<li>Verify PHP has write permissions to the uploads directory</li>";
    echo "<li>Check PHP error logs for detailed error information</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>"; // Close container
echo "</body></html>";
?>
