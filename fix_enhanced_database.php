<?php
/**
 * Fix Enhanced Database
 * Add missing columns to existing tables for enhanced functionality
 */

require_once 'config/database.php';

echo "<h1>Fix Enhanced Database Structure</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Check current form_submissions structure
    echo "<h2>Analyzing Current form_submissions Table</h2>";
    
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $currentColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    
    echo "<p><strong>Current columns:</strong> " . implode(', ', $currentColumns) . "</p>";
    
    // Define the enhanced columns we need to add
    $enhancedColumns = [
        'form_name' => "VARCHAR(255) AFTER share_id",
        'visitor_session' => "VARCHAR(64) AFTER submission_data",
        'country_code' => "VARCHAR(2) AFTER referrer",
        'city' => "VARCHAR(100) AFTER country_code",
        'browser_name' => "VARCHAR(50) AFTER city",
        'browser_version' => "VARCHAR(20) AFTER browser_name",
        'os_name' => "VARCHAR(50) AFTER browser_version",
        'device_type' => "ENUM('desktop', 'mobile', 'tablet') DEFAULT 'desktop' AFTER os_name",
        'submission_source' => "ENUM('direct', 'shared', 'embedded') DEFAULT 'shared' AFTER device_type",
        'status' => "ENUM('pending', 'processed', 'archived', 'spam', 'reviewed') DEFAULT 'pending' AFTER submission_source",
        'priority' => "ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' AFTER status",
        'tags' => "JSON AFTER priority",
        'notes' => "TEXT AFTER tags",
        'processed_at' => "TIMESTAMP NULL AFTER notes",
        'processed_by' => "INT NULL AFTER processed_at",
        'response_sent' => "BOOLEAN DEFAULT FALSE AFTER processed_by",
        'response_sent_at' => "TIMESTAMP NULL AFTER response_sent"
    ];
    
    echo "<h2>Adding Missing Columns</h2>";
    
    $addedColumns = [];
    $skippedColumns = [];
    
    foreach ($enhancedColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $currentColumns)) {
            try {
                $sql = "ALTER TABLE form_submissions ADD COLUMN $columnName $columnDefinition";
                $db->exec($sql);
                $addedColumns[] = $columnName;
                echo "<p>✅ Added column: $columnName</p>";
            } catch (Exception $e) {
                echo "<p>❌ Failed to add column $columnName: " . $e->getMessage() . "</p>";
            }
        } else {
            $skippedColumns[] = $columnName;
        }
    }
    
    if (!empty($addedColumns)) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Successfully Added Columns:</h4>";
        echo "<ul>";
        foreach ($addedColumns as $col) {
            echo "<li>$col</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($skippedColumns)) {
        echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Columns Already Exist:</h4>";
        echo "<ul>";
        foreach ($skippedColumns as $col) {
            echo "<li>$col</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Add indexes for performance
    echo "<h2>Adding Performance Indexes</h2>";
    
    $indexes = [
        'idx_form_submissions_status' => 'status',
        'idx_form_submissions_priority' => 'priority',
        'idx_form_submissions_submission_source' => 'submission_source',
        'idx_form_submissions_device_type' => 'device_type',
        'idx_form_submissions_browser_name' => 'browser_name',
        'idx_form_submissions_visitor_session' => 'visitor_session'
    ];
    
    foreach ($indexes as $indexName => $columnName) {
        if (in_array($columnName, $currentColumns) || in_array($columnName, $addedColumns)) {
            try {
                $sql = "ALTER TABLE form_submissions ADD INDEX $indexName ($columnName)";
                $db->exec($sql);
                echo "<p>✅ Added index: $indexName on $columnName</p>";
            } catch (Exception $e) {
                // Index might already exist, that's okay
                echo "<p>⚠️ Index $indexName: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Create zip_extractions table if it doesn't exist
    echo "<h2>Creating zip_extractions Table</h2>";
    
    try {
        $sql = "CREATE TABLE IF NOT EXISTS zip_extractions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_filename VARCHAR(255) NOT NULL,
            extracted_path VARCHAR(500) NOT NULL,
            total_files INT DEFAULT 0,
            html_files_found INT DEFAULT 0,
            assets_found INT DEFAULT 0,
            index_page_id INT NULL,
            index_page_path VARCHAR(500),
            extraction_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            error_message TEXT,
            file_structure JSON,
            extraction_time DECIMAL(10,4),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP NULL,
            FOREIGN KEY (index_page_id) REFERENCES pages(id) ON DELETE SET NULL,
            INDEX idx_original_filename (original_filename),
            INDEX idx_extraction_status (extraction_status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "<p>✅ zip_extractions table created/verified</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Failed to create zip_extractions table: " . $e->getMessage() . "</p>";
    }
    
    // Update app_settings with enhanced defaults
    echo "<h2>Updating App Settings</h2>";
    
    $enhancedSettings = [
        ['enable_form_submissions', '1', 'boolean', 'Enable form submission storage'],
        ['auto_detect_index_pages', '1', 'boolean', 'Automatically detect and prioritize index pages in ZIP files'],
        ['form_submission_notifications', '1', 'boolean', 'Send notifications for new form submissions'],
        ['online_deployment_mode', '0', 'boolean', 'Enable features for online deployment'],
        ['base_url', '', 'string', 'Base URL for the application (for online deployment)'],
        ['share_url_domain', '', 'string', 'Custom domain for share URLs']
    ];
    
    foreach ($enhancedSettings as $setting) {
        try {
            $sql = "INSERT INTO app_settings (setting_key, setting_value, setting_type, description) 
                    VALUES (?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    setting_value = VALUES(setting_value),
                    setting_type = VALUES(setting_type),
                    description = VALUES(description)";
            $stmt = $db->prepare($sql);
            $stmt->execute($setting);
            echo "<p>✅ Updated setting: {$setting[0]}</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ Setting {$setting[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    // Verify the final structure
    echo "<h2>Final Table Structure Verification</h2>";
    
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $finalColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Status</th></tr>";
    
    $finalColumnNames = array_column($finalColumns, 'Field');
    
    foreach ($finalColumns as $col) {
        $isNew = in_array($col['Field'], $addedColumns);
        $statusIcon = $isNew ? '🆕' : '✅';
        $rowStyle = $isNew ? 'style="background-color: #d4edda;"' : '';
        
        echo "<tr $rowStyle>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>$statusIcon</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test enhanced insertion
    echo "<h2>Testing Enhanced Form Submission</h2>";
    
    try {
        $testData = [
            'page_id' => 1,
            'form_name' => 'enhanced_test_form',
            'submission_data' => json_encode(['name' => 'Test User', 'email' => '<EMAIL>']),
            'visitor_session' => hash('sha256', '127.0.0.1' . 'test-agent' . date('Y-m-d')),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Enhanced Test Agent',
            'referrer' => 'http://test.com',
            'browser_name' => 'Chrome',
            'browser_version' => '91.0',
            'os_name' => 'Windows',
            'device_type' => 'desktop',
            'submission_source' => 'shared',
            'status' => 'pending',
            'priority' => 'normal'
        ];
        
        // Build dynamic query based on available columns
        $insertColumns = [];
        $insertValues = [];
        $insertData = [];
        
        foreach ($testData as $column => $value) {
            if (in_array($column, $finalColumnNames)) {
                $insertColumns[] = $column;
                $insertValues[] = '?';
                $insertData[] = $value;
            }
        }
        
        $sql = "INSERT INTO form_submissions (" . implode(', ', $insertColumns) . ", submitted_at) 
                VALUES (" . implode(', ', $insertValues) . ", NOW())";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($insertData);
        
        $testId = $db->lastInsertId();
        echo "<p>✅ Enhanced form submission test successful! ID: $testId</p>";
        
        // Show the inserted data
        $sql = "SELECT * FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testId]);
        $inserted = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Test Submission Data:</h4>";
        foreach ($inserted as $key => $value) {
            if ($value !== null && $value !== '') {
                echo "<p><strong>$key:</strong> $value</p>";
            }
        }
        echo "</div>";
        
        // Clean up test record
        $sql = "DELETE FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testId]);
        echo "<p>✅ Test record cleaned up</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Enhanced submission test failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 Database Enhancement Complete!</h2>";
    echo "<p>The database has been successfully enhanced with:</p>";
    echo "<ul>";
    echo "<li>Enhanced form_submissions table with visitor analytics</li>";
    echo "<li>ZIP extractions tracking table</li>";
    echo "<li>Performance indexes for better query speed</li>";
    echo "<li>Updated application settings</li>";
    echo "</ul>";
    echo "<p>You can now use the enhanced form submission system!</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Enhancement Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Test Enhanced System:</strong> <a href='test_enhanced_system.php'>Run Enhanced System Test</a></li>";
echo "<li><strong>Test Form Submissions:</strong> <a href='enhanced_submit_form.php'>Enhanced Submit Handler</a></li>";
echo "<li><strong>Upload ZIP Files:</strong> Test automatic index page detection</li>";
echo "<li><strong>Main Application:</strong> <a href='index.html'>Use the main app</a></li>";
echo "</ol>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h1, h2, h3, h4 { color: #333; }
</style>
