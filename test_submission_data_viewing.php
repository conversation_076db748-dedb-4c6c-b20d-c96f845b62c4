<?php
/**
 * Test Submission Data Viewing
 * Verify that submitted form data displays correctly instead of "invalid data"
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>Test Submission Data Viewing</h1>";

echo "<h2>1. Issue Analysis</h2>";

echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
echo "<h3>❌ Original Problem</h3>";
echo "<p>Submitted form data was displaying as 'Invalid data' in the submissions view.</p>";

echo "<h4>Root Causes Identified:</h4>";
echo "<ul>";
echo "<li><strong>Column Name Mismatch:</strong> JavaScript expected 'form_data' but database had 'submission_data'</li>";
echo "<li><strong>JSON Parsing Issues:</strong> Data format inconsistencies causing parse errors</li>";
echo "<li><strong>Null/Empty Data:</strong> Poor handling of empty or null form data</li>";
echo "<li><strong>Error Handling:</strong> Generic 'Invalid data' message without debugging info</li>";
echo "</ul>";
echo "</div>";

echo "<h2>2. Database Schema Check</h2>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check form_submissions table structure
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #e7f3ff; color: #004085; padding: 20px; border-radius: 8px;'>";
    echo "<h3>🔍 Current Database Schema</h3>";
    
    echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f2f2f2;'><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $dataColumns = [];
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
        
        if (in_array($column['Field'], ['form_data', 'submission_data'])) {
            $dataColumns[] = $column['Field'];
        }
    }
    echo "</table>";
    
    echo "<h4>Data Column Detection:</h4>";
    if (empty($dataColumns)) {
        echo "<p style='color: red;'>❌ No data column found (form_data or submission_data)</p>";
    } else {
        echo "<p style='color: green;'>✅ Found data columns: " . implode(', ', $dataColumns) . "</p>";
        
        // Determine which column to use
        $dataColumn = 'submission_data';
        if (in_array('form_data', $dataColumns) && !in_array('submission_data', $dataColumns)) {
            $dataColumn = 'form_data';
        }
        echo "<p><strong>Using column:</strong> <code>$dataColumn</code></p>";
    }
    echo "</div>";
    
    echo "<h2>3. Test Data Retrieval</h2>";
    
    // Get some sample submissions
    $sql = "SELECT * FROM form_submissions ORDER BY submitted_at DESC LIMIT 5";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($submissions)) {
        echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px;'>";
        echo "<h3>⚠️ No Submissions Found</h3>";
        echo "<p>No form submissions found in the database. Submit a test form first.</p>";
        echo "</div>";
        
        // Create a test submission
        echo "<h3>Creating Test Submission</h3>";
        $testData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'This is a test submission for data viewing verification.'
        ];
        
        $sql = "INSERT INTO form_submissions (page_id, $dataColumn, ip_address, submitted_at) VALUES (?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([1, json_encode($testData), '127.0.0.1']);
        
        $testId = $db->lastInsertId();
        echo "<p style='color: green;'>✅ Test submission created with ID: $testId</p>";
        
        // Retrieve the test submission
        $sql = "SELECT * FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testId]);
        $submissions = [$stmt->fetch(PDO::FETCH_ASSOC)];
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
    echo "<h3>✅ Sample Submissions Analysis</h3>";
    
    foreach ($submissions as $index => $submission) {
        echo "<h4>Submission " . ($index + 1) . " (ID: {$submission['id']})</h4>";
        
        // Get the data from the correct column
        $formDataRaw = $submission[$dataColumn] ?? $submission['form_data'] ?? $submission['submission_data'] ?? null;
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Raw Data:</strong></p>";
        echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
        echo htmlspecialchars($formDataRaw ?: 'NULL');
        echo "</pre>";
        
        // Test JSON parsing
        echo "<p><strong>JSON Parse Test:</strong></p>";
        try {
            if ($formDataRaw) {
                $parsedData = json_decode($formDataRaw, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    echo "<p style='color: green;'>✅ Valid JSON</p>";
                    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
                    echo htmlspecialchars(json_encode($parsedData, JSON_PRETTY_PRINT));
                    echo "</pre>";
                    
                    // Test preview formatting
                    echo "<p><strong>Preview Format Test:</strong></p>";
                    if (!empty($parsedData) && is_array($parsedData)) {
                        $fields = array_keys($parsedData);
                        $preview = array_slice($fields, 0, 3);
                        $previewText = implode(', ', array_map(function($field) use ($parsedData) {
                            return "$field: " . $parsedData[$field];
                        }, $preview));
                        echo "<p style='color: green;'>✅ Preview: " . htmlspecialchars($previewText) . "</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ Empty or invalid data structure</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Invalid JSON: " . json_last_error_msg() . "</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No data found</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>4. Test API Endpoint</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
    echo "<h3>🧪 Testing get_form_submissions.php</h3>";
    
    // Test the API endpoint
    $testUrl = 'includes/get_form_submissions.php';
    
    echo "<h4>API Response Test:</h4>";
    echo "<button onclick='testAPI()' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test API Endpoint</button>";
    echo "<div id='apiResult' style='margin: 15px 0;'></div>";
    echo "</div>";
    
    echo "<h2>5. Solutions Implemented</h2>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
    echo "<h3>✅ Fixes Applied</h3>";
    
    echo "<h4>1. Dynamic Column Detection</h4>";
    echo "<ul>";
    echo "<li><strong>get_form_submissions.php:</strong> Detects whether to use 'form_data' or 'submission_data'</li>";
    echo "<li><strong>Alias Mapping:</strong> Maps detected column to 'form_data' alias for consistency</li>";
    echo "<li><strong>Backward Compatibility:</strong> Works with both old and new database schemas</li>";
    echo "</ul>";
    
    echo "<h4>2. Enhanced JavaScript Parsing</h4>";
    echo "<ul>";
    echo "<li><strong>Null Handling:</strong> Properly handles null, undefined, and empty values</li>";
    echo "<li><strong>Object Detection:</strong> Handles both JSON strings and objects</li>";
    echo "<li><strong>Error Recovery:</strong> Graceful fallback for parsing errors</li>";
    echo "<li><strong>Debug Logging:</strong> Console logging for troubleshooting</li>";
    echo "</ul>";
    
    echo "<h4>3. Improved Error Messages</h4>";
    echo "<ul>";
    echo "<li><strong>Specific Messages:</strong> 'No data', 'Empty form', 'Invalid data format'</li>";
    echo "<li><strong>Debug Information:</strong> Console logs for developers</li>";
    echo "<li><strong>User-Friendly:</strong> Clear indication of data status</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>6. Test Form Submission</h2>";

echo "<div style='background: #e7f3ff; color: #004085; padding: 20px; border-radius: 8px;'>";
echo "<h3>🧪 Submit Test Data</h3>";
echo "<p>Submit this form to create test data and verify the viewing functionality:</p>";

echo "<form id='testDataForm' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;'>";
echo "<input type='hidden' name='_page_id' value='1'>";
echo "<input type='hidden' name='_form_name' value='data_viewing_test'>";

echo "<div style='margin: 15px 0;'>";
echo "<label for='test_name'>Name:</label>";
echo "<input type='text' id='test_name' name='name' value='Data Viewing Test User' style='width: 100%; padding: 8px; margin-top: 5px;'>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label for='test_email'>Email:</label>";
echo "<input type='email' id='test_email' name='email' value='<EMAIL>' style='width: 100%; padding: 8px; margin-top: 5px;'>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label for='test_message'>Message:</label>";
echo "<textarea id='test_message' name='message' rows='3' style='width: 100%; padding: 8px; margin-top: 5px;'>This is a test submission to verify that form data displays correctly in the submissions view.</textarea>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label for='test_rating'>Rating:</label>";
echo "<select id='test_rating' name='rating' style='width: 100%; padding: 8px; margin-top: 5px;'>";
echo "<option value='excellent'>Excellent</option>";
echo "<option value='good'>Good</option>";
echo "<option value='average'>Average</option>";
echo "<option value='poor'>Poor</option>";
echo "</select>";
echo "</div>";

echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Submit Test Data</button>";
echo "</form>";

echo "<div id='testResult' style='margin: 15px 0;'></div>";
echo "</div>";

echo "<h2>Quick Action Links</h2>";
echo "<div style='display: flex; gap: 15px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='index.html#database' target='_blank' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>View Database Tab</a>";
echo "<a href='enhanced_data_collection.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Data Collection Manager</a>";
echo "<a href='test_form_data_fix.php' target='_blank' style='background: #6f42c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Test Form Fixes</a>";
echo "</div>";

echo "<h2>Summary</h2>";
echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🎉 Data Viewing Issues Fixed!</h3>";
echo "<ul>";
echo "<li>✅ Dynamic column detection handles both 'form_data' and 'submission_data'</li>";
echo "<li>✅ Enhanced JavaScript parsing handles various data formats</li>";
echo "<li>✅ Improved error handling with specific, helpful messages</li>";
echo "<li>✅ Better null and empty data handling</li>";
echo "<li>✅ Debug logging for troubleshooting</li>";
echo "<li>✅ Backward compatibility with existing data</li>";
echo "</ul>";
echo "<p><strong>Submitted form data should now display correctly instead of 'Invalid data'!</strong></p>";
echo "</div>";

?>

<script>
// Test API endpoint
async function testAPI() {
    const resultDiv = document.getElementById('apiResult');
    resultDiv.innerHTML = '<p style="color: #007cba;">Testing API endpoint...</p>';
    
    try {
        const response = await fetch('includes/get_form_submissions.php');
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = `
                <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;">
                    <h4>✅ API Test Successful</h4>
                    <p><strong>Total Submissions:</strong> ${data.total}</p>
                    <p><strong>Sample Data:</strong></p>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;">${JSON.stringify(data.submissions.slice(0, 2), null, 2)}</pre>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                    <h4>❌ API Test Failed</h4>
                    <p>${data.message}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('API test error:', error);
        resultDiv.innerHTML = `
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                <h4>❌ API Test Error</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

// Test form submission
document.getElementById('testDataForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const resultDiv = document.getElementById('testResult');
    
    resultDiv.innerHTML = '<p style="color: #007cba;">Submitting test data...</p>';
    
    fetch('enhanced_submit_form.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;">
                    <h4>✅ Test Submission Successful</h4>
                    <p>${data.message}</p>
                    <p><strong>Submission ID:</strong> ${data.submission_id}</p>
                    <p>Now check the Database tab in the main application to see if the data displays correctly.</p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                    <h4>❌ Test Submission Failed</h4>
                    <p>${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Test submission error:', error);
        resultDiv.innerHTML = `
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                <h4>❌ Test Submission Error</h4>
                <p>${error.message}</p>
            </div>
        `;
    });
});
</script>

<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: 'Courier New', monospace; }
input, textarea, select { box-sizing: border-box; }
</style>
