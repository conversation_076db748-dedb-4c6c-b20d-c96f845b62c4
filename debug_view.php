<?php
/**
 * Debug View Script
 * Test the view.php functionality with detailed error reporting
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>View.php Debug Test</h1>";

// Test with a sample token
$testToken = $_GET['token'] ?? 'test_token_123';
$testShortCode = $_GET['s'] ?? null;
$testPageId = $_GET['id'] ?? null;

echo "<h2>Request Parameters</h2>";
echo "<p><strong>Token:</strong> " . htmlspecialchars($testToken) . "</p>";
echo "<p><strong>Short Code:</strong> " . htmlspecialchars($testShortCode ?: 'None') . "</p>";
echo "<p><strong>Page ID:</strong> " . htmlspecialchars($testPageId ?: 'None') . "</p>";

echo "<h2>Server Information</h2>";
echo "<p><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";

try {
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Test share lookup
    echo "<h2>Testing Share Lookup</h2>";
    
    if ($testToken) {
        $sql = "SELECT ps.*, p.* FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testToken]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            echo "<p>✅ Share found!</p>";
            echo "<pre>" . print_r($result, true) . "</pre>";
            
            // Test file existence
            echo "<h2>Testing File Access</h2>";
            echo "<p><strong>File Path:</strong> {$result['file_path']}</p>";
            echo "<p><strong>File Exists:</strong> " . (file_exists($result['file_path']) ? 'Yes' : 'No') . "</p>";
            
            if (file_exists($result['file_path'])) {
                $content = file_get_contents($result['file_path']);
                echo "<p><strong>File Size:</strong> " . strlen($content) . " bytes</p>";
                echo "<p><strong>Content Preview:</strong></p>";
                echo "<textarea rows='10' cols='80'>" . htmlspecialchars(substr($content, 0, 500)) . "</textarea>";
            }
            
        } else {
            echo "<p>❌ No share found with token: $testToken</p>";
            
            // Show available shares
            $sql = "SELECT share_token, title, page_id FROM page_shares WHERE is_active = 1 LIMIT 5";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $shares = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Available Shares:</h3>";
            if (count($shares) > 0) {
                foreach ($shares as $share) {
                    $debugUrl = "debug_view.php?token=" . $share['share_token'];
                    echo "<p>• <a href='$debugUrl'>{$share['title']} (Token: {$share['share_token']})</a></p>";
                }
            } else {
                echo "<p>No shares available</p>";
            }
        }
    }
    
    // Test view.php directly
    echo "<h2>Testing view.php Include</h2>";
    
    if ($testToken && $result) {
        echo "<p>Attempting to include view.php with token...</p>";
        
        // Set up environment for view.php
        $_GET['token'] = $testToken;
        
        ob_start();
        try {
            // Don't include view.php directly as it will output and exit
            echo "<p>✅ Would redirect to: <a href='view.php?token=$testToken' target='_blank'>view.php?token=$testToken</a></p>";
        } catch (Exception $e) {
            echo "<p>❌ Error including view.php: " . $e->getMessage() . "</p>";
        }
        $output = ob_get_clean();
        echo $output;
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Quick Actions</h2>";
echo "<p><a href='test_sharing.php'>Run Sharing Test</a></p>";
echo "<p><a href='index.html'>Back to Main App</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre, textarea { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>
