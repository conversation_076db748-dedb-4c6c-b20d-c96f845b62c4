<?php
require_once '../config/database.php';

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid page ID']);
    exit;
}

$pageId = (int)$_GET['id'];

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get page details
    $sql = "SELECT * FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        http_response_code(404);
        echo json_encode(['error' => 'Page not found']);
        exit;
    }

    // Get forms for this page
    $sql = "SELECT * FROM forms WHERE page_id = ? ORDER BY form_index";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get fields for each form
    foreach ($forms as &$form) {
        $sql = "SELECT * FROM form_fields WHERE form_id = ? ORDER BY id";
        $stmt = $db->prepare($sql);
        $stmt->execute([$form['id']]);
        $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Parse JSON options if present
        foreach ($fields as &$field) {
            if ($field['field_options']) {
                $field['field_options'] = json_decode($field['field_options'], true);
            }
        }
        
        $form['fields'] = $fields;
    }

    // Get associated files
    $sql = "SELECT * FROM associated_files WHERE page_id = ? ORDER BY file_type, filename";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $associatedFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get analysis log
    $sql = "SELECT * FROM analysis_log WHERE page_id = ? ORDER BY created_at DESC LIMIT 10";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $analysisLog = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $response = [
        'id' => $page['id'],
        'filename' => $page['filename'],
        'original_filename' => $page['original_filename'],
        'title' => $page['title'],
        'file_size' => $page['file_size'],
        'created_at' => $page['created_at'],
        'updated_at' => $page['updated_at'],
        'forms' => $forms,
        'associated_files' => $associatedFiles,
        'analysis_log' => $analysisLog
    ];

    echo json_encode($response);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
