<?php
/**
 * Enhanced Data Collection System
 * Ensures data collection works with any imported HTML or PHP page
 */

require_once 'config/database.php';
require_once 'includes/html_parser.php';

class EnhancedDataCollector {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Analyze and enhance a page for data collection
     */
    public function enhancePageForDataCollection($pageId) {
        try {
            // Get page information
            $sql = "SELECT * FROM pages WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pageId]);
            $page = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$page) {
                throw new Exception("Page not found");
            }
            
            $filePath = $page['file_path'];
            if (!file_exists($filePath)) {
                throw new Exception("Page file not found: $filePath");
            }
            
            // Analyze forms in the page
            $this->analyzeFormsInPage($pageId, $filePath);
            
            // Create enhanced version for data collection
            $this->createDataCollectionVersion($pageId, $filePath);
            
            // Update page metadata
            $this->updatePageMetadata($pageId);
            
            return [
                'success' => true,
                'message' => 'Page enhanced for data collection',
                'page_id' => $pageId
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to enhance page: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Analyze forms in the page using enhanced detection
     */
    private function analyzeFormsInPage($pageId, $filePath) {
        $parser = new HTMLParser();
        $forms = $parser->extractForms($filePath);
        
        // Clear existing forms for this page
        $sql = "DELETE FROM forms WHERE page_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        
        // Clear existing form fields
        $sql = "DELETE FROM form_fields WHERE page_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        
        foreach ($forms as $index => $formData) {
            // Insert form record
            $sql = "INSERT INTO forms (
                        page_id, form_name, form_action, form_method, form_enctype, 
                        form_id, form_class, form_target, form_autocomplete, 
                        form_novalidate, form_index, form_description, is_active,
                        validation_rules, custom_attributes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId,
                $formData['name'] ?? "form_$index",
                $formData['action'] ?? '',
                $formData['method'] ?? 'POST',
                $formData['enctype'] ?? 'application/x-www-form-urlencoded',
                $formData['id'] ?? "form_$index",
                $formData['class'] ?? '',
                $formData['target'] ?? null,
                $formData['autocomplete'] ?? 'on',
                $formData['novalidate'] ?? false,
                $index,
                $formData['description'] ?? "Form $index",
                json_encode($formData['validation_rules'] ?? []),
                json_encode($formData['custom_attributes'] ?? [])
            ]);
            
            $formId = $this->db->lastInsertId();
            
            // Insert form fields
            if (isset($formData['fields']) && is_array($formData['fields'])) {
                foreach ($formData['fields'] as $fieldIndex => $field) {
                    $sql = "INSERT INTO form_fields (
                                form_id, page_id, field_name, field_type, field_label,
                                field_placeholder, field_value, field_required,
                                field_readonly, field_disabled, field_multiple,
                                field_min, field_max, field_step, field_pattern,
                                field_options, field_attributes, field_index,
                                validation_rules, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";
                    
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([
                        $formId,
                        $pageId,
                        $field['name'] ?? "field_$fieldIndex",
                        $field['type'] ?? 'text',
                        $field['label'] ?? '',
                        $field['placeholder'] ?? '',
                        $field['value'] ?? '',
                        $field['required'] ?? false,
                        $field['readonly'] ?? false,
                        $field['disabled'] ?? false,
                        $field['multiple'] ?? false,
                        $field['min'] ?? null,
                        $field['max'] ?? null,
                        $field['step'] ?? null,
                        $field['pattern'] ?? null,
                        json_encode($field['options'] ?? []),
                        json_encode($field['attributes'] ?? []),
                        $fieldIndex,
                        json_encode($field['validation_rules'] ?? [])
                    ]);
                }
            }
        }
        
        // Update page to indicate it has forms
        if (count($forms) > 0) {
            $sql = "UPDATE pages SET has_forms = 1, last_analyzed = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pageId]);
        }
        
        return count($forms);
    }
    
    /**
     * Create an enhanced version of the page for data collection
     */
    private function createDataCollectionVersion($pageId, $filePath) {
        $content = file_get_contents($filePath);
        
        // Create DOM document
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();
        
        // Enhance all forms for data collection
        $this->enhanceFormsInDOM($dom, $pageId);
        
        // Add data collection JavaScript
        $this->addDataCollectionScript($dom, $pageId);
        
        // Save enhanced version
        $enhancedContent = $dom->saveHTML();
        $enhancedPath = str_replace('.html', '_enhanced.html', $filePath);
        $enhancedPath = str_replace('.php', '_enhanced.php', $enhancedPath);
        
        file_put_contents($enhancedPath, $enhancedContent);
        
        // Update page record with enhanced path
        $sql = "UPDATE pages SET enhanced_path = ? WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$enhancedPath, $pageId]);
        
        return $enhancedPath;
    }
    
    /**
     * Enhance forms in DOM for data collection
     */
    private function enhanceFormsInDOM($dom, $pageId) {
        $xpath = new DOMXPath($dom);
        
        // Find all forms
        $forms = $xpath->query('//form');
        
        foreach ($forms as $index => $form) {
            // Set form action to our data collection handler
            $form->setAttribute('action', 'enhanced_submit_form.php');
            $form->setAttribute('method', 'POST');
            
            // Add hidden fields for tracking
            $hiddenFields = [
                '_page_id' => $pageId,
                '_form_index' => $index,
                '_form_name' => $form->getAttribute('name') ?: "form_$index",
                '_original_action' => $form->getAttribute('action') ?: ''
            ];
            
            foreach ($hiddenFields as $name => $value) {
                $hidden = $dom->createElement('input');
                $hidden->setAttribute('type', 'hidden');
                $hidden->setAttribute('name', $name);
                $hidden->setAttribute('value', $value);
                $form->insertBefore($hidden, $form->firstChild);
            }
        }
        
        // Find standalone input elements (not in forms)
        $standaloneInputs = $xpath->query('//input[not(ancestor::form)] | //textarea[not(ancestor::form)] | //select[not(ancestor::form)]');
        
        if ($standaloneInputs->length > 0) {
            // Wrap standalone inputs in a form
            $wrapperForm = $dom->createElement('form');
            $wrapperForm->setAttribute('action', 'enhanced_submit_form.php');
            $wrapperForm->setAttribute('method', 'POST');
            $wrapperForm->setAttribute('style', 'display: contents;');
            
            // Add hidden fields
            $hiddenFields = [
                '_page_id' => $pageId,
                '_form_index' => 999,
                '_form_name' => 'standalone_inputs',
                '_original_action' => ''
            ];
            
            foreach ($hiddenFields as $name => $value) {
                $hidden = $dom->createElement('input');
                $hidden->setAttribute('type', 'hidden');
                $hidden->setAttribute('name', $name);
                $hidden->setAttribute('value', $value);
                $wrapperForm->appendChild($hidden);
            }
            
            // Move standalone inputs into the wrapper form
            foreach ($standaloneInputs as $input) {
                $input->parentNode->insertBefore($wrapperForm, $input);
                $wrapperForm->appendChild($input);
            }
        }
    }
    
    /**
     * Add data collection JavaScript to the page
     */
    private function addDataCollectionScript($dom, $pageId) {
        $script = $dom->createElement('script');
        $script->textContent = "
            document.addEventListener('DOMContentLoaded', function() {
                // Enhanced form submission handling
                const forms = document.querySelectorAll('form[action=\"enhanced_submit_form.php\"]');
                
                forms.forEach(function(form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        const formData = new FormData(form);
                        
                        // Show loading state
                        const submitBtn = form.querySelector('input[type=\"submit\"], button[type=\"submit\"]');
                        const originalText = submitBtn ? submitBtn.textContent : '';
                        if (submitBtn) {
                            submitBtn.textContent = 'Submitting...';
                            submitBtn.disabled = true;
                        }
                        
                        fetch('enhanced_submit_form.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Show success message
                                alert(data.message || 'Form submitted successfully!');
                                
                                // Reset form
                                form.reset();
                                
                                // Redirect if specified
                                if (data.redirect_url) {
                                    window.location.href = data.redirect_url;
                                }
                            } else {
                                alert('Error: ' + (data.message || 'Form submission failed'));
                            }
                        })
                        .catch(error => {
                            console.error('Form submission error:', error);
                            alert('An error occurred while submitting the form. Please try again.');
                        })
                        .finally(() => {
                            // Restore button state
                            if (submitBtn) {
                                submitBtn.textContent = originalText;
                                submitBtn.disabled = false;
                            }
                        });
                    });
                });
                
                // Track input interactions
                const inputs = document.querySelectorAll('input, textarea, select');
                inputs.forEach(function(input) {
                    input.addEventListener('change', function() {
                        // Optional: Track field interactions
                        console.log('Field interaction:', input.name, input.value);
                    });
                });
            });
        ";
        
        // Add script to head or body
        $head = $dom->getElementsByTagName('head')->item(0);
        if ($head) {
            $head->appendChild($script);
        } else {
            $body = $dom->getElementsByTagName('body')->item(0);
            if ($body) {
                $body->appendChild($script);
            }
        }
    }
    
    /**
     * Update page metadata
     */
    private function updatePageMetadata($pageId) {
        $sql = "UPDATE pages SET 
                    data_collection_enabled = 1,
                    last_analyzed = NOW(),
                    updated_at = NOW()
                WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
    }
    
    /**
     * Get collected data for a page
     */
    public function getCollectedData($pageId, $limit = 100, $offset = 0) {
        $sql = "SELECT fs.*, f.form_name as form_title, p.title as page_title
                FROM form_submissions fs
                LEFT JOIN forms f ON fs.form_id = f.id
                LEFT JOIN pages p ON fs.page_id = p.id
                WHERE fs.page_id = ?
                ORDER BY fs.submitted_at DESC
                LIMIT ? OFFSET ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId, $limit, $offset]);
        $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON data
        foreach ($submissions as &$submission) {
            $submission['form_data'] = json_decode($submission['submission_data'], true);
        }
        
        return $submissions;
    }
    
    /**
     * Get data collection statistics
     */
    public function getDataCollectionStats($pageId) {
        $stats = [];
        
        // Total submissions
        $sql = "SELECT COUNT(*) as total FROM form_submissions WHERE page_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        $stats['total_submissions'] = $stmt->fetchColumn();
        
        // Submissions by form
        $sql = "SELECT f.form_name, COUNT(*) as count 
                FROM form_submissions fs
                LEFT JOIN forms f ON fs.form_id = f.id
                WHERE fs.page_id = ?
                GROUP BY fs.form_id, f.form_name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        $stats['by_form'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Recent submissions
        $sql = "SELECT DATE(submitted_at) as date, COUNT(*) as count
                FROM form_submissions
                WHERE page_id = ? AND submitted_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(submitted_at)
                ORDER BY date DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$pageId]);
        $stats['recent_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $stats;
    }
}

// Handle API requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        $collector = new EnhancedDataCollector();
        
        switch ($action) {
            case 'enhance_page':
                $pageId = $_POST['page_id'] ?? 0;
                $result = $collector->enhancePageForDataCollection($pageId);
                break;
                
            case 'get_data':
                $pageId = $_POST['page_id'] ?? 0;
                $limit = $_POST['limit'] ?? 100;
                $offset = $_POST['offset'] ?? 0;
                $result = [
                    'success' => true,
                    'data' => $collector->getCollectedData($pageId, $limit, $offset)
                ];
                break;
                
            case 'get_stats':
                $pageId = $_POST['page_id'] ?? 0;
                $result = [
                    'success' => true,
                    'stats' => $collector->getDataCollectionStats($pageId)
                ];
                break;
                
            default:
                $result = ['success' => false, 'message' => 'Invalid action'];
        }
        
        echo json_encode($result);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ]);
    }
    exit;
}

// If not a POST request, show the management interface
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Data Collection Management</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 20px 0; padding: 20px; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #005a8b; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-secondary { background: #6c757d; }
        .form-group { margin: 15px 0; }
        .form-control { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .alert { padding: 15px; border-radius: 5px; margin: 15px 0; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007cba; }
        .stat-label { color: #6c757d; margin-top: 5px; }
        .loading { display: none; text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #007cba; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .data-table { max-height: 500px; overflow-y: auto; }
        .json-data { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Enhanced Data Collection Management</h1>

        <div class="card">
            <h2>Page Selection</h2>
            <div class="form-group">
                <label for="pageSelect">Select Page:</label>
                <select id="pageSelect" class="form-control">
                    <option value="">Loading pages...</option>
                </select>
            </div>
            <button onclick="enhancePage()" class="btn">Enable Data Collection</button>
            <button onclick="loadPageData()" class="btn btn-secondary">View Collected Data</button>
            <button onclick="loadPageStats()" class="btn btn-success">View Statistics</button>
        </div>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Processing...</p>
        </div>

        <div id="results"></div>

        <div id="statsContainer" style="display: none;">
            <div class="card">
                <h2>Data Collection Statistics</h2>
                <div id="statsContent"></div>
            </div>
        </div>

        <div id="dataContainer" style="display: none;">
            <div class="card">
                <h2>Collected Data</h2>
                <div id="dataContent"></div>
            </div>
        </div>
    </div>

    <script>
        // Load pages on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadPages();
        });

        async function loadPages() {
            try {
                const response = await fetch('includes/page_manager.php?action=get_pages');
                const result = await response.json();

                const select = document.getElementById('pageSelect');
                select.innerHTML = '<option value="">Select a page...</option>';

                if (result.success && result.pages) {
                    result.pages.forEach(page => {
                        const option = document.createElement('option');
                        option.value = page.id;
                        option.textContent = `${page.title || page.original_filename} (ID: ${page.id})`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading pages:', error);
                showAlert('Error loading pages: ' + error.message, 'danger');
            }
        }

        async function enhancePage() {
            const pageId = document.getElementById('pageSelect').value;
            if (!pageId) {
                showAlert('Please select a page first', 'danger');
                return;
            }

            showLoading(true);

            try {
                const formData = new FormData();
                formData.append('action', 'enhance_page');
                formData.append('page_id', pageId);

                const response = await fetch('enhanced_data_collection.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('Page enhanced for data collection successfully!', 'success');
                } else {
                    showAlert('Error: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('Error enhancing page:', error);
                showAlert('Error enhancing page: ' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        async function loadPageStats() {
            const pageId = document.getElementById('pageSelect').value;
            if (!pageId) {
                showAlert('Please select a page first', 'danger');
                return;
            }

            showLoading(true);

            try {
                const formData = new FormData();
                formData.append('action', 'get_stats');
                formData.append('page_id', pageId);

                const response = await fetch('enhanced_data_collection.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayStats(result.stats);
                } else {
                    showAlert('Error: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('Error loading stats:', error);
                showAlert('Error loading stats: ' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        async function loadPageData() {
            const pageId = document.getElementById('pageSelect').value;
            if (!pageId) {
                showAlert('Please select a page first', 'danger');
                return;
            }

            showLoading(true);

            try {
                const formData = new FormData();
                formData.append('action', 'get_data');
                formData.append('page_id', pageId);
                formData.append('limit', '50');
                formData.append('offset', '0');

                const response = await fetch('enhanced_data_collection.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayData(result.data);
                } else {
                    showAlert('Error: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('Error loading data:', error);
                showAlert('Error loading data: ' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        function displayStats(stats) {
            const container = document.getElementById('statsContainer');
            const content = document.getElementById('statsContent');

            let html = '<div class="stats-grid">';
            html += `<div class="stat-card">
                        <div class="stat-number">${stats.total_submissions}</div>
                        <div class="stat-label">Total Submissions</div>
                     </div>`;

            if (stats.by_form && stats.by_form.length > 0) {
                stats.by_form.forEach(form => {
                    html += `<div class="stat-card">
                                <div class="stat-number">${form.count}</div>
                                <div class="stat-label">${form.form_name || 'Unnamed Form'}</div>
                             </div>`;
                });
            }
            html += '</div>';

            if (stats.recent_activity && stats.recent_activity.length > 0) {
                html += '<h3>Recent Activity (Last 30 Days)</h3>';
                html += '<table class="table">';
                html += '<thead><tr><th>Date</th><th>Submissions</th></tr></thead><tbody>';
                stats.recent_activity.forEach(day => {
                    html += `<tr><td>${day.date}</td><td>${day.count}</td></tr>`;
                });
                html += '</tbody></table>';
            }

            content.innerHTML = html;
            container.style.display = 'block';
        }

        function displayData(data) {
            const container = document.getElementById('dataContainer');
            const content = document.getElementById('dataContent');

            if (!data || data.length === 0) {
                content.innerHTML = '<p>No data collected yet.</p>';
                container.style.display = 'block';
                return;
            }

            let html = '<div class="data-table">';
            html += '<table class="table">';
            html += '<thead><tr><th>ID</th><th>Form</th><th>Submitted</th><th>IP Address</th><th>Data</th></tr></thead><tbody>';

            data.forEach(submission => {
                html += '<tr>';
                html += `<td>${submission.id}</td>`;
                html += `<td>${submission.form_title || submission.form_name || 'Unknown'}</td>`;
                html += `<td>${submission.submitted_at}</td>`;
                html += `<td>${submission.ip_address || 'Unknown'}</td>`;
                html += `<td><div class="json-data">${JSON.stringify(submission.form_data, null, 2)}</div></td>`;
                html += '</tr>';
            });

            html += '</tbody></table></div>';
            content.innerHTML = html;
            container.style.display = 'block';
        }

        function showAlert(message, type) {
            const results = document.getElementById('results');
            results.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
    </script>
</body>
</html>
