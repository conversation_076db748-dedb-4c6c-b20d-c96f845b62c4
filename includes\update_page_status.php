<?php
/**
 * Simple Page Status Update Handler
 * Updates page status (active, archived, etc.)
 */

require_once '../config/database.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }

    $pageId = $_POST['page_id'] ?? null;
    $status = $_POST['status'] ?? null;

    if (!$pageId) {
        throw new Exception('Page ID is required');
    }

    if (!$status) {
        throw new Exception('Status is required');
    }

    // Validate status
    $allowedStatuses = ['active', 'archived', 'deleted'];
    if (!in_array($status, $allowedStatuses)) {
        throw new Exception('Invalid status. Allowed: ' . implode(', ', $allowedStatuses));
    }

    $database = new Database();
    $db = $database->getConnection();

    // Check if page exists
    $sql = "SELECT id, title, original_filename, status FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        throw new Exception('Page not found');
    }

    // Update page status
    $sql = "UPDATE pages SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $db->prepare($sql);
    $result = $stmt->execute([$status, $pageId]);

    if (!$result) {
        throw new Exception('Failed to update page status');
    }

    // Log the action
    $action = $status === 'archived' ? 'page_archived' : ($status === 'active' ? 'page_restored' : 'page_status_updated');
    
    $sql = "INSERT INTO activity_log (user_id, action, entity_type, entity_id, old_values, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        1, // Default user ID
        $action,
        'page',
        $pageId,
        json_encode(['status' => $page['status']]),
        json_encode(['status' => $status]),
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);

    $message = '';
    switch ($status) {
        case 'archived':
            $message = 'Page archived successfully';
            break;
        case 'active':
            $message = 'Page restored successfully';
            break;
        default:
            $message = 'Page status updated successfully';
    }

    echo json_encode([
        'success' => true,
        'message' => $message,
        'page_id' => $pageId,
        'old_status' => $page['status'],
        'new_status' => $status
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
