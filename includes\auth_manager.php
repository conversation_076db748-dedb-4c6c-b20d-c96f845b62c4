<?php
/**
 * Authentication Manager
 * Handles user authentication, session management, and access control
 */

require_once dirname(__DIR__) . '/config/database.php';

class AuthManager {
    private $db;
    private $sessionTimeout = 3600; // 1 hour
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Authenticate user with username/email and password
     */
    public function login($usernameOrEmail, $password) {
        try {
            // Find user by username or email
            $sql = "SELECT id, username, email, password_hash, full_name, role, is_active, last_login 
                    FROM users 
                    WHERE (username = ? OR email = ?) AND is_active = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$usernameOrEmail, $usernameOrEmail]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'Invalid username/email or password'
                ];
            }
            
            // Verify password (handle empty password for default admin)
            if (empty($user['password_hash'])) {
                // Default admin with empty password - require password setup
                if ($user['username'] === 'admin' && empty($password)) {
                    return [
                        'success' => false,
                        'message' => 'Please set up your admin password first',
                        'require_setup' => true,
                        'user_id' => $user['id']
                    ];
                }
            } else {
                if (!password_verify($password, $user['password_hash'])) {
                    return [
                        'success' => false,
                        'message' => 'Invalid username/email or password'
                    ];
                }
            }
            
            // Create session
            $this->createSession($user);
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            return [
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'full_name' => $user['full_name'],
                    'role' => $user['role']
                ]
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'Database error occurred'
            ];
        }
    }
    
    /**
     * Create user session
     */
    private function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // Regenerate session ID for security
        session_regenerate_id(true);
    }
    
    /**
     * Update user's last login timestamp
     */
    private function updateLastLogin($userId) {
        try {
            $sql = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
        } catch (PDOException $e) {
            // Log error but don't fail login
        }
    }
    
    /**
     * Check if user is logged in and session is valid
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
            return false;
        }
        
        // Check session timeout
        if (time() - $_SESSION['last_activity'] > $this->sessionTimeout) {
            $this->logout();
            return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    /**
     * Get current user information
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'email' => $_SESSION['email'],
            'full_name' => $_SESSION['full_name'],
            'role' => $_SESSION['role']
        ];
    }
    
    /**
     * Check if current user has specific role
     */
    public function hasRole($role) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        return $_SESSION['role'] === $role;
    }
    
    /**
     * Check if current user is admin
     */
    public function isAdmin() {
        return $this->hasRole('admin');
    }
    
    /**
     * Check if current user can edit (admin or editor)
     */
    public function canEdit() {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        return in_array($_SESSION['role'], ['admin', 'editor']);
    }
    
    /**
     * Logout user and destroy session
     */
    public function logout() {
        // Clear all session variables
        $_SESSION = array();

        // Delete session cookie
        if (ini_get("session.use_cookies") && session_id()) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Destroy session if active
        if (session_id()) {
            session_destroy();
        }

        return [
            'success' => true,
            'message' => 'Logged out successfully'
        ];
    }
    
    /**
     * Register new user (admin only)
     */
    public function registerUser($userData) {
        if (!$this->isAdmin()) {
            return [
                'success' => false,
                'message' => 'Only administrators can create new users'
            ];
        }
        
        try {
            // Validate required fields
            $required = ['username', 'email', 'password', 'full_name'];
            foreach ($required as $field) {
                if (empty($userData[$field])) {
                    return [
                        'success' => false,
                        'message' => "Field '$field' is required"
                    ];
                }
            }
            
            // Validate username
            if (!preg_match('/^[a-zA-Z0-9_]{3,50}$/', $userData['username'])) {
                return [
                    'success' => false,
                    'message' => 'Username must be 3-50 characters and contain only letters, numbers, and underscores'
                ];
            }
            
            // Validate email
            if (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                return [
                    'success' => false,
                    'message' => 'Invalid email address'
                ];
            }
            
            // Validate password
            if (strlen($userData['password']) < 8) {
                return [
                    'success' => false,
                    'message' => 'Password must be at least 8 characters long'
                ];
            }
            
            // Check if username or email already exists
            $sql = "SELECT id FROM users WHERE username = ? OR email = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userData['username'], $userData['email']]);
            
            if ($stmt->fetch()) {
                return [
                    'success' => false,
                    'message' => 'Username or email already exists'
                ];
            }
            
            // Hash password
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            // Set default role if not specified
            $role = $userData['role'] ?? 'editor';
            if (!in_array($role, ['admin', 'editor', 'viewer'])) {
                $role = 'editor';
            }
            
            // Insert new user
            $sql = "INSERT INTO users (username, email, password_hash, full_name, role, is_active) 
                    VALUES (?, ?, ?, ?, ?, 1)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $userData['username'],
                $userData['email'],
                $passwordHash,
                $userData['full_name'],
                $role
            ]);
            
            $userId = $this->db->lastInsertId();
            
            return [
                'success' => true,
                'message' => 'User created successfully',
                'user_id' => $userId
            ];
            
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                return [
                    'success' => false,
                    'message' => 'Username or email already exists'
                ];
            }
            
            return [
                'success' => false,
                'message' => 'Database error occurred'
            ];
        }
    }
    
    /**
     * Setup admin password (for initial setup)
     */
    public function setupAdminPassword($password) {
        try {
            if (strlen($password) < 8) {
                return [
                    'success' => false,
                    'message' => 'Password must be at least 8 characters long'
                ];
            }
            
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            
            $sql = "UPDATE users SET password_hash = ? WHERE username = 'admin' AND password_hash = ''";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$passwordHash]);
            
            if ($stmt->rowCount() > 0) {
                return [
                    'success' => true,
                    'message' => 'Admin password set successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Admin password already set or user not found'
                ];
            }
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'Database error occurred'
            ];
        }
    }
    
    /**
     * Require authentication for protected pages
     */
    public function requireAuth($redirectTo = 'login.php') {
        if (!$this->isLoggedIn()) {
            header("Location: $redirectTo");
            exit;
        }
    }
    
    /**
     * Require admin role
     */
    public function requireAdmin($redirectTo = 'login.php') {
        if (!$this->isAdmin()) {
            header("Location: $redirectTo");
            exit;
        }
    }
}
?>
