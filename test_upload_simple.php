<?php
// Simple upload test script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Upload Diagnostic Test</h1>";

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>POST Request Received</h2>";
    echo "<pre>POST Data: " . print_r($_POST, true) . "</pre>";
    echo "<pre>FILES Data: " . print_r($_FILES, true) . "</pre>";
    
    if (isset($_FILES['test_file'])) {
        $file = $_FILES['test_file'];
        echo "<h3>File Upload Test</h3>";
        
        if ($file['error'] === UPLOAD_ERR_OK) {
            $uploadDir = __DIR__ . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            
            // Create directory if it doesn't exist
            if (!is_dir($uploadDir)) {
                if (mkdir($uploadDir, 0777, true)) {
                    echo "<p style='color: green;'>✓ Created uploads directory: $uploadDir</p>";
                } else {
                    echo "<p style='color: red;'>✗ Failed to create uploads directory</p>";
                }
            }
            
            $targetFile = $uploadDir . 'test_' . time() . '_' . basename($file['name']);
            
            if (move_uploaded_file($file['tmp_name'], $targetFile)) {
                echo "<p style='color: green;'>✓ File uploaded successfully!</p>";
                echo "<p>Target: $targetFile</p>";
                echo "<p>Size: " . filesize($targetFile) . " bytes</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to move uploaded file</p>";
                echo "<p>Target was: $targetFile</p>";
                echo "<p>Temp file: {$file['tmp_name']}</p>";
                echo "<p>Directory writable: " . (is_writable($uploadDir) ? 'Yes' : 'No') . "</p>";
            }
        } else {
            echo "<p style='color: red;'>Upload error code: {$file['error']}</p>";
            
            $errors = [
                UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
                UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
            ];
            
            echo "<p>Error meaning: " . ($errors[$file['error']] ?? 'Unknown error') . "</p>";
        }
    }
    
    // Test the actual upload class
    echo "<h3>Testing Upload Class</h3>";
    try {
        require_once 'includes/upload.php';
        
        // Simulate the files array for the class
        if (isset($_FILES['test_file'])) {
            $_FILES['files'] = $_FILES['test_file'];
        }
        
        $uploader = new FileUploader();
        $result = $uploader->handleUpload();
        
        echo "<pre>Upload Class Result: " . print_r($result, true) . "</pre>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Upload Class Error: " . $e->getMessage() . "</p>";
        echo "<pre>Stack trace: " . $e->getTraceAsString() . "</pre>";
    }
    
} else {
    // Show diagnostic info
    echo "<h2>System Information</h2>";
    echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
    echo "<p><strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "</p>";
    echo "<p><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</p>";
    echo "<p><strong>Max File Uploads:</strong> " . ini_get('max_file_uploads') . "</p>";
    echo "<p><strong>File Uploads Enabled:</strong> " . (ini_get('file_uploads') ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
    
    echo "<h2>Directory Check</h2>";
    $uploadDir = __DIR__ . DIRECTORY_SEPARATOR . 'uploads';
    echo "<p><strong>Upload Directory:</strong> $uploadDir</p>";
    echo "<p><strong>Directory Exists:</strong> " . (is_dir($uploadDir) ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>Directory Writable:</strong> " . (is_writable($uploadDir) ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
    echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
    
    echo "<h2>Database Test</h2>";
    try {
        require_once 'config/database.php';
        $database = new Database();
        $conn = $database->getConnection();
        if ($conn) {
            echo "<p style='color: green;'>✓ Database connection successful</p>";
        } else {
            echo "<p style='color: red;'>✗ Database connection failed</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>File Upload Test</h2>";
}
?>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
h1, h2, h3 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>

<form method="POST" enctype="multipart/form-data">
    <h3>Upload Test Form</h3>
    <p>
        <label>Select a file to test:</label><br>
        <input type="file" name="test_file" accept=".html,.htm,.css,.js,.png,.jpg">
    </p>
    <p>
        <button type="submit">Test Upload</button>
    </p>
</form>

<h2>JavaScript Upload Test</h2>
<div id="js-upload-area" style="border: 2px dashed #ccc; padding: 20px; text-align: center; cursor: pointer; margin: 10px 0;">
    Click here or drag files to test JavaScript upload
    <input type="file" id="js-file-input" style="display: none;" multiple>
</div>
<div id="js-results"></div>

<script>
const jsUploadArea = document.getElementById('js-upload-area');
const jsFileInput = document.getElementById('js-file-input');
const jsResults = document.getElementById('js-results');

jsUploadArea.addEventListener('click', () => jsFileInput.click());
jsFileInput.addEventListener('change', testJSUpload);

async function testJSUpload() {
    const files = jsFileInput.files;
    console.log('Testing JS upload with files:', files);
    
    jsResults.innerHTML = '<p>Testing JavaScript upload...</p>';
    
    const formData = new FormData();
    Array.from(files).forEach((file, index) => {
        formData.append(`files[${index}]`, file);
    });
    
    try {
        const response = await fetch('includes/upload.php', {
            method: 'POST',
            body: formData
        });
        
        console.log('Response status:', response.status);
        const responseText = await response.text();
        console.log('Response text:', responseText);
        
        jsResults.innerHTML = `
            <h4>JavaScript Upload Result:</h4>
            <p><strong>Status:</strong> ${response.status}</p>
            <pre>${responseText}</pre>
        `;
        
    } catch (error) {
        console.error('JS Upload error:', error);
        jsResults.innerHTML = `
            <h4>JavaScript Upload Error:</h4>
            <p style="color: red;">${error.message}</p>
        `;
    }
}
</script>
