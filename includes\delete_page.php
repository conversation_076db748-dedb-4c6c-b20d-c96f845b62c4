<?php
/**
 * Simple Page Deletion Handler
 * Provides basic page deletion functionality
 */

require_once '../config/database.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }

    $pageId = $_POST['page_id'] ?? null;
    $deleteFiles = $_POST['delete_files'] ?? 'true';
    $deleteFiles = ($deleteFiles === 'true' || $deleteFiles === true);

    if (!$pageId) {
        throw new Exception('Page ID is required');
    }

    $database = new Database();
    $db = $database->getConnection();

    // Start transaction
    $db->beginTransaction();

    // Get page information
    $sql = "SELECT * FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        throw new Exception('Page not found');
    }

    // Get associated files
    $sql = "SELECT * FROM associated_files WHERE page_id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $associatedFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Delete physical files if requested
    if ($deleteFiles) {
        // Delete main page file
        if ($page['file_path'] && file_exists($page['file_path'])) {
            unlink($page['file_path']);
        }

        // Delete associated files
        foreach ($associatedFiles as $file) {
            if ($file['file_path'] && file_exists($file['file_path'])) {
                unlink($file['file_path']);
            }
        }
    }

    // Delete from database (foreign key constraints will handle related records)
    $sql = "DELETE FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);

    // Commit transaction
    $db->commit();

    // Log the deletion
    $sql = "INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        1, // Default user ID
        'page_deleted',
        'page',
        $pageId,
        json_encode([
            'page_title' => $page['title'],
            'original_filename' => $page['original_filename'],
            'files_deleted' => $deleteFiles,
            'associated_files_count' => count($associatedFiles)
        ]),
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);

    echo json_encode([
        'success' => true,
        'message' => 'Page deleted successfully',
        'deleted_files' => $deleteFiles ? count($associatedFiles) + 1 : 0
    ]);

} catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($db) && $db->inTransaction()) {
        $db->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
