<?php
/**
 * Authentication Check API
 * Returns current user session status and information
 */

require_once __DIR__ . '/auth_manager.php';

header('Content-Type: application/json');

$auth = new AuthManager();

if ($auth->isLoggedIn()) {
    $user = $auth->getCurrentUser();
    echo json_encode([
        'authenticated' => true,
        'user' => $user
    ]);
} else {
    echo json_encode([
        'authenticated' => false,
        'user' => null
    ]);
}
?>
