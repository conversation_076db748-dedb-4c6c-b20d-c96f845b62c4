<?php
/**
 * Asset Server for Webpage Manager
 * Serves CSS, JS, images, and other assets for viewed pages
 */

require_once 'config/database.php';

// Get the requested file
$filename = $_GET['file'] ?? null;

if (!$filename) {
    http_response_code(400);
    die('No file specified');
}

// Sanitize filename to prevent directory traversal
$filename = basename($filename);

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get file information from database
    $sql = "SELECT * FROM associated_files WHERE filename = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$filename]);
    $file = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$file) {
        http_response_code(404);
        die('File not found');
    }
    
    // Check if file exists on disk
    if (!file_exists($file['file_path'])) {
        http_response_code(404);
        die('File not found on disk');
    }
    
    // Get file info
    $fileSize = filesize($file['file_path']);
    $mimeType = $file['mime_type'] ?: getMimeType($file['file_path']);
    
    // Set appropriate headers
    header('Content-Type: ' . $mimeType);
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: public, max-age=31536000'); // Cache for 1 year
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
    
    // Set filename for downloads
    if (isset($_GET['download'])) {
        header('Content-Disposition: attachment; filename="' . $file['original_filename'] . '"');
    } else {
        header('Content-Disposition: inline; filename="' . $file['original_filename'] . '"');
    }
    
    // Handle range requests for large files (useful for videos/large images)
    if (isset($_SERVER['HTTP_RANGE'])) {
        handleRangeRequest($file['file_path'], $fileSize, $mimeType);
    } else {
        // Output the file
        readfile($file['file_path']);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    die('Database error');
} catch (Exception $e) {
    http_response_code(500);
    die('Server error');
}

function getMimeType($filePath) {
    $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
    
    $mimeTypes = [
        // Images
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'svg' => 'image/svg+xml',
        'webp' => 'image/webp',
        'ico' => 'image/x-icon',
        
        // Stylesheets
        'css' => 'text/css',
        
        // Scripts
        'js' => 'application/javascript',
        
        // Fonts
        'woff' => 'font/woff',
        'woff2' => 'font/woff2',
        'ttf' => 'font/ttf',
        'otf' => 'font/otf',
        'eot' => 'application/vnd.ms-fontobject',
        
        // Documents
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        
        // Archives
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
        
        // Default
        'default' => 'application/octet-stream'
    ];
    
    return $mimeTypes[$extension] ?? $mimeTypes['default'];
}

function handleRangeRequest($filePath, $fileSize, $mimeType) {
    $range = $_SERVER['HTTP_RANGE'];
    
    // Parse range header
    if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
        $start = intval($matches[1]);
        $end = $matches[2] ? intval($matches[2]) : $fileSize - 1;
        
        // Validate range
        if ($start > $end || $start >= $fileSize || $end >= $fileSize) {
            http_response_code(416);
            header('Content-Range: bytes */' . $fileSize);
            die('Requested range not satisfiable');
        }
        
        $length = $end - $start + 1;
        
        // Set partial content headers
        http_response_code(206);
        header('Content-Range: bytes ' . $start . '-' . $end . '/' . $fileSize);
        header('Content-Length: ' . $length);
        header('Accept-Ranges: bytes');
        
        // Output the requested range
        $file = fopen($filePath, 'rb');
        fseek($file, $start);
        
        $bufferSize = 8192;
        $remaining = $length;
        
        while ($remaining > 0 && !feof($file)) {
            $readSize = min($bufferSize, $remaining);
            echo fread($file, $readSize);
            $remaining -= $readSize;
            
            if (connection_aborted()) {
                break;
            }
        }
        
        fclose($file);
    } else {
        http_response_code(400);
        die('Invalid range header');
    }
}
?>
