/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&family=Poppins:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Root font size for responsive scaling */
html {
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    font-size: 1rem;
    line-height: 1.6;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.01em;
}

/* Form Labels */
label {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    color: #374151;
}

/* Tab Buttons */
.tab-btn {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

/* Button Text */
.btn {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    letter-spacing: -0.005em;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.25rem;
}

/* Responsive Typography */
@media (max-width: 1200px) {
    html { font-size: 15px; }
    .container { padding: 1rem; }
}

@media (max-width: 768px) {
    html { font-size: 14px; }
    .container { padding: 0.75rem; }
}

@media (max-width: 480px) {
    html { font-size: 13px; }
    .container { padding: 0.5rem; }
}

/* Improved Spacing for Main Content Areas */
.tab-content {
    margin-top: 1.5rem;
}

.tab-content h2 {
    font-size: clamp(1.3rem, 4vw, 1.8rem);
    margin-bottom: 1.5rem;
    color: #333;
    font-weight: 600;
}

.tab-content h3 {
    font-size: clamp(1.1rem, 3vw, 1.4rem);
    margin-bottom: 1.25rem;
    margin-top: 2rem;
    color: #495057;
    font-weight: 600;
}

.tab-content h4 {
    font-size: clamp(1rem, 2.5vw, 1.2rem);
    margin-bottom: 1rem;
    margin-top: 1.5rem;
    color: #667eea;
    font-weight: 600;
}

/* Better spacing between sections and buttons */
.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

.action-buttons {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.content-section {
    margin-bottom: 2rem;
}

.content-section:last-child {
    margin-bottom: 0;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 2.5rem;
    color: white;
    padding: 2rem 0;
}

.header h1 {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-size: clamp(2rem, 5vw, 2.8rem);
    margin-bottom: 0.75rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    font-weight: 700;
    letter-spacing: -0.02em;
}

.header h1 i {
    margin-right: 0.75rem;
    opacity: 0.9;
    font-size: 0.9em;
}

/* Feather Icons in Header */
.header h1 svg {
    width: clamp(1.8rem, 4vw, 2.5rem);
    height: clamp(1.8rem, 4vw, 2.5rem);
    margin-right: 0.75rem;
    opacity: 0.9;
    stroke-width: 1.5;
}

.header p {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    opacity: 0.9;
    margin-bottom: 1rem;
    font-weight: 400;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    background: rgba(255,255,255,0.15);
    border-radius: 12px;
    padding: 0.4rem;
    backdrop-filter: blur(10px);
    gap: 0.2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(102, 126, 234, 0.2);
    padding: 0.5rem 0 calc(0.5rem + env(safe-area-inset-bottom));
    z-index: 1000;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
    justify-content: space-around;
    align-items: center;
    gap: 0.25rem;
}

.mobile-nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 0.3rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 16px;
    min-width: 60px;
    flex: 1;
    max-width: 80px;
    position: relative;
    color: #888;
    font-family: 'Inter', sans-serif;
}

.mobile-nav-btn svg {
    width: 24px;
    height: 24px;
    margin-bottom: 0.3rem;
    stroke-width: 2;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    stroke: currentColor;
}

.mobile-nav-btn span {
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    line-height: 1;
}

.mobile-nav-btn:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.08);
    transform: translateY(-2px);
}

.mobile-nav-btn.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.12);
}

.mobile-nav-btn.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 3px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 0 0 3px 3px;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-3px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.mobile-nav-btn.active svg {
    transform: scale(1.1);
    color: #667eea;
}

.mobile-nav-btn.active span {
    font-weight: 600;
    color: #667eea;
}

.tab-btn {
    background: none;
    border: none;
    padding: clamp(0.75rem, 2vw, 1rem) clamp(1rem, 3vw, 1.5rem);
    color: white;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: clamp(0.85rem, 2vw, 1rem);
    font-weight: 500;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Inter', sans-serif;
}

.tab-btn i {
    font-size: 1.1em;
    opacity: 0.8;
}

/* Feather Icons Styling */
.tab-btn svg {
    width: 18px;
    height: 18px;
    stroke-width: 2;
    opacity: 0.8;
}

.tab-btn.active svg {
    opacity: 1;
}

.tab-btn:hover {
    background: rgba(255,255,255,0.2);
}

.tab-btn.active {
    background: white;
    color: #333;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.tab-btn.active i {
    color: #667eea;
}

/* Tab Content */
.tab-content {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 1rem;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Project Selection */
.project-selection {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.project-selection label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.75rem;
    font-family: 'Poppins', 'Inter', sans-serif;
}

.project-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.project-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
}

.project-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.project-input::placeholder {
    color: #999;
    font-style: italic;
}

.project-dropdown-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.project-dropdown-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.project-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-top: none;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.project-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.project-dropdown-item:last-child {
    border-bottom: none;
}

.project-dropdown-item:hover {
    background: rgba(102, 126, 234, 0.05);
}

.project-dropdown-item i {
    width: 16px;
    height: 16px;
    color: #667eea;
}

.project-dropdown-item span {
    font-family: 'Inter', sans-serif;
    color: #333;
}

.project-input-container.open .project-input {
    border-radius: 12px 12px 0 0;
    border-bottom-color: transparent;
}

.project-input-container.open .project-dropdown-btn {
    transform: translateY(-50%) rotate(180deg);
}

/* Upload Section */
.upload-area {
    border: 2px dashed rgba(102, 126, 234, 0.3);
    border-radius: 16px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02), rgba(118, 75, 162, 0.02));
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.upload-area:hover {
    border-color: rgba(102, 126, 234, 0.6);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.upload-area:hover::before {
    opacity: 1;
}

.upload-area.dragover {
    border-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(32, 201, 151, 0.05));
    transform: scale(1.02);
    box-shadow: 0 15px 50px rgba(40, 167, 69, 0.2);
}

.upload-content {
    position: relative;
    z-index: 1;
}

.upload-content i {
    font-size: clamp(3rem, 6vw, 4rem);
    color: #667eea;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    display: block;
}

/* Feather Icons in Upload Area */
.upload-content svg {
    width: clamp(3rem, 6vw, 4rem);
    height: clamp(3rem, 6vw, 4rem);
    color: #667eea;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    stroke-width: 1.2;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.upload-content h3 {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-size: clamp(1.4rem, 3vw, 1.8rem);
    margin-bottom: 0.75rem;
    color: #333;
    font-weight: 600;
    letter-spacing: -0.02em;
}

.upload-content p {
    color: #666;
    margin-bottom: 1rem;
    font-size: clamp(1rem, 2vw, 1.1rem);
    line-height: 1.6;
    opacity: 0.9;
}

.upload-info {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.upload-info small {
    color: #888;
    font-size: 0.9rem;
    opacity: 0.8;
}

.file-types {
    font-size: clamp(0.8rem, 1.8vw, 0.9rem);
    color: #999;
    margin-top: 0.5rem;
}

#file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Progress Bar */
.upload-progress {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-weight: 500;
}

/* Upload Results */
.upload-results {
    margin-top: 20px;
}

.result-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    border-radius: 8px;
    background: #f8f9fa;
}

.result-item.success {
    border-left: 4px solid #28a745;
}

.result-item.error {
    border-left: 4px solid #dc3545;
}

.result-item i {
    margin-right: 10px;
    width: 20px;
}

/* Search Bar */
.search-bar {
    display: flex;
    margin-bottom: 1.5rem;
    gap: 0.75rem;
    flex-wrap: wrap;
}

#search-pages {
    flex: 1;
    min-width: 200px;
    padding: clamp(0.6rem, 2vw, 0.75rem) clamp(0.8rem, 2vw, 1rem);
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: clamp(0.85rem, 2vw, 1rem);
}

#search-pages:focus {
    outline: none;
    border-color: #667eea;
}

/* Pages Grid */
.pages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.page-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    position: relative;
}

.page-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px 12px 0 0;
}

.page-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    border-color: rgba(102, 126, 234, 0.15);
}

.page-card h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: clamp(1rem, 2.5vw, 1.2rem);
    font-weight: 600;
}

.page-card .page-info {
    font-size: clamp(0.8rem, 2vw, 0.9rem);
    color: #666;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.page-card .forms-count {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
    margin-top: 10px;
}

/* Buttons */
.btn {
    padding: clamp(0.7rem, 2vw, 0.85rem) clamp(1.2rem, 3vw, 1.5rem);
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: clamp(0.9rem, 2vw, 1rem);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    margin-bottom: 0.5rem;
    font-family: 'Inter', sans-serif;
    min-height: 44px;
    position: relative;
    overflow: hidden;
    letter-spacing: -0.01em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

/* Feather Icons in Buttons */
.btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

/* General Feather Icons Styling */
svg[data-feather] {
    stroke: currentColor;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
}

/* Feather Icons in Form Labels */
label svg {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    opacity: 0.8;
}

/* Feather Icons in Alerts */
.alert svg {
    width: 18px;
    height: 18px;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a42a0 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

#refresh-pages {
    padding: 12px 15px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

/* Database Actions */
.db-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.db-tables {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Enhanced Share Modal Styles */
.enhanced-share-modal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 30px 100px rgba(102, 126, 234, 0.3);
    max-width: 700px;
    width: 90%;
}

/* Enhanced URL Modal */
.enhanced-url-modal {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 30px 100px rgba(40, 167, 69, 0.3);
    max-width: 700px;
    width: 90%;
}

/* Enhanced Edit Modal */
.enhanced-edit-modal {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 30px 100px rgba(253, 126, 20, 0.3);
    max-width: 700px;
    width: 90%;
}

.enhanced-share-modal .modal-header,
.enhanced-url-modal .modal-header,
.enhanced-edit-modal .modal-header {
    background: transparent;
    color: white;
    padding: 25px 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.enhanced-share-modal .modal-body,
.enhanced-url-modal .modal-body,
.enhanced-edit-modal .modal-body {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    color: #333;
    border-radius: 0 0 20px 20px;
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-title-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-icon {
    font-size: 1.5rem;
    opacity: 0.9;
}

.enhanced-share-modal .modal-close,
.enhanced-url-modal .modal-close,
.enhanced-edit-modal .modal-close {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.enhanced-share-modal .modal-close:hover,
.enhanced-url-modal .modal-close:hover,
.enhanced-edit-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Form Sections */
.form-section {
    background: rgba(248, 249, 250, 0.9);
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 16px;
    border: 1px solid rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-section:hover {
    background: rgba(248, 249, 250, 1);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.form-section h4 {
    color: #667eea;
    margin: 0 0 12px 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.form-section h4 i {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Modern Input Styling */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="url"],
input[type="search"],
input[type="datetime-local"],
input[type="date"],
textarea,
select {
    font-family: 'Inter', sans-serif;
    padding: 12px 16px;
    border: 2px solid rgba(0,0,0,0.1);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    width: 100%;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* Enhanced Form Elements */
.enhanced-share-modal .form-group,
.enhanced-url-modal .form-group,
.enhanced-edit-modal .form-group {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 0.95rem;
}

.enhanced-share-modal label,
.enhanced-url-modal label,
.enhanced-edit-modal label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.enhanced-share-modal input,
.enhanced-share-modal textarea,
.enhanced-share-modal select,
.enhanced-url-modal input,
.enhanced-url-modal textarea,
.enhanced-url-modal select,
.enhanced-edit-modal input,
.enhanced-edit-modal textarea,
.enhanced-edit-modal select {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    background: white;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.enhanced-share-modal input:focus,
.enhanced-share-modal textarea:focus,
.enhanced-share-modal select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.enhanced-share-modal input:hover,
.enhanced-share-modal textarea:hover,
.enhanced-share-modal select:hover {
    border-color: #adb5bd;
}

.form-help {
    display: block;
    margin-top: 3px;
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
    line-height: 1.3;
}

/* Enhanced Checkboxes for all modals */
.enhanced-share-modal .checkbox-group,
.enhanced-url-modal .checkbox-group,
.enhanced-edit-modal .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.enhanced-share-modal .enhanced-checkbox,
.enhanced-url-modal .enhanced-checkbox,
.enhanced-edit-modal .enhanced-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    padding: 10px 12px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(102, 126, 234, 0.15);
    transition: all 0.2s ease;
}

.enhanced-share-modal .enhanced-checkbox:hover,
.enhanced-url-modal .enhanced-checkbox:hover,
.enhanced-edit-modal .enhanced-checkbox:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

/* Hide the default checkbox */
.enhanced-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Custom checkbox styling */
.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #667eea;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-top: 2px;
    background: white;
    cursor: pointer;
}

/* Checked state */
.enhanced-checkbox input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

/* Checkmark icon */
.enhanced-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

/* Hover effect */
.enhanced-checkbox:hover .checkmark {
    border-color: #5a6fd8;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* Focus effect */
.enhanced-checkbox input[type="checkbox"]:focus + .checkmark {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.checkbox-text {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.checkbox-text strong {
    color: #495057;
    font-size: 0.85rem;
    font-weight: 500;
}

.checkbox-text small {
    color: #6c757d;
    font-size: 0.75rem;
    line-height: 1.2;
}

/* Enhanced Action Buttons */
.enhanced-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid rgba(102, 126, 234, 0.15);
}

.enhanced-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
    min-width: 120px;
    justify-content: center;
}

.enhanced-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.enhanced-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.enhanced-secondary {
    background: rgba(108, 117, 125, 0.1);
    color: #495057;
    border: 1px solid #dee2e6;
    padding: 11px 22px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 90px;
    justify-content: center;
}

.enhanced-secondary:hover {
    background: rgba(108, 117, 125, 0.2);
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.enhanced-secondary:active {
    transform: translateY(0);
}

/* Form Row Enhancements */
.enhanced-share-modal .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

/* Additional Modal Improvements */
.enhanced-share-modal .modal-title-section h3,
.enhanced-url-modal .modal-title-section h3 {
    margin: 0;
    font-size: clamp(1.1rem, 3vw, 1.3rem);
    font-weight: 600;
}

.enhanced-share-modal .modal-close,
.enhanced-url-modal .modal-close {
    color: white;
    font-size: clamp(1rem, 2.5vw, 1.2rem);
}

/* Compact form styling */
.enhanced-share-modal .form-group:last-child {
    margin-bottom: 0;
}

.enhanced-share-modal select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
}

/* Better spacing for sections */
.form-section:last-child {
    margin-bottom: 0;
}

/* Responsive Design for Enhanced Modals */
@media (max-width: 768px) {
    .enhanced-share-modal,
    .enhanced-url-modal,
    .enhanced-edit-modal {
        margin: 2% auto;
        width: 95%;
        max-height: 95vh;
    }

    .enhanced-share-modal .modal-body,
    .enhanced-url-modal .modal-body,
    .enhanced-edit-modal .modal-body {
        padding: 20px;
    }

    .enhanced-share-modal .form-section,
    .enhanced-url-modal .form-section,
    .enhanced-edit-modal .form-section {
        padding: 15px;
        margin-bottom: 15px;
    }

    .enhanced-share-modal .enhanced-actions,
    .enhanced-url-modal .enhanced-actions,
    .enhanced-edit-modal .enhanced-actions {
        flex-direction: column;
        gap: 10px;
    }

    .enhanced-share-modal .enhanced-primary,
    .enhanced-share-modal .enhanced-secondary,
    .enhanced-url-modal .enhanced-primary,
    .enhanced-url-modal .enhanced-secondary,
    .enhanced-edit-modal .enhanced-primary,
    .enhanced-edit-modal .enhanced-secondary {
        width: 100%;
        padding: 16px;
        font-size: 16px;
    }

    .enhanced-share-modal .form-row,
    .enhanced-url-modal .form-row,
    .enhanced-edit-modal .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .enhanced-share-modal .enhanced-checkbox,
    .enhanced-url-modal .enhanced-checkbox,
    .enhanced-edit-modal .enhanced-checkbox {
        padding: 12px;
    }

    .enhanced-share-modal .modal-title-section h3,
    .enhanced-url-modal .modal-title-section h3,
    .enhanced-edit-modal .modal-title-section h3 {
        font-size: 1.3rem;
    }

    /* Mobile-specific checkbox improvements */
    .enhanced-share-modal .checkmark,
    .enhanced-url-modal .checkmark,
    .enhanced-edit-modal .checkmark {
        width: 22px;
        height: 22px;
        margin-top: 1px;
    }

    .enhanced-share-modal .checkbox-text,
    .enhanced-url-modal .checkbox-text,
    .enhanced-edit-modal .checkbox-text {
        gap: 3px;
    }
}

@media (max-width: 480px) {
    .enhanced-share-modal .modal-body,
    .enhanced-url-modal .modal-body,
    .enhanced-edit-modal .modal-body {
        padding: 15px;
    }

    .enhanced-share-modal .form-section,
    .enhanced-url-modal .form-section,
    .enhanced-edit-modal .form-section {
        padding: 12px;
    }

    .enhanced-share-modal .form-section h4,
    .enhanced-url-modal .form-section h4,
    .enhanced-edit-modal .form-section h4 {
        font-size: 1rem;
    }

    .enhanced-share-modal .enhanced-checkbox,
    .enhanced-url-modal .enhanced-checkbox,
    .enhanced-edit-modal .enhanced-checkbox {
        padding: 10px;
        gap: 10px;
    }

    .enhanced-share-modal .checkbox-text strong,
    .enhanced-url-modal .checkbox-text strong,
    .enhanced-edit-modal .checkbox-text strong {
        font-size: 0.8rem;
    }

    .enhanced-share-modal .checkbox-text small,
    .enhanced-url-modal .checkbox-text small,
    .enhanced-edit-modal .checkbox-text small {
        font-size: 0.7rem;
    }
}

/* Page Details Styles */
.page-details h4 {
    color: #667eea;
    margin: 20px 0 10px 0;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 5px;
}

.forms-list {
    margin-top: 15px;
}

.form-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.form-item h5 {
    color: #333;
    margin-bottom: 10px;
}

.fields-list {
    margin-top: 10px;
    display: grid;
    gap: 8px;
}

.field-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.field-name {
    font-weight: 500;
}

/* Form Editing Styles */
.forms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.forms-header h4 {
    margin: 0;
    color: #667eea;
}

.btn-sm {
    padding: 8px 12px;
    font-size: 0.9rem;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.form-header h5 {
    margin: 0;
    color: #333;
}

.form-attributes {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-attr {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-attr label {
    font-weight: 500;
    color: #555;
    font-size: 0.9rem;
}

.form-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.fields-section {
    margin-top: 15px;
}

.fields-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.fields-header h6 {
    margin: 0;
    color: #555;
    font-size: 1rem;
}

.field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.field-basic-info {
    display: flex;
    gap: 10px;
    align-items: center;
    flex: 1;
}

.field-name-input {
    min-width: 150px;
}

.field-type-select {
    min-width: 120px;
}

.field-actions {
    display: flex;
    gap: 5px;
}

.field-attributes {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-top: 10px;
}

.field-attr-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 10px;
}

.field-attr-row:last-child {
    margin-bottom: 0;
}

.field-attr {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.field-attr.checkbox-attr {
    flex-direction: row;
    align-items: center;
}

.field-attr.checkbox-attr label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
    cursor: pointer;
}

.field-input {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.85rem;
    transition: border-color 0.3s ease;
}

.field-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.btn-icon {
    background: none;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    color: #667eea;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-icon:hover {
    background: #f0f2ff;
    color: #5a67d8;
}

.btn-icon.btn-danger {
    color: #dc3545;
}

.btn-icon.btn-danger:hover {
    background: #fff5f5;
    color: #c53030;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Page Actions */
.page-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.page-buttons {
    display: flex;
    gap: 5px;
}

/* Sharing Styles */
.sharing-section {
    padding: 0;
}

.sharing-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
    flex-wrap: wrap;
}

.shares-container h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: clamp(1.1rem, 3vw, 1.3rem);
    margin-top: 1rem;
}

.shares-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.share-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.share-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.share-card.expired {
    opacity: 0.7;
    border-color: #dc3545;
}

.share-card.limit-reached {
    opacity: 0.7;
    border-color: #ffc107;
}

.share-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.share-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.share-status {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.expired {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.limit {
    background: #fff3cd;
    color: #856404;
}

.status-badge.protected {
    background: #d1ecf1;
    color: #0c5460;
}

.share-info {
    margin-bottom: 15px;
}

.share-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: #666;
}

.share-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.share-urls {
    margin-bottom: 15px;
}

.url-group {
    margin-bottom: 10px;
}

.url-group label {
    display: block;
    font-weight: 500;
    color: #555;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.url-input-group {
    display: flex;
    gap: 5px;
}

.url-input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    background: #f8f9fa;
}

.share-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* Modal Enhancements */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Share Result Notifications */
.notification.share-result,
.notification.url-result {
    min-width: 400px;
    max-width: 500px;
}

.result-urls {
    margin-top: 15px;
}

.url-result {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.url-result label {
    min-width: 80px;
    font-weight: 500;
}

.url-result input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

.url-result button {
    padding: 8px 15px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.url-result button:hover {
    background: #5a67d8;
}

/* Info Notifications */
.notification.info {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

/* Warning Notifications */
.notification.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

/* Enhanced Upload Styles */
.upload-options {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Upload Mode Selector */
.upload-mode-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 0;
}

@media (max-width: 768px) {
    .upload-mode-selector {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

.mode-option {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 1.25rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.mode-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.mode-option:hover {
    border-color: rgba(102, 126, 234, 0.4);
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.mode-option:hover::before {
    transform: scaleX(1);
}

.mode-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.mode-option input[type="radio"]:checked + .mode-label {
    color: #667eea;
}

.mode-option input[type="radio"]:checked + .mode-label svg {
    color: #667eea;
    transform: scale(1.1);
}

.mode-option:has(input[type="radio"]:checked) {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.08);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.mode-option:has(input[type="radio"]:checked)::before {
    transform: scaleX(1);
}

.mode-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-family: 'Poppins', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.mode-label svg {
    width: 20px;
    height: 20px;
    color: #667eea;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.mode-option small {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-left: 2.75rem;
    opacity: 0.8;
}

.radio-label {
    display: flex;
    flex-direction: column;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.radio-label:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.radio-label input[type="radio"] {
    margin: 0;
    margin-bottom: 8px;
}

.radio-label span {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.radio-label small {
    color: #666;
    font-size: 0.9rem;
}

.radio-label input[type="radio"]:checked + span {
    color: #667eea;
}

.radio-label:has(input[type="radio"]:checked) {
    border-color: #667eea;
    background: #f8f9ff;
}

.project-selection {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.project-selection label {
    font-weight: 500;
    color: #333;
}

.project-selection select {
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 1rem;
}

.project-selection .btn {
    align-self: flex-start;
}

/* Enhanced Upload Results */
.upload-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.upload-summary h3 {
    margin: 0;
    color: #333;
}

.upload-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.result-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    border-left: 4px solid #28a745;
}

.result-item.error {
    border-left-color: #dc3545;
}

.result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.result-header i {
    font-size: 1.2rem;
}

.result-item.success .result-header i {
    color: #28a745;
}

.result-item.error .result-header i {
    color: #dc3545;
}

.file-name {
    font-weight: 500;
    flex: 1;
}

.file-type {
    background: #e9ecef;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.result-message {
    color: #666;
    margin-bottom: 10px;
}

.zip-details {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
}

.pages-list {
    margin: 10px 0 0 20px;
    color: #555;
}

.pages-list li {
    margin-bottom: 5px;
}

.file-details {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.file-details .btn {
    padding: 5px 10px;
    font-size: 0.9rem;
}

/* Enhanced Page Management Styles */
.manage-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 0;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    min-width: 280px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.search-input::placeholder {
    color: #999;
    font-style: italic;
}

.status-filter, .project-filter {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    min-width: 160px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
    cursor: pointer;
}

.status-filter:focus, .project-filter:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.action-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

/* Bulk Actions Panel */
.bulk-actions-panel {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 16px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bulk-actions-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.selected-count {
    font-weight: 600;
    color: #667eea;
    font-family: 'Poppins', 'Inter', sans-serif;
    font-size: 1.1rem;
}

.bulk-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.bulk-buttons .btn {
    font-size: 0.9rem;
    padding: 0.6rem 1rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.bulk-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Enhanced Page Cards */
.page-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.page-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.page-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.page-select {
    flex-shrink: 0;
}

.page-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.page-title {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.page-title h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.project-badge {
    background: #667eea;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-archived {
    background: #fff3cd;
    color: #856404;
}

.status-deleted {
    background: #f8d7da;
    color: #721c24;
}

.page-actions {
    display: flex;
    gap: 5px;
    flex-shrink: 0;
    flex-wrap: wrap;
}

.page-actions .btn {
    padding: 6px 10px;
    font-size: 0.9rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.page-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.page-actions .btn-sm {
    background: #6c757d;
    color: white;
}

.page-actions .btn-sm:hover {
    background: #5a6268;
}

.page-actions .btn-danger {
    background: #dc3545;
    color: white;
}

.page-actions .btn-danger:hover {
    background: #c82333;
}

.page-actions .btn-warning {
    background: #ffc107;
    color: #212529;
}

.page-actions .btn-warning:hover {
    background: #e0a800;
}

.page-actions .btn-success {
    background: #28a745;
    color: white;
}

.page-actions .btn-success:hover {
    background: #218838;
}

.page-info {
    color: #666;
}

.page-info p {
    margin: 5px 0;
}

.page-stats {
    display: flex;
    gap: 15px;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.page-stats .stat {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 0.9rem;
}

.page-stats .stat i {
    color: #667eea;
}

/* Page Statistics */
.page-stats {
    display: flex;
    justify-content: space-around;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    color: #333;
    font-size: 1.5rem;
    font-weight: bold;
}

/* Delete Modal Styles */
.delete-warning {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    margin-bottom: 20px;
}

.delete-warning i {
    color: #856404;
    font-size: 1.5rem;
}

.delete-warning p {
    margin: 0;
    color: #856404;
    font-weight: 500;
}

.delete-options {
    margin-bottom: 20px;
}

.checkbox-label {
    display: flex;
    flex-direction: column;
    gap: 5px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
}

.checkbox-label small {
    color: #666;
    margin-left: 30px;
}

.delete-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.delete-details ul {
    margin: 10px 0;
    padding-left: 20px;
}

.delete-details li {
    margin-bottom: 5px;
}

.warning-text {
    color: #dc3545;
    font-weight: bold;
    margin-top: 15px;
    margin-bottom: 0;
}

/* Button Variants */
.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

/* No Pages Message */
.no-pages {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-size: 1.1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .manage-controls {
        flex-direction: column;
        gap: 1.5rem;
        align-items: stretch;
        padding: 1.25rem;
    }

    .filter-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .search-input,
    .status-filter,
    .project-filter {
        min-width: auto;
        width: 100%;
    }

    .action-controls {
        justify-content: center;
        gap: 0.5rem;
    }

    .bulk-actions-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .bulk-buttons {
        justify-content: center;
    }

    .search-controls {
        flex-direction: column;
        gap: 10px;
    }

    .search-input {
        min-width: auto;
    }

    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .page-title {
        justify-content: center;
    }

    .page-actions {
        justify-content: center;
    }

    .page-stats {
        flex-direction: column;
        gap: 10px;
    }

    .bulk-actions-content {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .bulk-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Forms Tab Styles */
.forms-section {
    padding: 0;
}

.forms-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-selector {
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-selector label {
    font-weight: 500;
    color: #333;
}

.page-selector select {
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 1rem;
    min-width: 250px;
}

.form-actions {
    display: flex;
    gap: 10px;
}

.forms-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.no-forms-message {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    color: #666;
}

.no-forms-message i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.no-forms-message h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.no-forms-message p {
    margin: 0;
    font-size: 1.1rem;
}

/* Form Editor Styles */
.form-editor {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    border-left: 4px solid #667eea;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.form-header h3 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-header i {
    color: #667eea;
}

.form-type {
    background: #e9ecef;
    color: #495057;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.form-info {
    display: flex;
    gap: 15px;
    align-items: center;
    color: #666;
    font-size: 0.9rem;
}

.form-info span {
    background: #f8f9fa;
    padding: 5px 10px;
    border-radius: 15px;
}

.form-attributes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.form-attr {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-attr label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.form-attr input,
.form-attr select {
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-attr input:focus,
.form-attr select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Field Editor Styles */
.form-fields {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.field-editor {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.field-title {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.field-title strong {
    color: #333;
}

.field-type {
    background: #667eea;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 500;
}

.field-label {
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

.field-actions {
    display: flex;
    gap: 5px;
}

.btn-icon {
    background: none;
    border: none;
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    color: #666;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: #e9ecef;
    color: #333;
}

.btn-icon.btn-danger {
    color: #dc3545;
}

.btn-icon.btn-danger:hover {
    background: #f8d7da;
    color: #721c24;
}

.field-details {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.field-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.field-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.field-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.field-group input,
.field-group select {
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9rem;
}

.field-group input:focus,
.field-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.checkbox-label {
    flex-direction: row !important;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    width: auto;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.form-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Responsive Design for Forms */
@media (max-width: 768px) {
    .forms-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .page-selector {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .page-selector select {
        min-width: auto;
    }

    .form-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .form-info {
        justify-content: center;
        flex-wrap: wrap;
    }

    .form-attributes {
        grid-template-columns: 1fr;
    }

    .field-grid {
        grid-template-columns: 1fr;
    }

    .field-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .field-title {
        justify-content: center;
    }

    .field-actions {
        justify-content: center;
    }

    .form-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Enhanced Database Tab Styles */
.database-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.db-nav-btn {
    padding: 12px 20px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #495057;
}

.db-nav-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.db-nav-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.db-section {
    display: none;
}

.db-section.active {
    display: block;
}

/* Form Submissions Styles */
.submissions-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    flex-wrap: wrap;
    gap: 15px;
}

.submissions-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.submissions-filters select,
.submissions-filters input {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 0.9rem;
    min-width: 150px;
}

.submissions-actions {
    display: flex;
    gap: 10px;
}

.submissions-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.no-submissions-message {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-submissions-message i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.no-submissions-message h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.submissions-table {
    width: 100%;
    border-collapse: collapse;
}

.submissions-table th,
.submissions-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.submissions-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
}

.submissions-table tr:hover {
    background: #f8f9fa;
}

.submission-row {
    cursor: pointer;
}

.submission-data {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.submission-date {
    color: #666;
    font-size: 0.9rem;
}

.submission-source {
    display: flex;
    align-items: center;
    gap: 5px;
}

.source-badge {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 500;
}

.source-badge.shared {
    background: #667eea;
    color: white;
}

.source-badge.direct {
    background: #28a745;
    color: white;
}

/* Analytics Styles */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.analytics-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.analytics-card h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 500;
}

.analytics-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    margin: 0;
}

.analytics-charts {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 25px;
}

.analytics-charts h4 {
    margin: 0 0 20px 0;
    color: #333;
}

.chart-container {
    position: relative;
    height: 300px;
}

.chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    color: #666;
    font-style: italic;
}

/* Submission Detail Modal */
.submission-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.submission-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 10px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.submission-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.submission-modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.modal-close-btn:hover {
    color: #333;
}

.submission-details {
    display: grid;
    gap: 15px;
}

.detail-group {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.detail-group h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #495057;
}

.detail-value {
    color: #333;
    word-break: break-word;
}

/* Responsive Design for Database Tab */
@media (max-width: 768px) {
    .database-nav {
        flex-direction: column;
    }

    .db-nav-btn {
        justify-content: center;
    }

    .submissions-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .submissions-filters {
        justify-content: center;
    }

    .submissions-filters select,
    .submissions-filters input {
        min-width: auto;
        flex: 1;
    }

    .submissions-actions {
        justify-content: center;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .submissions-table {
        font-size: 0.9rem;
    }

    .submissions-table th,
    .submissions-table td {
        padding: 8px;
    }

    .submission-data {
        max-width: 150px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-attributes {
        grid-template-columns: 1fr;
    }

    .field-attr-row {
        grid-template-columns: 1fr;
    }

    .field-basic-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .forms-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .shares-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .sharing-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .share-header {
        flex-direction: column;
        gap: 10px;
    }

    .share-stats {
        flex-direction: column;
        gap: 8px;
    }

    .notification.share-result,
    .notification.url-result {
        min-width: auto;
        max-width: 90vw;
    }
}

.field-type {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.field-required {
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    text-transform: uppercase;
}

/* Database Structure Styles */
.table-structure {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table-structure h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-structure h4::before {
    content: "🗃️";
    font-size: 1.1rem;
}

.table-fields {
    display: grid;
    gap: 8px;
}

.field-row {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.field-row .field-name {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
    min-width: 150px;
}

.field-row .field-type {
    background: #28a745;
    color: white;
    padding: 4px 10px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.field-constraints {
    color: #6c757d;
    font-size: 0.85rem;
    font-style: italic;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease;
    max-width: 300px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.notification.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px 10px 80px 10px; /* Add bottom padding for mobile nav */
    }

    /* Show desktop navigation on mobile (horizontal, compact) */
    .nav-tabs {
        display: flex !important;
        flex-direction: row;
        gap: 0.25rem;
        margin-bottom: 1rem;
        overflow-x: auto;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .tab-btn {
        flex: 0 0 auto;
        min-width: 80px;
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
        border-radius: 8px;
        margin: 0;
        background: transparent;
        border: none;
        transition: all 0.3s ease;
        text-align: center;
        white-space: nowrap;
    }

    .tab-btn:hover {
        background: rgba(102, 126, 234, 0.1);
    }

    .tab-btn.active {
        background: #667eea;
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .tab-btn svg {
        width: 18px;
        height: 18px;
        margin-bottom: 0.25rem;
        display: block;
        margin-left: auto;
        margin-right: auto;
    }

    /* Hide tab text on very small screens, show icons only */
    @media (max-width: 480px) {
        .tab-btn {
            min-width: 60px;
            padding: 0.75rem 0.25rem;
        }

        .tab-text {
            display: none;
        }

        .tab-btn svg {
            width: 20px;
            height: 20px;
            margin-bottom: 0;
        }
    }

    /* Hide mobile bottom navigation */
    .mobile-bottom-nav {
        display: none !important;
    }

    /* Ensure content doesn't get hidden behind bottom nav */
    body {
        padding-bottom: 80px;
    }

    .pages-grid {
        grid-template-columns: 1fr;
    }

    .db-actions {
        flex-direction: column;
    }

    .field-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .field-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* Desktop styles */
@media (min-width: 769px) {
    .mobile-bottom-nav {
        display: none !important;
    }

    .nav-tabs {
        display: flex !important;
    }

    body {
        padding-bottom: 0;
    }

    .container {
        padding: 20px;
    }
}

/* Authentication Styles */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-main {
    flex: 1;
}

.header-auth {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    font-size: 0.9rem;
}

.auth-user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.auth-user-info .user-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.auth-user-info .user-details {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
}

.auth-user-info .user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.auth-user-info .user-role {
    font-size: 0.75rem;
    opacity: 0.8;
    text-transform: capitalize;
}

.auth-actions {
    display: flex;
    gap: 0.5rem;
}

.auth-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.auth-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.auth-btn.login-btn {
    background: rgba(255,255,255,0.9);
    color: #333;
}

.auth-btn.login-btn:hover {
    background: white;
}

/* Admin-only elements */
.admin-only {
    display: none;
}

.admin-only.show {
    display: block;
}

.tab-btn.admin-only.show {
    display: flex;
}

/* User Management Styles */
.users-section {
    padding: 0;
}

.users-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.user-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.user-card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}

.user-info h4 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.user-info p {
    margin: 0.2rem 0 0 0;
    color: #666;
    font-size: 0.9rem;
}

.user-details {
    margin-bottom: 1rem;
}

.user-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.user-detail .label {
    font-weight: 500;
    color: #555;
}

.user-detail .value {
    color: #333;
}

.user-status {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.user-status.active {
    background: #d4edda;
    color: #155724;
}

.user-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.user-role-badge {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.user-role-badge.admin {
    background: #fff3cd;
    color: #856404;
}

.user-role-badge.editor {
    background: #d1ecf1;
    color: #0c5460;
}

.user-role-badge.viewer {
    background: #e2e3e5;
    color: #383d41;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.user-actions .btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* Responsive adjustments for authentication */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .header-auth {
        justify-content: center;
        flex-wrap: wrap;
    }

    .auth-user-info {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .users-grid {
        grid-template-columns: 1fr;
    }

    .users-actions {
        justify-content: center;
    }
}
