# Webpage Manager - Form Analysis & Database Generator

A comprehensive web application for importing, analyzing, and managing HTML webpages with automatic form detection and database structure generation.

## Features

### 🚀 Core Functionality
- **File Import System**: Upload HTML, CSS, JavaScript, images, and font files
- **Form Detection**: Automatically detect and analyze forms in HTML pages
- **Field Analysis**: Extract detailed information about form inputs, including types, attributes, and validation rules
- **Database Generation**: Automatically create database tables based on detected form structures
- **File Management**: Organize and manage imported files with associated assets

### 📊 Analysis Capabilities
- **Form Parsing**: Detect forms with all attributes (action, method, enctype, etc.)
- **Input Field Analysis**: Extract comprehensive field information including:
  - Field types (text, email, number, date, etc.)
  - Validation attributes (required, pattern, min/max, etc.)
  - Options for select, radio, and checkbox fields
  - Placeholder text and default values
- **Metadata Extraction**: Extract page titles, descriptions, and other metadata
- **Asset Detection**: Identify linked CSS, JavaScript, images, and fonts

### 🗄️ Database Features
- **Smart Table Generation**: Create appropriate database tables for each form
- **Field Type Mapping**: Intelligent mapping of HTML input types to SQL data types
- **Constraint Generation**: Apply NOT NULL, UNIQUE, and other constraints based on form validation
- **SQL Export**: Export generated database structure as SQL files
- **Relationship Management**: Handle form relationships and data integrity

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

### Setup Instructions

1. **Clone or Download**
   ```bash
   git clone <repository-url>
   # or download and extract the ZIP file
   ```

2. **Database Configuration**
   - Edit `config/database.php` with your database credentials:
   ```php
   private $host = 'localhost';
   private $db_name = 'webpage_manager';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

3. **Create Database**
   - The application will automatically create the database and tables on first run
   - Or manually create the database: `CREATE DATABASE webpage_manager;`

4. **Set Permissions**
   ```bash
   chmod 755 uploads/
   chmod 755 uploads/pages/
   chmod 755 uploads/assets/
   ```

5. **Web Server Setup**
   - Point your web server document root to the project directory
   - Ensure PHP has write permissions to the `uploads/` directory

6. **Access Application**
   - Open your browser and navigate to your web server URL
   - The application should load with the import interface

## Usage Guide

### 1. Importing Files

**Single Page Import:**
- Click "Import Pages" tab
- Drag and drop or select your HTML file
- Associated CSS, JS, images, and fonts can be uploaded together

**Batch Import:**
- Select multiple files at once
- The system will automatically organize them by type
- HTML files will be analyzed for forms immediately

### 2. Managing Pages

**View Imported Pages:**
- Switch to "Manage Pages" tab
- Browse all imported pages with form counts
- Use search to find specific pages

**Page Details:**
- Click on any page card to view detailed information
- See all detected forms and their fields
- Review form attributes and field properties

### 3. Database Generation

**Generate Structure:**
- Go to "Database" tab
- Click "Generate Database Structure"
- Review the proposed table structures

**Export SQL:**
- Click "Export SQL" to download the database schema
- Import the SQL file into your database system
- Tables will be ready for form data storage

## File Structure

```
webpage-manager/
├── index.html              # Main application interface
├── assets/
│   ├── css/
│   │   └── style.css      # Application styles
│   └── js/
│       └── app.js         # Frontend JavaScript
├── config/
│   └── database.php       # Database configuration
├── includes/
│   ├── upload.php         # File upload handler
│   ├── html_parser.php    # HTML parsing and form analysis
│   ├── get_pages.php      # API: Get all pages
│   ├── get_page_details.php # API: Get page details
│   ├── generate_database.php # Database structure generator
│   ├── get_database_structure.php # API: Get database structure
│   └── export_sql.php     # SQL export functionality
├── uploads/
│   ├── pages/             # Uploaded HTML files
│   └── assets/            # Uploaded CSS, JS, images, fonts
└── README.md
```

## Database Schema

The application uses the following core tables:

- **pages**: Store imported HTML page information
- **forms**: Store detected form structures
- **form_fields**: Store detailed field information
- **associated_files**: Track uploaded assets
- **generated_tables**: Store generated database structures
- **analysis_log**: Log analysis results and errors

## API Endpoints

- `POST /includes/upload.php` - Upload files
- `GET /includes/get_pages.php` - Get all pages
- `GET /includes/get_page_details.php?id={id}` - Get page details
- `POST /includes/generate_database.php` - Generate database structure
- `GET /includes/get_database_structure.php` - Get database structure
- `GET /includes/export_sql.php` - Export SQL file

## Supported File Types

**HTML Files:**
- .html, .htm
- Full HTML5 support
- Automatic form detection

**Stylesheets:**
- .css files
- Linked and embedded styles

**Scripts:**
- .js files
- External JavaScript files

**Images:**
- .jpg, .jpeg, .png, .gif, .svg, .webp
- Automatic asset linking detection

**Fonts:**
- .woff, .woff2, .ttf, .otf
- Web font support

## Form Analysis Features

### Supported Input Types
- Text inputs (text, email, url, tel, search)
- Numeric inputs (number, range)
- Date/time inputs (date, datetime-local, time)
- Selection inputs (radio, checkbox, select)
- File uploads
- Textareas and buttons

### Extracted Attributes
- Field names and IDs
- Validation rules (required, pattern, min/max)
- Placeholder text and default values
- Field constraints and options
- Form actions and methods

## Troubleshooting

### Common Issues

**Upload Errors:**
- Check file permissions on uploads/ directory
- Verify PHP upload limits in php.ini
- Ensure supported file types

**Database Connection:**
- Verify database credentials in config/database.php
- Check MySQL service is running
- Ensure database exists and is accessible

**Form Detection Issues:**
- Verify HTML is well-formed
- Check for JavaScript-generated forms (not supported)
- Review analysis log for error details

### Error Logs
- Check browser console for JavaScript errors
- Review PHP error logs for server-side issues
- Use the analysis_log table for form detection errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source. Please check the license file for details.

## Support

For issues, questions, or contributions, please create an issue in the repository or contact the development team.
