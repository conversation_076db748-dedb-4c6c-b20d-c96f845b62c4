-- Webpage Manager Initial Data
-- Default data for the enhanced Webpage Manager application
-- Version: 2.0.0

USE webpage_manager;

-- Insert default admin user
INSERT IGNORE INTO users (id, username, email, password_hash, full_name, role) 
VALUES (1, 'admin', 'admin@localhost', '', 'System Administrator', 'admin');

-- Insert default project
INSERT IGNORE INTO projects (id, name, description, slug, created_by) 
VALUES (1, 'Default Project', 'Default project for imported pages', 'default', 1);

-- Insert application settings
INSERT IGNORE INTO app_settings (setting_key, setting_value, setting_type, description, is_system) VALUES
('max_file_size', '10485760', 'integer', 'Maximum file upload size in bytes (10MB)', 1),
('allowed_file_types', 'html,htm,css,js,png,jpg,jpeg,gif,svg,woff,woff2,ttf,otf', 'string', 'Allowed file extensions for upload', 1),
('auto_analyze_forms', '1', 'boolean', 'Automatically analyze forms when pages are imported', 1),
('keep_file_backups', '1', 'boolean', 'Keep backup copies of modified files', 1),
('max_backup_versions', '5', 'integer', 'Maximum number of backup versions to keep', 1),
('enable_form_submissions', '0', 'boolean', 'Enable form submission storage', 1),
('cleanup_old_logs', '1', 'boolean', 'Automatically cleanup old log entries', 1),
('log_retention_days', '30', 'integer', 'Number of days to keep log entries', 1),
('app_version', '2.0.0', 'string', 'Application version', 1),
('database_version', '2.0.0', 'string', 'Database schema version', 1),
('upload_directory', 'uploads/', 'string', 'Directory for uploaded files', 1),
('pages_directory', 'uploads/pages/', 'string', 'Directory for uploaded HTML pages', 1),
('assets_directory', 'uploads/assets/', 'string', 'Directory for uploaded assets', 1),
('enable_version_control', '1', 'boolean', 'Enable page version control', 1),
('max_page_versions', '10', 'integer', 'Maximum number of page versions to keep', 1),
('enable_activity_logging', '1', 'boolean', 'Enable activity logging', 1),
('default_form_method', 'POST', 'string', 'Default form method for new forms', 1),
('default_form_enctype', 'application/x-www-form-urlencoded', 'string', 'Default form encoding type', 1),
('enable_form_validation', '1', 'boolean', 'Enable client-side form validation', 1),
('auto_generate_tables', '0', 'boolean', 'Automatically generate database tables from forms', 1);

-- Insert default form templates
INSERT IGNORE INTO form_templates (name, description, category, template_data, is_system) VALUES
('Contact Form', 'Basic contact form with name, email, and message fields', 'contact', 
JSON_OBJECT(
    'fields', JSON_ARRAY(
        JSON_OBJECT('name', 'name', 'type', 'text', 'required', true, 'label', 'Full Name', 'placeholder', 'Enter your full name'),
        JSON_OBJECT('name', 'email', 'type', 'email', 'required', true, 'label', 'Email Address', 'placeholder', '<EMAIL>'),
        JSON_OBJECT('name', 'subject', 'type', 'text', 'required', false, 'label', 'Subject', 'placeholder', 'Message subject'),
        JSON_OBJECT('name', 'message', 'type', 'textarea', 'required', true, 'label', 'Message', 'placeholder', 'Your message here...', 'rows', 5)
    ),
    'attributes', JSON_OBJECT('method', 'POST', 'action', 'contact.php', 'enctype', 'application/x-www-form-urlencoded')
), 1),

('Registration Form', 'User registration form with validation', 'registration',
JSON_OBJECT(
    'fields', JSON_ARRAY(
        JSON_OBJECT('name', 'username', 'type', 'text', 'required', true, 'label', 'Username', 'minlength', 3, 'maxlength', 20),
        JSON_OBJECT('name', 'email', 'type', 'email', 'required', true, 'label', 'Email Address'),
        JSON_OBJECT('name', 'password', 'type', 'password', 'required', true, 'label', 'Password', 'minlength', 8),
        JSON_OBJECT('name', 'confirm_password', 'type', 'password', 'required', true, 'label', 'Confirm Password'),
        JSON_OBJECT('name', 'first_name', 'type', 'text', 'required', true, 'label', 'First Name'),
        JSON_OBJECT('name', 'last_name', 'type', 'text', 'required', true, 'label', 'Last Name'),
        JSON_OBJECT('name', 'terms', 'type', 'checkbox', 'required', true, 'label', 'I agree to the terms and conditions')
    ),
    'attributes', JSON_OBJECT('method', 'POST', 'action', 'register.php', 'enctype', 'application/x-www-form-urlencoded')
), 1),

('Login Form', 'Simple login form', 'authentication',
JSON_OBJECT(
    'fields', JSON_ARRAY(
        JSON_OBJECT('name', 'username', 'type', 'text', 'required', true, 'label', 'Username or Email'),
        JSON_OBJECT('name', 'password', 'type', 'password', 'required', true, 'label', 'Password'),
        JSON_OBJECT('name', 'remember', 'type', 'checkbox', 'required', false, 'label', 'Remember me')
    ),
    'attributes', JSON_OBJECT('method', 'POST', 'action', 'login.php', 'enctype', 'application/x-www-form-urlencoded')
), 1),

('Survey Form', 'Basic survey form with various field types', 'survey',
JSON_OBJECT(
    'fields', JSON_ARRAY(
        JSON_OBJECT('name', 'name', 'type', 'text', 'required', true, 'label', 'Your Name'),
        JSON_OBJECT('name', 'age', 'type', 'number', 'required', false, 'label', 'Age', 'min', 13, 'max', 120),
        JSON_OBJECT('name', 'gender', 'type', 'radio', 'required', false, 'label', 'Gender', 'options', JSON_ARRAY('Male', 'Female', 'Other')),
        JSON_OBJECT('name', 'interests', 'type', 'checkbox', 'required', false, 'label', 'Interests', 'options', JSON_ARRAY('Technology', 'Sports', 'Music', 'Travel', 'Reading')),
        JSON_OBJECT('name', 'rating', 'type', 'range', 'required', false, 'label', 'Overall Rating', 'min', 1, 'max', 10, 'step', 1),
        JSON_OBJECT('name', 'comments', 'type', 'textarea', 'required', false, 'label', 'Additional Comments', 'rows', 4)
    ),
    'attributes', JSON_OBJECT('method', 'POST', 'action', 'survey.php', 'enctype', 'application/x-www-form-urlencoded')
), 1),

('Newsletter Signup', 'Simple newsletter subscription form', 'marketing',
JSON_OBJECT(
    'fields', JSON_ARRAY(
        JSON_OBJECT('name', 'email', 'type', 'email', 'required', true, 'label', 'Email Address', 'placeholder', 'Enter your email'),
        JSON_OBJECT('name', 'name', 'type', 'text', 'required', false, 'label', 'Name (Optional)', 'placeholder', 'Your name'),
        JSON_OBJECT('name', 'frequency', 'type', 'select', 'required', false, 'label', 'Email Frequency', 'options', JSON_ARRAY('Daily', 'Weekly', 'Monthly')),
        JSON_OBJECT('name', 'consent', 'type', 'checkbox', 'required', true, 'label', 'I consent to receive marketing emails')
    ),
    'attributes', JSON_OBJECT('method', 'POST', 'action', 'newsletter.php', 'enctype', 'application/x-www-form-urlencoded')
), 1),

('File Upload Form', 'Form with file upload capabilities', 'upload',
JSON_OBJECT(
    'fields', JSON_ARRAY(
        JSON_OBJECT('name', 'title', 'type', 'text', 'required', true, 'label', 'File Title'),
        JSON_OBJECT('name', 'description', 'type', 'textarea', 'required', false, 'label', 'Description', 'rows', 3),
        JSON_OBJECT('name', 'category', 'type', 'select', 'required', true, 'label', 'Category', 'options', JSON_ARRAY('Document', 'Image', 'Video', 'Other')),
        JSON_OBJECT('name', 'file', 'type', 'file', 'required', true, 'label', 'Select File', 'accept', '.pdf,.doc,.docx,.jpg,.png,.gif'),
        JSON_OBJECT('name', 'public', 'type', 'checkbox', 'required', false, 'label', 'Make this file public')
    ),
    'attributes', JSON_OBJECT('method', 'POST', 'action', 'upload.php', 'enctype', 'multipart/form-data')
), 1);

-- Insert some sample analysis log entries
INSERT INTO analysis_log (page_id, analysis_type, status, message, details) VALUES
(NULL, 'system_startup', 'success', 'Database initialized successfully', JSON_OBJECT('version', '2.0.0', 'tables_created', 14)),
(NULL, 'template_creation', 'success', 'Default form templates created', JSON_OBJECT('templates_count', 6)),
(NULL, 'settings_initialization', 'success', 'Application settings initialized', JSON_OBJECT('settings_count', 20));

-- Create a view for easy form analysis
CREATE OR REPLACE VIEW form_analysis_view AS
SELECT 
    p.id as page_id,
    p.filename,
    p.title as page_title,
    f.id as form_id,
    f.form_name,
    f.form_action,
    f.form_method,
    COUNT(ff.id) as field_count,
    COUNT(CASE WHEN ff.field_required = 1 THEN 1 END) as required_fields,
    GROUP_CONCAT(DISTINCT ff.field_type ORDER BY ff.field_type) as field_types,
    f.created_at as form_created,
    p.created_at as page_created
FROM pages p
LEFT JOIN forms f ON p.id = f.page_id
LEFT JOIN form_fields ff ON f.id = ff.form_id
WHERE p.status = 'active' AND (f.is_active = 1 OR f.is_active IS NULL)
GROUP BY p.id, f.id
ORDER BY p.created_at DESC, f.form_index;

-- Create a view for database table generation status
CREATE OR REPLACE VIEW table_generation_status AS
SELECT 
    f.id as form_id,
    f.form_name,
    p.filename,
    p.title as page_title,
    gt.table_name,
    gt.is_created,
    COUNT(ff.id) as field_count,
    gt.created_at as table_generated,
    gt.updated_at as table_updated
FROM forms f
JOIN pages p ON f.page_id = p.id
LEFT JOIN generated_tables gt ON f.id = gt.form_id
LEFT JOIN form_fields ff ON f.id = ff.form_id
WHERE f.is_active = 1 AND p.status = 'active'
GROUP BY f.id, gt.id
ORDER BY gt.created_at DESC;

-- Insert initial activity log entry
INSERT INTO activity_log (user_id, action, entity_type, entity_id, new_values, ip_address) VALUES
(1, 'database_initialization', 'system', NULL, JSON_OBJECT('version', '2.0.0', 'timestamp', NOW()), '127.0.0.1');
