<?php
/**
 * Get Form Submissions
 * Retrieves form submissions with filtering and pagination
 */

require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    // Check if form_submissions table exists and has correct structure
    // First, check if the table exists
    $sql = "SHOW TABLES LIKE 'form_submissions'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $tableExists = $stmt->fetch();

    if (!$tableExists) {
        // Create table with full structure matching database.php
        $sql = "CREATE TABLE form_submissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_id INT NOT NULL,
            form_id INT NULL,
            share_id INT NULL,
            form_name VARCHAR(255),
            submission_data JSON NOT NULL,
            visitor_session VARCHAR(64),
            ip_address VARCHAR(45),
            user_agent TEXT,
            referrer VARCHAR(500),
            country_code VARCHAR(2),
            city VARCHAR(100),
            browser_name VARCHAR(50),
            browser_version VARCHAR(20),
            os_name VARCHAR(50),
            device_type ENUM('desktop', 'mobile', 'tablet') DEFAULT 'desktop',
            submission_source ENUM('direct', 'shared', 'embedded') DEFAULT 'shared',
            status ENUM('pending', 'processed', 'archived', 'spam', 'reviewed') DEFAULT 'pending',
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            tags JSON,
            notes TEXT,
            processed_at TIMESTAMP NULL,
            processed_by INT NULL,
            response_sent BOOLEAN DEFAULT FALSE,
            response_sent_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_page_id (page_id),
            INDEX idx_form_id (form_id),
            INDEX idx_share_id (share_id),
            INDEX idx_status (status),
            INDEX idx_priority (priority),
            INDEX idx_submission_source (submission_source),
            INDEX idx_created_at (created_at),
            INDEX idx_ip_address (ip_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $db->exec($sql);
    } else {
        // Check if share_id column exists, add if missing
        $sql = "SHOW COLUMNS FROM form_submissions LIKE 'share_id'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columnExists = $stmt->fetch();

        if (!$columnExists) {
            $sql = "ALTER TABLE form_submissions ADD COLUMN share_id INT NULL AFTER form_id";
            $db->exec($sql);
            $sql = "ALTER TABLE form_submissions ADD INDEX idx_share_id (share_id)";
            $db->exec($sql);
        }

        // Check if submitted_at column exists, add if missing
        $sql = "SHOW COLUMNS FROM form_submissions LIKE 'submitted_at'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $submittedAtExists = $stmt->fetch();

        if (!$submittedAtExists) {
            $sql = "ALTER TABLE form_submissions ADD COLUMN submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
            $db->exec($sql);
        }
    }

    // Detect which data column exists
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

    $dataColumn = 'submission_data';
    if (in_array('form_data', $columns) && !in_array('submission_data', $columns)) {
        $dataColumn = 'form_data';
    }

    // Detect which date column exists
    $dateColumn = 'submitted_at';
    if (!in_array('submitted_at', $columns) && in_array('created_at', $columns)) {
        $dateColumn = 'created_at';
    }

    // Get specific submission by ID
    if (isset($_GET['id'])) {
        $submissionId = $_GET['id'];

        // Check if we can safely join with page_shares
        if ($pageSharesExists && $shareIdExists) {
            $sql = "SELECT
                        fs.*,
                        fs.$dataColumn as form_data,
                        p.title as page_title,
                        p.original_filename as page_filename,
                        f.form_name,
                        ps.title as share_title
                    FROM form_submissions fs
                    LEFT JOIN pages p ON fs.page_id = p.id
                    LEFT JOIN forms f ON fs.form_id = f.id
                    LEFT JOIN page_shares ps ON fs.share_id = ps.id
                    WHERE fs.id = ?";
        } else {
            $sql = "SELECT
                        fs.*,
                        fs.$dataColumn as form_data,
                        p.title as page_title,
                        p.original_filename as page_filename,
                        f.form_name,
                        NULL as share_title
                    FROM form_submissions fs
                    LEFT JOIN pages p ON fs.page_id = p.id
                    LEFT JOIN forms f ON fs.form_id = f.id
                    WHERE fs.id = ?";
        }

        $stmt = $db->prepare($sql);
        $stmt->execute([$submissionId]);
        $submission = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($submission) {
            echo json_encode([
                'success' => true,
                'submission' => $submission
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Submission not found'
            ]);
        }
        exit;
    }

    // Build query with filters
    $whereConditions = [];
    $params = [];

    if (isset($_GET['page_id']) && $_GET['page_id']) {
        $whereConditions[] = "fs.page_id = ?";
        $params[] = $_GET['page_id'];
    }

    if (isset($_GET['form_id']) && $_GET['form_id']) {
        $whereConditions[] = "fs.form_id = ?";
        $params[] = $_GET['form_id'];
    }

    if (isset($_GET['date_from']) && $_GET['date_from']) {
        $whereConditions[] = "DATE(fs.$dateColumn) >= ?";
        $params[] = $_GET['date_from'];
    }

    if (isset($_GET['date_to']) && $_GET['date_to']) {
        $whereConditions[] = "DATE(fs.$dateColumn) <= ?";
        $params[] = $_GET['date_to'];
    }

    $whereClause = '';
    if (!empty($whereConditions)) {
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    }

    // Check if page_shares table exists and if share_id column exists
    $sql = "SHOW TABLES LIKE 'page_shares'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pageSharesExists = $stmt->fetch();

    $sql = "SHOW COLUMNS FROM form_submissions LIKE 'share_id'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $shareIdExists = $stmt->fetch();

    // Build query based on available tables and columns
    if ($pageSharesExists && $shareIdExists) {
        $sql = "SELECT
                    fs.*,
                    fs.$dataColumn as form_data,
                    p.title as page_title,
                    p.original_filename as page_filename,
                    f.form_name,
                    ps.title as share_title
                FROM form_submissions fs
                LEFT JOIN pages p ON fs.page_id = p.id
                LEFT JOIN forms f ON fs.form_id = f.id
                LEFT JOIN page_shares ps ON fs.share_id = ps.id
                $whereClause
                ORDER BY fs.$dateColumn DESC
                LIMIT 100";
    } else {
        // Fallback query without page_shares join
        $sql = "SELECT
                    fs.*,
                    fs.$dataColumn as form_data,
                    p.title as page_title,
                    p.original_filename as page_filename,
                    f.form_name,
                    NULL as share_title
                FROM form_submissions fs
                LEFT JOIN pages p ON fs.page_id = p.id
                LEFT JOIN forms f ON fs.form_id = f.id
                $whereClause
                ORDER BY fs.$dateColumn DESC
                LIMIT 100";
    }

    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get pages for filter dropdown
    $sql = "SELECT DISTINCT p.id, p.title, p.original_filename as filename
            FROM pages p
            INNER JOIN form_submissions fs ON p.id = fs.page_id
            ORDER BY p.title, p.original_filename";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get forms for filter dropdown
    $sql = "SELECT DISTINCT f.id, f.form_name
            FROM forms f
            INNER JOIN form_submissions fs ON f.id = fs.form_id
            WHERE f.form_name IS NOT NULL AND f.form_name != ''
            ORDER BY f.form_name";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'submissions' => $submissions,
        'pages' => $pages,
        'forms' => $forms,
        'total' => count($submissions)
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
