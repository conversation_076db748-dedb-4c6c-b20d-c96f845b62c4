<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input {
            width: auto;
        }
        button {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background: #5a67d8;
        }
        .standalone-input {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .standalone-input h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Contact Us</h1>
        
        <!-- Main Contact Form -->
        <form id="contact-form" name="contact_form" method="POST" action="">
            <div class="form-group">
                <label for="name">Full Name *</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone">
            </div>
            
            <div class="form-group">
                <label for="subject">Subject</label>
                <select id="subject" name="subject">
                    <option value="">Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="support">Technical Support</option>
                    <option value="sales">Sales Question</option>
                    <option value="feedback">Feedback</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" placeholder="Please enter your message here..." required></textarea>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="newsletter" name="newsletter" value="yes">
                    <label for="newsletter">Subscribe to our newsletter</label>
                </div>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="terms" name="terms" value="accepted" required>
                    <label for="terms">I agree to the terms and conditions *</label>
                </div>
            </div>
            
            <button type="submit">Send Message</button>
        </form>
        
        <!-- Standalone Newsletter Signup -->
        <div class="standalone-input">
            <h3>Quick Newsletter Signup</h3>
            <div class="form-group">
                <label for="newsletter-email">Email for Newsletter</label>
                <input type="email" id="newsletter-email" name="newsletter_email" placeholder="Enter your email">
            </div>
        </div>
        
        <!-- Standalone Search -->
        <div class="standalone-input">
            <h3>Search Our Site</h3>
            <div class="form-group">
                <label for="search-query">Search</label>
                <input type="search" id="search-query" name="search_query" placeholder="What are you looking for?">
            </div>
        </div>
    </div>
    
    <script>
        // Add some basic form validation
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const message = document.getElementById('message').value.trim();
            const terms = document.getElementById('terms').checked;
            
            if (!name || !email || !message || !terms) {
                e.preventDefault();
                alert('Please fill in all required fields and accept the terms.');
                return false;
            }
            
            // If this is not a shared page, prevent default submission
            if (!document.querySelector('input[name="_share_token"]')) {
                e.preventDefault();
                alert('This is a test form. Upload this page to the system and share it to test form submissions.');
                return false;
            }
        });
    </script>
</body>
</html>
