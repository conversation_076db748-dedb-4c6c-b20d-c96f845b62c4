<?php
/**
 * Detailed Error Check
 * Show exact errors and issues with the enhanced system
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Detailed Error Check</h1>";

try {
    echo "<h2>1. Testing Database Connection</h2>";
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    echo "<p>✅ Database connection successful</p>";
    
    echo "<h2>2. Testing form_submissions Table Structure</h2>";
    
    try {
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>✅ form_submissions table exists</p>";
        echo "<p><strong>Column count:</strong> " . count($columns) . "</p>";
        
        // Check for specific problematic columns
        $columnNames = array_column($columns, 'Field');
        $requiredColumns = ['page_id', 'submission_data', 'ip_address'];
        
        foreach ($requiredColumns as $required) {
            if (in_array($required, $columnNames)) {
                echo "<p>✅ Required column '$required' exists</p>";
            } else {
                echo "<p>❌ Required column '$required' MISSING</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error checking form_submissions: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. Testing Basic Form Submission Insert</h2>";
    
    try {
        // Test basic insertion first
        $sql = "INSERT INTO form_submissions (page_id, submission_data, ip_address, submitted_at) 
                VALUES (?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([1, '{"test": "basic"}', '127.0.0.1']);
        
        $basicId = $db->lastInsertId();
        echo "<p>✅ Basic insertion successful with ID: $basicId</p>";
        
        // Clean up
        $sql = "DELETE FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$basicId]);
        
    } catch (Exception $e) {
        echo "<p>❌ Basic insertion failed: " . $e->getMessage() . "</p>";
        echo "<p><strong>SQL Error:</strong> " . $e->getCode() . "</p>";
    }
    
    echo "<h2>4. Testing Enhanced Columns</h2>";
    
    try {
        // Check which enhanced columns actually exist
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $actualColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
        
        $enhancedColumns = [
            'form_name', 'visitor_session', 'browser_name', 'browser_version',
            'os_name', 'device_type', 'submission_source', 'status', 'priority'
        ];
        
        $existingEnhanced = [];
        $missingEnhanced = [];
        
        foreach ($enhancedColumns as $col) {
            if (in_array($col, $actualColumns)) {
                $existingEnhanced[] = $col;
            } else {
                $missingEnhanced[] = $col;
            }
        }
        
        if (!empty($existingEnhanced)) {
            echo "<p>✅ Enhanced columns present: " . implode(', ', $existingEnhanced) . "</p>";
        }
        
        if (!empty($missingEnhanced)) {
            echo "<p>❌ Enhanced columns missing: " . implode(', ', $missingEnhanced) . "</p>";
        }
        
        // Try enhanced insertion with only existing columns
        if (!empty($existingEnhanced)) {
            $testData = [
                'page_id' => 1,
                'submission_data' => '{"test": "enhanced"}',
                'ip_address' => '127.0.0.1'
            ];
            
            // Add enhanced data for existing columns
            if (in_array('form_name', $existingEnhanced)) {
                $testData['form_name'] = 'test_form';
            }
            if (in_array('browser_name', $existingEnhanced)) {
                $testData['browser_name'] = 'Chrome';
            }
            if (in_array('device_type', $existingEnhanced)) {
                $testData['device_type'] = 'desktop';
            }
            if (in_array('status', $existingEnhanced)) {
                $testData['status'] = 'pending';
            }
            
            $columns = array_keys($testData);
            $placeholders = array_fill(0, count($testData), '?');
            
            $sql = "INSERT INTO form_submissions (" . implode(', ', $columns) . ", submitted_at) 
                    VALUES (" . implode(', ', $placeholders) . ", NOW())";
            
            $stmt = $db->prepare($sql);
            $stmt->execute(array_values($testData));
            
            $enhancedId = $db->lastInsertId();
            echo "<p>✅ Enhanced insertion successful with ID: $enhancedId</p>";
            
            // Show what was inserted
            $sql = "SELECT * FROM form_submissions WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$enhancedId]);
            $inserted = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
            echo "<h4>Inserted Data:</h4>";
            foreach ($inserted as $key => $value) {
                if ($value !== null && $value !== '') {
                    echo "<p><strong>$key:</strong> $value</p>";
                }
            }
            echo "</div>";
            
            // Clean up
            $sql = "DELETE FROM form_submissions WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$enhancedId]);
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Enhanced insertion failed: " . $e->getMessage() . "</p>";
        echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
        echo "<p><strong>Error Info:</strong> " . print_r($stmt->errorInfo(), true) . "</p>";
    }
    
    echo "<h2>5. Testing page_shares Table</h2>";
    
    try {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.is_active = 1 
                LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $share = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($share) {
            echo "<p>✅ page_shares query successful</p>";
            echo "<p><strong>Found share:</strong> {$share['title']}</p>";
        } else {
            echo "<p>⚠️ No active shares found (normal if none created)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ page_shares query failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. Testing Enhanced Submit Form Handler</h2>";
    
    if (file_exists('enhanced_submit_form.php')) {
        echo "<p>✅ enhanced_submit_form.php exists</p>";
        
        // Test if it can be included without errors
        try {
            ob_start();
            $_SERVER['REQUEST_METHOD'] = 'GET'; // Prevent actual processing
            include 'enhanced_submit_form.php';
            $output = ob_get_clean();
            echo "<p>✅ enhanced_submit_form.php loads without syntax errors</p>";
        } catch (Exception $e) {
            echo "<p>❌ enhanced_submit_form.php has errors: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ enhanced_submit_form.php does not exist</p>";
    }
    
    echo "<h2>7. Testing Deployment Config</h2>";
    
    if (file_exists('includes/deployment_config.php')) {
        echo "<p>✅ deployment_config.php exists</p>";
        
        try {
            require_once 'includes/deployment_config.php';
            $config = new DeploymentConfig();
            $baseUrl = $config->getBaseUrl();
            echo "<p>✅ DeploymentConfig class works</p>";
            echo "<p><strong>Base URL:</strong> $baseUrl</p>";
        } catch (Exception $e) {
            echo "<p>❌ DeploymentConfig error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ deployment_config.php does not exist</p>";
    }
    
    echo "<h2>8. Testing ZIP Extractor</h2>";
    
    if (file_exists('includes/enhanced_zip_extractor.php')) {
        echo "<p>✅ enhanced_zip_extractor.php exists</p>";
        
        try {
            require_once 'includes/enhanced_zip_extractor.php';
            $extractor = new EnhancedZipExtractor();
            echo "<p>✅ EnhancedZipExtractor class loads</p>";
        } catch (Exception $e) {
            echo "<p>❌ EnhancedZipExtractor error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ enhanced_zip_extractor.php does not exist</p>";
    }
    
    echo "<h2>9. Overall System Status</h2>";
    
    // Count issues
    $issues = [];
    
    // Check if basic form submission works
    try {
        $sql = "INSERT INTO form_submissions (page_id, submission_data, ip_address, submitted_at) 
                VALUES (?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([1, '{"test": "final"}', '127.0.0.1']);
        $testId = $db->lastInsertId();
        
        $sql = "DELETE FROM form_submissions WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$testId]);
        
        echo "<p>✅ Basic form submission system working</p>";
    } catch (Exception $e) {
        $issues[] = "Basic form submission broken: " . $e->getMessage();
    }
    
    // Check if enhanced columns exist
    $sql = "DESCRIBE form_submissions";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    
    if (!in_array('browser_name', $columns)) {
        $issues[] = "Enhanced columns missing from form_submissions table";
    }
    
    if (!empty($issues)) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Issues Found:</h4>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ All Systems Working!</h4>";
        echo "<p>No critical issues found.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h2>❌ Critical Error</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h2>Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='fix_enhanced_database.php'>Run Database Fix Again</a></li>";
echo "<li><a href='test_form_submission.php'>Test Original Form System</a></li>";
echo "<li><a href='index.html'>Main Application</a></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3, h4 { color: #333; }
</style>
