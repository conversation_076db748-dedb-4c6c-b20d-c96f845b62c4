<?php
/**
 * HTML Parser for Form Analysis
 * Extracts forms and input fields from HTML files
 */

class HTMLParser {
    private $dom;
    private $xpath;

    public function __construct() {
        $this->dom = new DOMDocument();
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
    }

    public function extractForms($filePath) {
        $content = file_get_contents($filePath);
        if (!$content) {
            throw new Exception("Could not read file: $filePath");
        }

        // Load HTML content
        $this->dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $this->xpath = new DOMXPath($this->dom);

        $forms = [];

        // Extract traditional HTML forms
        $formElements = $this->xpath->query('//form');
        foreach ($formElements as $index => $formElement) {
            $forms[] = $this->parseForm($formElement, $index);
        }

        // Extract standalone inputs and custom form implementations
        $standaloneInputs = $this->extractStandaloneInputs();
        if (!empty($standaloneInputs)) {
            $forms[] = [
                'index' => count($forms),
                'action' => '',
                'method' => 'GET',
                'enctype' => 'application/x-www-form-urlencoded',
                'id' => 'standalone_inputs',
                'class' => 'standalone-inputs',
                'name' => 'standalone_inputs',
                'type' => 'standalone',
                'description' => 'Standalone input elements found outside of forms',
                'fields' => $standaloneInputs
            ];
        }

        // Extract custom form implementations (divs with form-like behavior)
        $customForms = $this->extractCustomForms();
        $forms = array_merge($forms, $customForms);

        // Extract interactive elements (buttons, clickable elements)
        $interactiveElements = $this->extractInteractiveElements();
        if (!empty($interactiveElements)) {
            $forms[] = [
                'index' => count($forms),
                'action' => '',
                'method' => 'GET',
                'enctype' => 'application/x-www-form-urlencoded',
                'id' => 'interactive_elements',
                'class' => 'interactive-elements',
                'name' => 'interactive_elements',
                'type' => 'interactive',
                'description' => 'Interactive elements and custom controls',
                'fields' => $interactiveElements
            ];
        }

        return $forms;
    }

    private function parseForm($formElement, $index) {
        $form = [
            'index' => $index,
            'action' => $formElement->getAttribute('action'),
            'method' => strtoupper($formElement->getAttribute('method')) ?: 'GET',
            'enctype' => $formElement->getAttribute('enctype'),
            'id' => $formElement->getAttribute('id'),
            'class' => $formElement->getAttribute('class'),
            'name' => $formElement->getAttribute('name'),
            'fields' => []
        ];

        // Find all input elements within this form
        $inputElements = $this->xpath->query('.//input | .//textarea | .//select | .//button', $formElement);

        foreach ($inputElements as $inputElement) {
            $field = $this->parseField($inputElement);
            if ($field) {
                $form['fields'][] = $field;
            }
        }

        return $form;
    }

    private function parseField($element) {
        $tagName = strtolower($element->tagName);
        $type = strtolower($element->getAttribute('type'));

        // Include all elements, even buttons and submit inputs for comprehensive analysis
        $field = [
            'tag' => $tagName,
            'type' => $type ?: ($tagName === 'textarea' ? 'textarea' : ($tagName === 'select' ? 'select' : ($tagName === 'button' ? 'button' : 'text'))),
            'name' => $element->getAttribute('name'),
            'id' => $element->getAttribute('id'),
            'class' => $element->getAttribute('class'),
            'placeholder' => $element->getAttribute('placeholder'),
            'value' => $element->getAttribute('value'),
            'title' => $element->getAttribute('title'),
            'required' => $element->hasAttribute('required'),
            'readonly' => $element->hasAttribute('readonly'),
            'disabled' => $element->hasAttribute('disabled'),
            'multiple' => $element->hasAttribute('multiple'),
            'autofocus' => $element->hasAttribute('autofocus'),
            'autocomplete' => $element->getAttribute('autocomplete'),
            'form' => $element->getAttribute('form'), // Can reference form by ID
            'formaction' => $element->getAttribute('formaction'),
            'formmethod' => $element->getAttribute('formmethod'),
            'formenctype' => $element->getAttribute('formenctype'),
            'formnovalidate' => $element->hasAttribute('formnovalidate'),
            'formtarget' => $element->getAttribute('formtarget')
        ];

        // Add label information
        $field['label'] = $this->findLabelForInput($element);

        // Add ARIA attributes for accessibility
        $field['aria_label'] = $element->getAttribute('aria-label');
        $field['aria_describedby'] = $element->getAttribute('aria-describedby');
        $field['aria_required'] = $element->getAttribute('aria-required');
        $field['aria_invalid'] = $element->getAttribute('aria-invalid');
        $field['role'] = $element->getAttribute('role');

        // Add data attributes
        $field['data_attributes'] = $this->extractDataAttributes($element);

        // Add type-specific attributes
        switch ($tagName) {
            case 'input':
                $this->parseInputAttributes($element, $field);
                break;
            case 'textarea':
                $this->parseTextareaAttributes($element, $field);
                break;
            case 'select':
                $this->parseSelectAttributes($element, $field);
                break;
            case 'button':
                $this->parseButtonAttributes($element, $field);
                break;
        }

        return $field;
    }

    private function parseInputAttributes($element, &$field) {
        $type = $field['type'];

        // Common attributes for various input types
        $attributes = [
            'min', 'max', 'step', 'pattern', 'maxlength', 'minlength',
            'size', 'accept', 'autocomplete', 'list', 'dirname'
        ];

        foreach ($attributes as $attr) {
            $value = $element->getAttribute($attr);
            if ($value !== '') {
                $field[$attr] = $value;
            }
        }

        // Handle all HTML5 input types
        switch ($type) {
            case 'text':
            case 'search':
                $field['spellcheck'] = $element->getAttribute('spellcheck');
                break;

            case 'password':
                // No special attributes, but note security implications
                $field['security_note'] = 'Password field - will be hashed if stored';
                break;

            case 'email':
                $field['multiple'] = $element->hasAttribute('multiple');
                break;

            case 'url':
            case 'tel':
                // Pattern validation often used
                break;

            case 'number':
            case 'range':
                $field['value_as_number'] = $element->getAttribute('value');
                break;

            case 'date':
            case 'datetime-local':
            case 'month':
            case 'week':
            case 'time':
                $field['value_as_date'] = $element->getAttribute('value');
                break;

            case 'color':
                $field['value'] = $element->getAttribute('value') ?: '#000000';
                break;

            case 'radio':
            case 'checkbox':
                $field['checked'] = $element->hasAttribute('checked');
                $field['default_checked'] = $element->hasAttribute('checked');
                // Find related options (same name)
                if ($field['name']) {
                    $field['options'] = $this->findRelatedOptions($field['name'], $type);
                }
                break;

            case 'file':
                $field['accept'] = $element->getAttribute('accept');
                $field['capture'] = $element->getAttribute('capture');
                $field['webkitdirectory'] = $element->hasAttribute('webkitdirectory');
                break;

            case 'hidden':
                // Hidden fields might contain important data
                $field['purpose'] = 'hidden_data';
                break;

            case 'submit':
            case 'button':
            case 'reset':
                $field['form_action'] = $element->getAttribute('formaction');
                $field['form_method'] = $element->getAttribute('formmethod');
                $field['form_enctype'] = $element->getAttribute('formenctype');
                $field['form_novalidate'] = $element->hasAttribute('formnovalidate');
                $field['form_target'] = $element->getAttribute('formtarget');
                break;

            case 'image':
                $field['src'] = $element->getAttribute('src');
                $field['alt'] = $element->getAttribute('alt');
                $field['width'] = $element->getAttribute('width');
                $field['height'] = $element->getAttribute('height');
                break;
        }

        // Check for datalist association
        if ($field['list']) {
            $field['datalist_options'] = $this->extractDatalistOptions($field['list']);
        }

        // Check for validation attributes
        $field['validation'] = [
            'required' => $field['required'],
            'pattern' => $field['pattern'] ?? null,
            'min' => $field['min'] ?? null,
            'max' => $field['max'] ?? null,
            'minlength' => $field['minlength'] ?? null,
            'maxlength' => $field['maxlength'] ?? null,
            'step' => $field['step'] ?? null
        ];
    }

    private function parseTextareaAttributes($element, &$field) {
        $field['rows'] = $element->getAttribute('rows') ?: null;
        $field['cols'] = $element->getAttribute('cols') ?: null;
        $field['maxlength'] = $element->getAttribute('maxlength') ?: null;
        $field['minlength'] = $element->getAttribute('minlength') ?: null;
        $field['wrap'] = $element->getAttribute('wrap') ?: null;
        
        // Get textarea content
        if ($element->textContent) {
            $field['value'] = trim($element->textContent);
        }
    }

    private function parseSelectAttributes($element, &$field) {
        $field['size'] = $element->getAttribute('size') ?: null;
        
        // Extract options
        $options = [];
        $optionElements = $this->xpath->query('.//option', $element);
        
        foreach ($optionElements as $option) {
            $optionData = [
                'value' => $option->getAttribute('value'),
                'text' => trim($option->textContent),
                'selected' => $option->hasAttribute('selected'),
                'disabled' => $option->hasAttribute('disabled')
            ];
            $options[] = $optionData;
        }
        
        // Extract optgroups
        $optgroupElements = $this->xpath->query('.//optgroup', $element);
        foreach ($optgroupElements as $optgroup) {
            $groupOptions = [];
            $groupOptionElements = $this->xpath->query('.//option', $optgroup);
            
            foreach ($groupOptionElements as $option) {
                $groupOptions[] = [
                    'value' => $option->getAttribute('value'),
                    'text' => trim($option->textContent),
                    'selected' => $option->hasAttribute('selected'),
                    'disabled' => $option->hasAttribute('disabled')
                ];
            }
            
            $options[] = [
                'optgroup' => $optgroup->getAttribute('label'),
                'options' => $groupOptions
            ];
        }
        
        $field['options'] = $options;
    }

    private function findRelatedOptions($name, $type) {
        $options = [];
        $elements = $this->xpath->query("//input[@name='$name' and @type='$type']");
        
        foreach ($elements as $element) {
            $options[] = [
                'value' => $element->getAttribute('value'),
                'id' => $element->getAttribute('id'),
                'checked' => $element->hasAttribute('checked'),
                'label' => $this->findLabelForInput($element)
            ];
        }
        
        return $options;
    }

    private function findLabelForInput($inputElement) {
        $inputId = $inputElement->getAttribute('id');
        
        // Try to find label by 'for' attribute
        if ($inputId) {
            $labels = $this->xpath->query("//label[@for='$inputId']");
            if ($labels->length > 0) {
                return trim($labels->item(0)->textContent);
            }
        }
        
        // Try to find parent label
        $parentLabel = $this->xpath->query('ancestor::label', $inputElement);
        if ($parentLabel->length > 0) {
            return trim($parentLabel->item(0)->textContent);
        }
        
        // Try to find adjacent text or label
        $previousSibling = $inputElement->previousSibling;
        while ($previousSibling) {
            if ($previousSibling->nodeType === XML_TEXT_NODE) {
                $text = trim($previousSibling->textContent);
                if ($text) {
                    return $text;
                }
            } elseif ($previousSibling->nodeType === XML_ELEMENT_NODE) {
                if ($previousSibling->tagName === 'label') {
                    return trim($previousSibling->textContent);
                }
                break;
            }
            $previousSibling = $previousSibling->previousSibling;
        }
        
        return null;
    }

    private function extractStandaloneInputs() {
        $standaloneInputs = [];

        // Find all input elements that are NOT inside a form
        $allInputs = $this->xpath->query('//input[not(ancestor::form)] | //textarea[not(ancestor::form)] | //select[not(ancestor::form)]');

        foreach ($allInputs as $inputElement) {
            $field = $this->parseField($inputElement);
            if ($field) {
                $field['context'] = 'standalone';
                $field['parent_element'] = $this->getParentContext($inputElement);
                $standaloneInputs[] = $field;
            }
        }

        return $standaloneInputs;
    }

    private function extractCustomForms() {
        $customForms = [];

        // Look for divs with form-like classes or data attributes
        $formLikeElements = $this->xpath->query('//div[contains(@class, "form") or contains(@class, "contact") or contains(@class, "signup") or contains(@class, "login") or contains(@class, "register") or contains(@class, "search") or @data-form or @data-contact or @role="form"]');

        foreach ($formLikeElements as $index => $element) {
            // Check if this div contains input elements
            $inputs = $this->xpath->query('.//input | .//textarea | .//select | .//button', $element);

            if ($inputs->length > 0) {
                $customForm = [
                    'index' => $index,
                    'action' => $element->getAttribute('data-action') ?: '',
                    'method' => strtoupper($element->getAttribute('data-method')) ?: 'POST',
                    'enctype' => $element->getAttribute('data-enctype') ?: 'application/x-www-form-urlencoded',
                    'id' => $element->getAttribute('id') ?: 'custom_form_' . $index,
                    'class' => $element->getAttribute('class'),
                    'name' => $element->getAttribute('data-name') ?: 'custom_form_' . $index,
                    'type' => 'custom',
                    'description' => 'Custom form implementation detected',
                    'fields' => []
                ];

                foreach ($inputs as $inputElement) {
                    $field = $this->parseField($inputElement);
                    if ($field) {
                        $field['context'] = 'custom_form';
                        $field['parent_element'] = $this->getParentContext($inputElement);
                        $customForm['fields'][] = $field;
                    }
                }

                if (!empty($customForm['fields'])) {
                    $customForms[] = $customForm;
                }
            }
        }

        return $customForms;
    }

    private function extractInteractiveElements() {
        $interactiveElements = [];

        // Find buttons outside of forms
        $buttons = $this->xpath->query('//button[not(ancestor::form)] | //input[@type="button" or @type="submit" or @type="reset"][not(ancestor::form)]');

        foreach ($buttons as $button) {
            $field = $this->parseField($button);
            if ($field) {
                $field['context'] = 'interactive';
                $field['parent_element'] = $this->getParentContext($button);
                $field['onclick'] = $button->getAttribute('onclick');
                $field['data_attributes'] = $this->extractDataAttributes($button);
                $interactiveElements[] = $field;
            }
        }

        // Find elements with click handlers or form-related data attributes
        $clickableElements = $this->xpath->query('//*[@onclick or @data-submit or @data-form or @data-action or contains(@class, "btn") or contains(@class, "button") or contains(@class, "submit")][not(self::form) and not(self::input) and not(self::button) and not(self::textarea) and not(self::select)]');

        foreach ($clickableElements as $element) {
            $field = [
                'tag' => strtolower($element->tagName),
                'type' => 'clickable',
                'name' => $element->getAttribute('data-name') ?: $element->getAttribute('id'),
                'id' => $element->getAttribute('id'),
                'class' => $element->getAttribute('class'),
                'text' => trim($element->textContent),
                'context' => 'interactive',
                'parent_element' => $this->getParentContext($element),
                'onclick' => $element->getAttribute('onclick'),
                'data_attributes' => $this->extractDataAttributes($element),
                'role' => $element->getAttribute('role'),
                'aria_label' => $element->getAttribute('aria-label')
            ];

            $interactiveElements[] = $field;
        }

        // Find elements with contenteditable attribute
        $editableElements = $this->xpath->query('//*[@contenteditable="true"]');

        foreach ($editableElements as $element) {
            $field = [
                'tag' => strtolower($element->tagName),
                'type' => 'contenteditable',
                'name' => $element->getAttribute('data-name') ?: $element->getAttribute('id'),
                'id' => $element->getAttribute('id'),
                'class' => $element->getAttribute('class'),
                'value' => trim($element->textContent),
                'context' => 'editable',
                'parent_element' => $this->getParentContext($element),
                'data_attributes' => $this->extractDataAttributes($element),
                'placeholder' => $element->getAttribute('data-placeholder'),
                'maxlength' => $element->getAttribute('data-maxlength')
            ];

            $interactiveElements[] = $field;
        }

        return $interactiveElements;
    }

    private function getParentContext($element) {
        $parent = $element->parentNode;
        $context = [];

        while ($parent && $parent->nodeType === XML_ELEMENT_NODE) {
            $context[] = [
                'tag' => strtolower($parent->tagName),
                'id' => $parent->getAttribute('id'),
                'class' => $parent->getAttribute('class')
            ];

            // Stop at certain container elements
            if (in_array(strtolower($parent->tagName), ['body', 'main', 'section', 'article', 'div'])) {
                break;
            }

            $parent = $parent->parentNode;
        }

        return $context;
    }

    private function extractDataAttributes($element) {
        $dataAttributes = [];

        if ($element->hasAttributes()) {
            foreach ($element->attributes as $attribute) {
                if (strpos($attribute->name, 'data-') === 0) {
                    $dataAttributes[$attribute->name] = $attribute->value;
                }
            }
        }

        return $dataAttributes;
    }



    private function extractDatalistOptions($listId) {
        $options = [];
        $datalist = $this->xpath->query("//datalist[@id='$listId']");

        if ($datalist->length > 0) {
            $optionElements = $this->xpath->query('.//option', $datalist->item(0));
            foreach ($optionElements as $option) {
                $options[] = [
                    'value' => $option->getAttribute('value'),
                    'label' => $option->getAttribute('label') ?: $option->textContent,
                    'disabled' => $option->hasAttribute('disabled')
                ];
            }
        }

        return $options;
    }

    private function parseButtonAttributes($element, &$field) {
        $field['button_type'] = $element->getAttribute('type') ?: 'button';
        $field['text'] = trim($element->textContent);
        $field['onclick'] = $element->getAttribute('onclick');

        // Form-related attributes for buttons
        $field['form'] = $element->getAttribute('form');
        $field['formaction'] = $element->getAttribute('formaction');
        $field['formmethod'] = $element->getAttribute('formmethod');
        $field['formenctype'] = $element->getAttribute('formenctype');
        $field['formnovalidate'] = $element->hasAttribute('formnovalidate');
        $field['formtarget'] = $element->getAttribute('formtarget');

        // Check if button contains images or icons
        $images = $this->xpath->query('.//img', $element);
        if ($images->length > 0) {
            $field['contains_image'] = true;
            $field['image_src'] = $images->item(0)->getAttribute('src');
            $field['image_alt'] = $images->item(0)->getAttribute('alt');
        }

        // Check for icon classes (common patterns)
        $class = $field['class'];
        if ($class && (strpos($class, 'icon') !== false || strpos($class, 'fa-') !== false || strpos($class, 'glyphicon') !== false)) {
            $field['has_icon'] = true;
            $field['icon_classes'] = $class;
        }
    }

    public function extractMetadata($filePath) {
        $content = file_get_contents($filePath);
        if (!$content) {
            throw new Exception("Could not read file: $filePath");
        }

        $this->dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $this->xpath = new DOMXPath($this->dom);

        $metadata = [
            'title' => $this->extractTitle(),
            'description' => $this->extractMetaDescription(),
            'keywords' => $this->extractMetaKeywords(),
            'charset' => $this->extractCharset(),
            'language' => $this->extractLanguage(),
            'stylesheets' => $this->extractStylesheets(),
            'scripts' => $this->extractScripts(),
            'images' => $this->extractImages(),
            'links' => $this->extractLinks()
        ];

        return $metadata;
    }

    private function extractTitle() {
        $titleElements = $this->xpath->query('//title');
        return $titleElements->length > 0 ? trim($titleElements->item(0)->textContent) : null;
    }

    private function extractMetaDescription() {
        $metaElements = $this->xpath->query("//meta[@name='description']");
        return $metaElements->length > 0 ? $metaElements->item(0)->getAttribute('content') : null;
    }

    private function extractMetaKeywords() {
        $metaElements = $this->xpath->query("//meta[@name='keywords']");
        return $metaElements->length > 0 ? $metaElements->item(0)->getAttribute('content') : null;
    }

    private function extractCharset() {
        $metaElements = $this->xpath->query("//meta[@charset]");
        if ($metaElements->length > 0) {
            return $metaElements->item(0)->getAttribute('charset');
        }
        
        $metaElements = $this->xpath->query("//meta[@http-equiv='Content-Type']");
        if ($metaElements->length > 0) {
            $content = $metaElements->item(0)->getAttribute('content');
            if (preg_match('/charset=([^;]+)/i', $content, $matches)) {
                return trim($matches[1]);
            }
        }
        
        return null;
    }

    private function extractLanguage() {
        $htmlElements = $this->xpath->query('//html[@lang]');
        return $htmlElements->length > 0 ? $htmlElements->item(0)->getAttribute('lang') : null;
    }

    private function extractStylesheets() {
        $stylesheets = [];
        $linkElements = $this->xpath->query("//link[@rel='stylesheet']");
        
        foreach ($linkElements as $link) {
            $stylesheets[] = [
                'href' => $link->getAttribute('href'),
                'media' => $link->getAttribute('media'),
                'type' => $link->getAttribute('type')
            ];
        }
        
        return $stylesheets;
    }

    private function extractScripts() {
        $scripts = [];
        $scriptElements = $this->xpath->query('//script[@src]');
        
        foreach ($scriptElements as $script) {
            $scripts[] = [
                'src' => $script->getAttribute('src'),
                'type' => $script->getAttribute('type'),
                'async' => $script->hasAttribute('async'),
                'defer' => $script->hasAttribute('defer')
            ];
        }
        
        return $scripts;
    }

    private function extractImages() {
        $images = [];
        $imgElements = $this->xpath->query('//img[@src]');
        
        foreach ($imgElements as $img) {
            $images[] = [
                'src' => $img->getAttribute('src'),
                'alt' => $img->getAttribute('alt'),
                'width' => $img->getAttribute('width'),
                'height' => $img->getAttribute('height')
            ];
        }
        
        return $images;
    }

    private function extractLinks() {
        $links = [];
        $linkElements = $this->xpath->query('//a[@href]');
        
        foreach ($linkElements as $link) {
            $links[] = [
                'href' => $link->getAttribute('href'),
                'text' => trim($link->textContent),
                'title' => $link->getAttribute('title'),
                'target' => $link->getAttribute('target')
            ];
        }
        
        return $links;
    }

    public function generateDatabaseFieldType($fieldType, $attributes = []) {
        switch (strtolower($fieldType)) {
            case 'email':
                return 'VARCHAR(255)';
            case 'url':
                return 'VARCHAR(500)';
            case 'tel':
            case 'phone':
                return 'VARCHAR(20)';
            case 'number':
            case 'range':
                return isset($attributes['step']) && strpos($attributes['step'], '.') !== false ? 'DECIMAL(10,2)' : 'INT';
            case 'date':
                return 'DATE';
            case 'datetime':
            case 'datetime-local':
                return 'DATETIME';
            case 'time':
                return 'TIME';
            case 'checkbox':
                return 'BOOLEAN';
            case 'textarea':
                return 'TEXT';
            case 'password':
                return 'VARCHAR(255)'; // Will be hashed
            case 'file':
                return 'VARCHAR(500)'; // File path
            case 'hidden':
            case 'text':
            case 'search':
            default:
                $maxLength = isset($attributes['maxlength']) ? (int)$attributes['maxlength'] : 255;
                return $maxLength > 255 ? 'TEXT' : "VARCHAR($maxLength)";
        }
    }
}
?>
